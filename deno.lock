{"version": "4", "redirects": {"https://esm.sh/@ai-sdk/openai@latest": "https://esm.sh/@ai-sdk/openai@1.3.22", "https://esm.sh/@ai-sdk/ui-utils@^1.1.19?target=denonext": "https://esm.sh/@ai-sdk/ui-utils@1.2.1?target=denonext", "https://esm.sh/@n8n_io/riot-tmpl@^4.0.1?target=denonext": "https://esm.sh/@n8n_io/riot-tmpl@4.0.1?target=denonext", "https://esm.sh/@supabase/functions-js@^2.1.5?target=denonext": "https://esm.sh/@supabase/functions-js@2.4.4?target=denonext", "https://esm.sh/@supabase/gotrue-js@^2.56.0?target=denonext": "https://esm.sh/@supabase/gotrue-js@2.69.1?target=denonext", "https://esm.sh/@supabase/node-fetch@^2.6.14?target=denonext": "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext", "https://esm.sh/@supabase/postgrest-js@^1.8.5?target=denonext": "https://esm.sh/@supabase/postgrest-js@1.19.4?target=denonext", "https://esm.sh/@supabase/realtime-js@^2.8.4?target=denonext": "https://esm.sh/@supabase/realtime-js@2.11.7?target=denonext", "https://esm.sh/@supabase/storage-js@^2.5.4?target=denonext": "https://esm.sh/@supabase/storage-js@2.7.2?target=denonext", "https://esm.sh/@types/combined-stream@~1.0.6/index.d.ts": "https://esm.sh/@types/combined-stream@1.0.6/index.d.ts", "https://esm.sh/@types/debug@~4.1.12/index.d.ts": "https://esm.sh/@types/debug@4.1.12/index.d.ts", "https://esm.sh/@types/esprima@~4.0.6/index.d.ts": "https://esm.sh/@types/esprima@4.0.6/index.d.ts", "https://esm.sh/@types/follow-redirects@~1.14.4/index.d.ts": "https://esm.sh/@types/follow-redirects@1.14.4/index.d.ts", "https://esm.sh/@types/lodash@~4.17.16/index.d.ts": "https://esm.sh/@types/lodash@4.17.16/index.d.ts", "https://esm.sh/@types/mime-types@~2.1.4/index.d.ts": "https://esm.sh/@types/mime-types@2.1.4/index.d.ts", "https://esm.sh/@types/proxy-from-env@~1.0.4/index.d.ts": "https://esm.sh/@types/proxy-from-env@1.0.4/index.d.ts", "https://esm.sh/@types/ws@~8.5.14/index.d.mts": "https://esm.sh/@types/ws@8.5.14/index.d.mts", "https://esm.sh/ai@latest": "https://esm.sh/ai@4.3.15", "https://esm.sh/ast-types@^0.16.1?target=denonext": "https://esm.sh/ast-types@0.16.1?target=denonext", "https://esm.sh/asynckit@^0.4.0?target=denonext": "https://esm.sh/asynckit@0.4.0?target=denonext", "https://esm.sh/axios@^1.7.9?target=denonext": "https://esm.sh/axios@1.8.2?target=denonext", "https://esm.sh/bufferutil@^4.0.1?target=denonext": "https://esm.sh/bufferutil@4.0.9?target=denonext", "https://esm.sh/combined-stream@^1.0.8?target=denonext": "https://esm.sh/combined-stream@1.0.8?target=denonext", "https://esm.sh/debug?target=denonext": "https://esm.sh/debug@4.4.0?target=denonext", "https://esm.sh/delayed-stream@~1.0.0?target=denonext": "https://esm.sh/delayed-stream@1.0.0?target=denonext", "https://esm.sh/esprima-next@^5.8.4?target=denonext": "https://esm.sh/esprima-next@5.8.4?target=denonext", "https://esm.sh/esprima@~4.0.0?target=denonext": "https://esm.sh/esprima@4.0.1?target=denonext", "https://esm.sh/follow-redirects@^1.15.6?target=denonext": "https://esm.sh/follow-redirects@1.15.9?target=denonext", "https://esm.sh/form-data@^4.0.0?target=denonext": "https://esm.sh/form-data@4.0.2?target=denonext", "https://esm.sh/mime-types@^2.1.12?target=denonext": "https://esm.sh/mime-types@2.1.35?target=denonext", "https://esm.sh/ms@^2.1.3?target=denonext": "https://esm.sh/ms@2.1.3?target=denonext", "https://esm.sh/nanoid@^3.3.8/non-secure?target=denonext": "https://esm.sh/nanoid@3.3.11/non-secure?target=denonext", "https://esm.sh/node-gyp-build@^4.3.0?target=denonext": "https://esm.sh/node-gyp-build@4.8.4?target=denonext", "https://esm.sh/proxy-from-env@^1.1.0?target=denonext": "https://esm.sh/proxy-from-env@1.1.0?target=denonext", "https://esm.sh/recast@^0.22.0/lib/util?target=denonext": "https://esm.sh/recast@0.22.0/lib/util?target=denonext", "https://esm.sh/recast@^0.22.0?target=denonext": "https://esm.sh/recast@0.22.0?target=denonext", "https://esm.sh/secure-json-parse@^2.7.0?target=denonext": "https://esm.sh/secure-json-parse@2.7.0?target=denonext", "https://esm.sh/source-map@~0.6.1?target=denonext": "https://esm.sh/source-map@0.6.1?target=denonext", "https://esm.sh/supports-color?target=denonext": "https://esm.sh/supports-color@10.0.0?target=denonext", "https://esm.sh/tr46@~0.0.3?target=denonext": "https://esm.sh/tr46@0.0.3?target=denonext", "https://esm.sh/tslib@^2.0.1?target=denonext": "https://esm.sh/tslib@2.8.1?target=denonext", "https://esm.sh/utf-8-validate@%3E=5.0.2?target=denonext": "https://esm.sh/utf-8-validate@6.0.5?target=denonext", "https://esm.sh/webidl-conversions@^3.0.0?target=denonext": "https://esm.sh/webidl-conversions@3.0.1?target=denonext", "https://esm.sh/whatwg-url@^5.0.0?target=denonext": "https://esm.sh/whatwg-url@5.0.0?target=denonext", "https://esm.sh/ws@^8.14.2?target=denonext": "https://esm.sh/ws@8.18.1?target=denonext", "https://esm.sh/ws@^8.18.0?target=denonext": "https://esm.sh/ws@8.18.1?target=denonext", "https://esm.sh/zod-to-json-schema@^3.24.1?target=denonext": "https://esm.sh/zod-to-json-schema@3.24.5?target=denonext", "https://esm.sh/zod-to-json-schema@^3.24.3?target=denonext": "https://esm.sh/zod-to-json-schema@3.24.5?target=denonext", "https://esm.sh/zod@^3.0.0?target=denonext": "https://esm.sh/zod@3.24.2?target=denonext", "https://esm.sh/zod@^3.23.8?target=denonext": "https://esm.sh/zod@3.24.2?target=denonext", "https://esm.sh/zod@^3.24.1?target=denonext": "https://esm.sh/zod@3.24.2?target=denonext", "https://esm.sh/zod@^3.24.2?target=denonext": "https://esm.sh/zod@3.24.2?target=denonext", "https://esm.sh/zod@latest": "https://esm.sh/zod@3.24.4"}, "remote": {"https://deno.land/std@0.150.0/media_types/_util.ts": "ce9b4fc4ba1c447dafab619055e20fd88236ca6bdd7834a21f98bd193c3fbfa1", "https://deno.land/std@0.150.0/media_types/mod.ts": "2d4b6f32a087029272dc59e0a55ae3cc4d1b27b794ccf528e94b1925795b3118", "https://deno.land/std@0.150.0/media_types/vendor/mime-db.v1.52.0.ts": "724cee25fa40f1a52d3937d6b4fbbfdd7791ff55e1b7ac08d9319d5632c7f5af", "https://deno.land/std@0.177.0/async/abortable.ts": "73acfb3ed7261ce0d930dbe89e43db8d34e017b063cf0eaa7d215477bf53442e", "https://deno.land/std@0.177.0/async/deadline.ts": "c5facb0b404eede83e38bd2717ea8ab34faa2ffb20ef87fd261fcba32ba307aa", "https://deno.land/std@0.177.0/async/debounce.ts": "adab11d04ca38d699444ac8a9d9856b4155e8dda2afd07ce78276c01ea5a4332", "https://deno.land/std@0.177.0/async/deferred.ts": "42790112f36a75a57db4a96d33974a936deb7b04d25c6084a9fa8a49f135def8", "https://deno.land/std@0.177.0/async/delay.ts": "73aa04cec034c84fc748c7be49bb15cac3dd43a57174bfdb7a4aec22c248f0dd", "https://deno.land/std@0.177.0/async/mod.ts": "f04344fa21738e5ad6bea37a6bfffd57c617c2d372bb9f9dcfd118a1b622e576", "https://deno.land/std@0.177.0/async/mux_async_iterator.ts": "70c7f2ee4e9466161350473ad61cac0b9f115cff4c552eaa7ef9d50c4cbb4cc9", "https://deno.land/std@0.177.0/async/pool.ts": "fd082bd4aaf26445909889435a5c74334c017847842ec035739b4ae637ae8260", "https://deno.land/std@0.177.0/async/retry.ts": "5efa3ba450ac0c07a40a82e2df296287b5013755d232049efd7ea2244f15b20f", "https://deno.land/std@0.177.0/async/tee.ts": "47e42d35f622650b02234d43803d0383a89eb4387e1b83b5a40106d18ae36757", "https://deno.land/std@0.177.0/collections/filter_values.ts": "5b9feaf17b9a6e5ffccdd36cf6f38fa4ffa94cff2602d381c2ad0c2a97929652", "https://deno.land/std@0.177.0/collections/without_all.ts": "a89f5da0b5830defed4f59666e188df411d8fece35a5f6ca69be6ca71a95c185", "https://deno.land/std@0.177.0/dotenv/mod.ts": "8dcbc8a40b896a0bf094582aaeadbfc76d3528872faf2efc0302beb1d2f6afd0", "https://deno.land/std@0.177.0/encoding/base64.ts": "7de04c2f8aeeb41453b09b186480be90f2ff357613b988e99fabb91d2eeceba1", "https://deno.land/std@0.177.0/encoding/base64url.ts": "3f1178f6446834457b16bfde8b559c1cd3481727fe384d3385e4a9995dc2d851", "https://deno.land/std@0.177.0/fmt/colors.ts": "938c5d44d889fb82eff6c358bea8baa7e85950a16c9f6dae3ec3a7a729164471", "https://deno.land/std@0.177.0/http/server.ts": "cbb17b594651215ba95c01a395700684e569c165a567e4e04bba327f41197433", "https://deno.land/std@0.177.0/testing/_diff.ts": "1a3c044aedf77647d6cac86b798c6417603361b66b54c53331b312caeb447aea", "https://deno.land/std@0.177.0/testing/_format.ts": "a69126e8a469009adf4cf2a50af889aca364c349797e63174884a52ff75cf4c7", "https://deno.land/std@0.177.0/testing/asserts.ts": "984ab0bfb3faeed92ffaa3a6b06536c66811185328c5dd146257c702c41b01ab", "https://deno.land/std@0.192.0/_util/asserts.ts": "178dfc49a464aee693a7e285567b3d0b555dc805ff490505a8aae34f9cfb1462", "https://deno.land/std@0.192.0/bytes/concat.ts": "d26d6f3d7922e6d663dacfcd357563b7bf4a380ce5b9c2bbe0c8586662f25ce2", "https://deno.land/std@0.192.0/crypto/_fnv/fnv32.ts": "e4649dfdefc5c987ed53c3c25db62db771a06d9d1b9c36d2b5cf0853b8e82153", "https://deno.land/std@0.192.0/crypto/_fnv/fnv64.ts": "bfa0e4702061fdb490a14e6bf5f9168a22fb022b307c5723499469bfefca555e", "https://deno.land/std@0.192.0/crypto/_fnv/mod.ts": "f956a95f58910f223e420340b7404702ecd429603acd4491fa77af84f746040c", "https://deno.land/std@0.192.0/crypto/_fnv/util.ts": "accba12bfd80a352e32a872f87df2a195e75561f1b1304a4cb4f5a4648d288f9", "https://deno.land/std@0.192.0/crypto/_wasm/lib/deno_std_wasm_crypto.generated.mjs": "85b50eee2e511584698c04f1d84155e57452ea963106fee64987c326e9e5d25d", "https://deno.land/std@0.192.0/crypto/_wasm/mod.ts": "973058e70052c98292b567d1c8396dffc28d6dfc6a44f0763032f6fbdf5222f5", "https://deno.land/std@0.192.0/crypto/crypto.ts": "4cae012d2022bc1470f548354a2e1a998f751eaddab6ab83dfb995948ba0b89d", "https://deno.land/std@0.192.0/crypto/timing_safe_equal.ts": "0fae34ee02264f309ae0b6e54e9746a7aba3996e5454903ed106967a7a9ef665", "https://deno.land/std@0.192.0/fmt/colors.ts": "d67e3cd9f472535241a8e410d33423980bec45047e343577554d3356e1f0ef4e", "https://deno.land/std@0.192.0/testing/_diff.ts": "1a3c044aedf77647d6cac86b798c6417603361b66b54c53331b312caeb447aea", "https://deno.land/std@0.192.0/testing/_format.ts": "a69126e8a469009adf4cf2a50af889aca364c349797e63174884a52ff75cf4c7", "https://deno.land/std@0.192.0/testing/asserts.ts": "e16d98b4d73ffc4ed498d717307a12500ae4f2cbe668f1a215632d19fcffc22f", "https://deno.land/std@0.192.0/uuid/_common.ts": "cb1441f4df460571fc0919e1c5c217f3e7006189b703caf946604b3f791ae34d", "https://deno.land/std@0.192.0/uuid/constants.ts": "0d0e95561343da44adb4a4edbc1f04cef48b0d75288c4d1704f58743f4a50d88", "https://deno.land/std@0.192.0/uuid/mod.ts": "5c7ca252dddba1ddf0bca2dc1124328245272650c98251d71996bb9cd8f5a386", "https://deno.land/std@0.192.0/uuid/v1.ts": "fe36009afce7ced96e1b5928565e12c5a8eb0df1a2b5063c0a72bda6b75c0de5", "https://deno.land/std@0.192.0/uuid/v3.ts": "220a3746cf315052252be7e8dbfa410f579f576b849129012cb2e2b14d05782f", "https://deno.land/std@0.192.0/uuid/v4.ts": "0f081880c156fd59b9e44e2f84ea0f94a3627e89c224eaf6cc982b53d849f37e", "https://deno.land/std@0.192.0/uuid/v5.ts": "a6b4da1235c6e554eafe812bdc7617b5e8666a86368fa729f11f1eca81a3a4d3", "https://deno.land/std@0.208.0/assert/_constants.ts": "8a9da298c26750b28b326b297316cdde860bc237533b07e1337c021379e6b2a9", "https://deno.land/std@0.208.0/assert/_diff.ts": "58e1461cc61d8eb1eacbf2a010932bf6a05b79344b02ca38095f9b805795dc48", "https://deno.land/std@0.208.0/assert/_format.ts": "a69126e8a469009adf4cf2a50af889aca364c349797e63174884a52ff75cf4c7", "https://deno.land/std@0.208.0/assert/assert.ts": "9a97dad6d98c238938e7540736b826440ad8c1c1e54430ca4c4e623e585607ee", "https://deno.land/std@0.208.0/assert/assert_almost_equals.ts": "e15ca1f34d0d5e0afae63b3f5d975cbd18335a132e42b0c747d282f62ad2cd6c", "https://deno.land/std@0.208.0/assert/assert_array_includes.ts": "6856d7f2c3544bc6e62fb4646dfefa3d1df5ff14744d1bca19f0cbaf3b0d66c9", "https://deno.land/std@0.208.0/assert/assert_equals.ts": "d8ec8a22447fbaf2fc9d7c3ed2e66790fdb74beae3e482855d75782218d68227", "https://deno.land/std@0.208.0/assert/assert_exists.ts": "407cb6b9fb23a835cd8d5ad804e2e2edbbbf3870e322d53f79e1c7a512e2efd7", "https://deno.land/std@0.208.0/assert/assert_false.ts": "0ccbcaae910f52c857192ff16ea08bda40fdc79de80846c206bfc061e8c851c6", "https://deno.land/std@0.208.0/assert/assert_greater.ts": "ae2158a2d19313bf675bf7251d31c6dc52973edb12ac64ac8fc7064152af3e63", "https://deno.land/std@0.208.0/assert/assert_greater_or_equal.ts": "1439da5ebbe20855446cac50097ac78b9742abe8e9a43e7de1ce1426d556e89c", "https://deno.land/std@0.208.0/assert/assert_instance_of.ts": "3aedb3d8186e120812d2b3a5dea66a6e42bf8c57a8bd927645770bd21eea554c", "https://deno.land/std@0.208.0/assert/assert_is_error.ts": "c21113094a51a296ffaf036767d616a78a2ae5f9f7bbd464cd0197476498b94b", "https://deno.land/std@0.208.0/assert/assert_less.ts": "aec695db57db42ec3e2b62e97e1e93db0063f5a6ec133326cc290ff4b71b47e4", "https://deno.land/std@0.208.0/assert/assert_less_or_equal.ts": "5fa8b6a3ffa20fd0a05032fe7257bf985d207b85685fdbcd23651b70f928c848", "https://deno.land/std@0.208.0/assert/assert_match.ts": "c4083f80600bc190309903c95e397a7c9257ff8b5ae5c7ef91e834704e672e9b", "https://deno.land/std@0.208.0/assert/assert_not_equals.ts": "9f1acab95bd1f5fc9a1b17b8027d894509a745d91bac1718fdab51dc76831754", "https://deno.land/std@0.208.0/assert/assert_not_instance_of.ts": "0c14d3dfd9ab7a5276ed8ed0b18c703d79a3d106102077ec437bfe7ed912bd22", "https://deno.land/std@0.208.0/assert/assert_not_match.ts": "3796a5b0c57a1ce6c1c57883dd4286be13a26f715ea662318ab43a8491a13ab0", "https://deno.land/std@0.208.0/assert/assert_not_strict_equals.ts": "4cdef83df17488df555c8aac1f7f5ec2b84ad161b6d0645ccdbcc17654e80c99", "https://deno.land/std@0.208.0/assert/assert_object_match.ts": "d8fc2867cfd92eeacf9cea621e10336b666de1874a6767b5ec48988838370b54", "https://deno.land/std@0.208.0/assert/assert_rejects.ts": "45c59724de2701e3b1f67c391d6c71c392363635aad3f68a1b3408f9efca0057", "https://deno.land/std@0.208.0/assert/assert_strict_equals.ts": "b1f538a7ea5f8348aeca261d4f9ca603127c665e0f2bbfeb91fa272787c87265", "https://deno.land/std@0.208.0/assert/assert_string_includes.ts": "b821d39ebf5cb0200a348863c86d8c4c4b398e02012ce74ad15666fc4b631b0c", "https://deno.land/std@0.208.0/assert/assert_throws.ts": "63784e951475cb7bdfd59878cd25a0931e18f6dc32a6077c454b2cd94f4f4bcd", "https://deno.land/std@0.208.0/assert/assertion_error.ts": "4d0bde9b374dfbcbe8ac23f54f567b77024fb67dbb1906a852d67fe050d42f56", "https://deno.land/std@0.208.0/assert/equal.ts": "9f1a46d5993966d2596c44e5858eec821859b45f783a5ee2f7a695dfc12d8ece", "https://deno.land/std@0.208.0/assert/fail.ts": "c36353d7ae6e1f7933d45f8ea51e358c8c4b67d7e7502028598fe1fea062e278", "https://deno.land/std@0.208.0/assert/mod.ts": "37c49a26aae2b254bbe25723434dc28cd7532e444cf0b481a97c045d110ec085", "https://deno.land/std@0.208.0/assert/unimplemented.ts": "d56fbeecb1f108331a380f72e3e010a1f161baa6956fd0f7cf3e095ae1a4c75a", "https://deno.land/std@0.208.0/assert/unreachable.ts": "4600dc0baf7d9c15a7f7d234f00c23bca8f3eba8b140286aaca7aa998cf9a536", "https://deno.land/std@0.208.0/fmt/colors.ts": "34b3f77432925eb72cf0bfb351616949746768620b8e5ead66da532f93d10ba2", "https://deno.land/std@0.208.0/testing/mock.ts": "3f23573411caf1eacd48d62c5f9a606970648a694ce55132c76b0d2365baa03d", "https://deno.land/x/xhr@0.3.0/mod.ts": "094aacd627fd9635cd942053bf8032b5223b909858fa9dc8ffa583752ff63b20", "https://esm.sh/@ai-sdk/openai@1.3.18": "8509e6aa31a8e91db0b7d1193384a2ebce7e68b4ce6732db31c22d42f42adfe7", "https://esm.sh/@ai-sdk/openai@1.3.18/denonext/openai.mjs": "7073bcff00c9825566bef4e1d38ec3f2306173a30d2ef87f609fa80c0cd1bece", "https://esm.sh/@ai-sdk/openai@1.3.22": "3c0efb70f148d1803e8559cbff8a4c3420f4e3c970d9d3acc2a31501d75a8b9a", "https://esm.sh/@ai-sdk/openai@1.3.22/denonext/openai.mjs": "cde0b576c9923515249767d329985d6dcd32b8e2269badf334732c0046b3cc0b", "https://esm.sh/@ai-sdk/openai@1.3.9": "daa07d66cb7e8001d290eb21a3b4004e83a68c259f4ea1326af112d88f12cdfd", "https://esm.sh/@ai-sdk/openai@1.3.9/denonext/openai.mjs": "b6fed28b3a2ea6292326a1dae1e6b130d6f5abc4343305b7f0b4a2328951cd98", "https://esm.sh/@ai-sdk/provider-utils@2.2.1/denonext/provider-utils.mjs": "377acb9f11bd6803bf05720ca674bb6c6d78cca37d9bd3eb364607910f1c3f29", "https://esm.sh/@ai-sdk/provider-utils@2.2.6/denonext/provider-utils.mjs": "1d02dd002ebacee8180f113f385b7c29ef4a2d27e710b3f221a372993b05bf55", "https://esm.sh/@ai-sdk/provider-utils@2.2.6/es2022/provider-utils.mjs": "20286420f49d188dc76b37c22dd99f30807b4c960dc4f0f3dc1c48303813c9ec", "https://esm.sh/@ai-sdk/provider-utils@2.2.7/denonext/provider-utils.mjs": "b1d42d082831b32efe4e479fa619b94373c69829dac99c85b8be4fef703c4d68", "https://esm.sh/@ai-sdk/provider-utils@2.2.8/denonext/provider-utils.mjs": "7f8714c3c05d83f58d4cafddd4bd00e5228b8aaf829238089d4275f84fb02ee3", "https://esm.sh/@ai-sdk/provider@1.1.0/denonext/provider.mjs": "006eca52a1a4a09297051bd484ce65a0d836cb8eb5fe2555acb71aaf64b5321f", "https://esm.sh/@ai-sdk/provider@1.1.2/denonext/provider.mjs": "d9bdd9b05792a3bf43984df5aebe1be193c649fdb4b261ea26d79ec80f71264f", "https://esm.sh/@ai-sdk/provider@1.1.2/es2022/provider.mjs": "d9bdd9b05792a3bf43984df5aebe1be193c649fdb4b261ea26d79ec80f71264f", "https://esm.sh/@ai-sdk/provider@1.1.3/denonext/provider.mjs": "797a0e0099a8fd9cd41d80af78116c844baf8d0d7a841089effd6c2c28f26970", "https://esm.sh/@ai-sdk/ui-utils@1.2.1/denonext/ui-utils.mjs": "fb434f41185c3bbd5a78c6843696ee3a1c516a36813d72473e2727fff1e7e8ed", "https://esm.sh/@ai-sdk/ui-utils@1.2.11/denonext/ui-utils.mjs": "230c8806b1780c8f4374341f8cc861741cb845cf1701fb7ea41068865560b09f", "https://esm.sh/@ai-sdk/ui-utils@1.2.1?target=denonext": "9c6f504545fc99f64e00807f76d2f7b96e9adf1c8b7c830ad3466b5390743e70", "https://esm.sh/@ai-sdk/ui-utils@1.2.7": "d55e0d8138bcf0db5f71c9df70cfe97d46f14ea3b24f459938ff98df39f9cdb2", "https://esm.sh/@ai-sdk/ui-utils@1.2.7/denonext/ui-utils.mjs": "83a6e8de6421a6b5adfae75dbdb58fcd38c28e03cf18990f0b0b40d26d74b21b", "https://esm.sh/@ai-sdk/ui-utils@1.2.7/es2022/ui-utils.mjs": "ea9dbc0fbc6dea82249cd37726f0df9a762ddde5c4d56197e0b7656d3bfc8ce3", "https://esm.sh/@ai-sdk/ui-utils@1.2.8/denonext/ui-utils.mjs": "5e31392cf6f2cb83e55c2619875e1e8971a52dc7f3355892bfe7453c06d3d6f3", "https://esm.sh/@mastra/client-js@0.1.13-alpha.3": "1e3aa2075dcaf8148daa8908a94623d2d644799e3f8c5ee9ff223d1f8f339974", "https://esm.sh/@mastra/client-js@0.1.13-alpha.3/denonext/client-js.mjs": "282c75c066fe7b44eb890473b631828d6fff42ea564fe94d42faade70622abfc", "https://esm.sh/@mastra/client-js@0.1.19": "e46d996d5392e0862581e1072bf50733fdd1808e69a220e01d0ff3d7e4397289", "https://esm.sh/@mastra/client-js@0.1.19/denonext/client-js.mjs": "6fbb5ba09b1d2794ed8f706f3691a8587464aef71770eed088f8a350a0e2ebe5", "https://esm.sh/@n8n/tournament@1.0.6": "6658b7b10e2adebb2948824ddd0003330a3cfa254ab42b9822ebd55d02f835e5", "https://esm.sh/@n8n/tournament@1.0.6/denonext/tournament.mjs": "fab0b45cf30008965387ada3b00c44d9561474bccd201e3d42b874087503d536", "https://esm.sh/@n8n_io/riot-tmpl@4.0.1/denonext/riot-tmpl.mjs": "895c484174b89aca0d7d5e47a3fee6fad88418b6fe8ec91bf4feca77335441a9", "https://esm.sh/@n8n_io/riot-tmpl@4.0.1?target=denonext": "be2a0dc8d9677aba625d3e1bf8521de7a625f00d75f0af40a613e56641a7b502", "https://esm.sh/@nangohq/node@0.56.4": "8ca5e736b01d4bc337ab8f7851ab4b73c8796a79c64abefdb9cf36ec175781d2", "https://esm.sh/@nangohq/node@0.56.4/denonext/node.mjs": "21e66b3649d6b45aa7ddfbe296565036145078b22970a0f572ef282d5aeb9be7", "https://esm.sh/@opentelemetry/api@1.9.0/denonext/api.mjs": "d9ede8bd549446ae09ea5af32de6b8b7d4ca13bcbd90eb39ae890c0fea78f618", "https://esm.sh/@opentelemetry/api@1.9.0/es2022/api.mjs": "164fcc719feaeff6cc792e65b47f69ad17adddbb68b8a8fdecf39572f49385c0", "https://esm.sh/@supabase/auth-js@2.64.2/denonext/auth-js.mjs": "ae42eb4c7ac9fe6bfcd0e5c242f353877cf148759d1502ee653f21b2028be5e1", "https://esm.sh/@supabase/auth-js@2.64.4/denonext/auth-js.mjs": "a72d41796602ae950f8b7b980a629ccb4ac45376f0e510503c7a5e16019f4362", "https://esm.sh/@supabase/functions-js@2.3.1/denonext/functions-js.mjs": "e8296206805e0997328ee586fad70f86e9ab63dd36f511a85ba8c7f8c903c508", "https://esm.sh/@supabase/functions-js@2.4.1/denonext/functions-js.mjs": "0dfa81270bba8ae5af371b273a564203b4625b3a18e70774235b0b46ef40d940", "https://esm.sh/@supabase/functions-js@2.4.4/denonext/functions-js.mjs": "7adeb257410ef3c4a8a1eb9b4ff416c0075d1c32860ca04913c8a9dace1de6a6", "https://esm.sh/@supabase/functions-js@2.4.4?target=denonext": "e2476c61b8afb50cd0987ff03900f48daa646706d6bad319c347e38870b9e72b", "https://esm.sh/@supabase/gotrue-js@2.69.1/denonext/gotrue-js.mjs": "f750ba1c7bed3c55b044078ec8ccfa44367b5f27cc70dc41cbf51706f83378ee", "https://esm.sh/@supabase/gotrue-js@2.69.1?target=denonext": "292e91ea998566e337a060e8f0274dfdb02d0af1cc45366319c7a46fe53d3d31", "https://esm.sh/@supabase/node-fetch@2.6.15/denonext/node-fetch.mjs": "0bae9052231f4f6dbccc7234d05ea96923dbf967be12f402764580b6bf9f713d", "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext": "4d28c4ad97328403184353f68434f2b6973971507919e9150297413664919cf3", "https://esm.sh/@supabase/postgrest-js@1.15.2/denonext/postgrest-js.mjs": "46a1fa1d82c4c082de350bb6b606b2f216e131b8e1f7d2c57a046e2ade2bc059", "https://esm.sh/@supabase/postgrest-js@1.15.8/denonext/postgrest-js.mjs": "e38ab1fbd1d7ed325e3ddc20c72001d3653151c3d3be3455d682b141321a345d", "https://esm.sh/@supabase/postgrest-js@1.19.4/denonext/postgrest-js.mjs": "2073b5552ba10c7a8302bffffae771e3aede1daf833382355dae239fb0ab2576", "https://esm.sh/@supabase/postgrest-js@1.19.4?target=denonext": "b526be6f497dc0c461937d1f877cf2e2ebf5dbd92cf2af098fc22a6362a9a569", "https://esm.sh/@supabase/realtime-js@2.10.2/denonext/realtime-js.mjs": "3a5a350e881736135ec1bd8c4361c96edeb647d3c56be7cf3a68eddfaf3ce242", "https://esm.sh/@supabase/realtime-js@2.11.7/denonext/realtime-js.mjs": "63b135b799827a3793cbdc82adfe73fade427b991d0b155eac6e7c97f6c0552c", "https://esm.sh/@supabase/realtime-js@2.11.7?target=denonext": "25567f84d1658232a29414054330863cc71b2249c288f00099a0c99ac01f2e18", "https://esm.sh/@supabase/realtime-js@2.9.5/denonext/realtime-js.mjs": "6dfd328883982dee8a34d8b61b1f02a4a080ecd1a89a51e29b75efe6b40ed700", "https://esm.sh/@supabase/storage-js@2.5.5/denonext/storage-js.mjs": "a952ba6fdc889a4ae8d671ce4d1a152702c1caa3158eb2269a0208b091856075", "https://esm.sh/@supabase/storage-js@2.6.0/denonext/storage-js.mjs": "d3f8b6f5f538cd0d8f88ed0248c3b5dc845d9ca41500589e19fbfc734c8952de", "https://esm.sh/@supabase/storage-js@2.7.2/denonext/storage-js.mjs": "b1bf6c0dc5c1792976cf102be6d867236a1ce2bb0c091ca90c02ea6d7a087255", "https://esm.sh/@supabase/storage-js@2.7.2?target=denonext": "beeffa5b6f716944ee3342ee432016cea801ed1f241396b57e2cdcd50c36d8fe", "https://esm.sh/@supabase/supabase-js@2.38.4": "039569070c2e83c292b347c39879a10e76aac6079c166f2e2677453a7799e933", "https://esm.sh/@supabase/supabase-js@2.38.4/denonext/supabase-js.mjs": "c9a6b6de54aa47e98b836b90dd0f7dd292afc990762b2c84e77aca92e94d05ba", "https://esm.sh/@supabase/supabase-js@2.43.4": "ed6952bb6d04f4abd86e80a0dde37f35798d483d2603cc8408baf310a21a0b36", "https://esm.sh/@supabase/supabase-js@2.43.4/denonext/supabase-js.mjs": "0b1353c9f4d8ade0ab96c56cf7c266f7ecb30c2fa93aa2ffbea9f23f8677dfdc", "https://esm.sh/@supabase/supabase-js@2.45.0": "36ae57c3f2dc3f94fd122a846b40a87114d29bb2f47e910cb46bee5575febf2e", "https://esm.sh/@supabase/supabase-js@2.45.0/denonext/supabase-js.mjs": "59c4845d0798118ff097b3295b5a6f3ef63d18ff4a31bf8125e91b467e0be7a6", "https://esm.sh/ai@2.2.35/denonext/ai.mjs": "aa5d18d6d26b0b7ea3ed5080c0607bb7c4f427f90ecc2f9cd9e998d1b0c8f6e4", "https://esm.sh/ai@2.2.35/dist/index.js": "d6c7ccca41cd666ef511157c018ac48e92b7aa79b9ae2ded4569d86772507482", "https://esm.sh/ai@4.3.15": "db1cd55ffbdbc5482d9748b6a09416d05374fda9af4e3bac8f841734a5c3f39b", "https://esm.sh/ai@4.3.15/denonext/ai.mjs": "a487306a4ed8382ec1311f37ecc43d96479677dea1f8dbda685df0755943fe8b", "https://esm.sh/ai@4.3.4": "9bb4be71a1dcfceaff6a6748b99f73a4fc7d54db7c6f6cbb63e2e893234faf7a", "https://esm.sh/ai@4.3.4/denonext/ai.mjs": "7cc233c9cc5957947da807a1d4a8f77cbe7f83f29ec19cc26616c615a8406e2b", "https://esm.sh/ai@4.3.4/dist/index": "d0433e7208458eafe7739dd4a7d9e65f0e889107ef0efdd6b4ad3a688b5f49b3", "https://esm.sh/ai@4.3.4/dist/index.js": "afd60daf358f9c3d25dc3eabdb1a453b262c2e92b11cc673f7b3eb65b7f806db", "https://esm.sh/ai@4.3.4/es2022/ai.mjs": "037d757a0feab4f22a06db97c8c2f241fefd9984f921c1c76e2ef3a42b7b39f0", "https://esm.sh/ai@4.3.9": "6aa350aaf8b57d069c18404dd1fdc953f36bad6607faad22b487bc1f94c3fefb", "https://esm.sh/ai@4.3.9/denonext/ai.mjs": "79fe758dc2f96fe011c4f5c120a2b5709c4259743d0677b19f0ceba015aa34d2", "https://esm.sh/ast-types@0.15.2/denonext/ast-types.mjs": "76626bcc162be03f8d574b50d5f5335c4614d263f84ec236e7cf238da3d9e66c", "https://esm.sh/ast-types@0.16.1/denonext/ast-types.mjs": "76fa1c6735acdabfa408ef00022e439b8668a5674ec1de6e6625c789ae529625", "https://esm.sh/ast-types@0.16.1?target=denonext": "0334b8baed476f97dacccd9841fe9bb60fcab7a90e22f801d634bf8223f20bf7", "https://esm.sh/asynckit@0.4.0/denonext/asynckit.mjs": "4ef3be6eb52c104699b90ca5524db55ec15bc76b361432f05c16b6106279ba72", "https://esm.sh/asynckit@0.4.0?target=denonext": "c6bd8832d6d16b648e22d124a16d33c3a7f7076e92be9444f2e4f6b27545708d", "https://esm.sh/axios@1.8.2/denonext/axios.mjs": "4e4cf74efd53eeae0cbf5923284e17fbd2ae714a1b22b55a70097b67cc8a57d9", "https://esm.sh/axios@1.8.2/denonext/lib/adapters/http.mjs": "9b3ec04263cf68201b93e2b5b13d464039c7cd27f324d1a151e579fe11efe126", "https://esm.sh/axios@1.8.2/denonext/lib/adapters/xhr.mjs": "c5a53d0300744bd1547b6d627384f3e6b46567a0fc9048b33744f954982b8cd8", "https://esm.sh/axios@1.8.2/denonext/lib/cancel/CanceledError.mjs": "826692fe7b77c6d140518230a600b18fe371d8513f6e724c8b9bc21c73ce4ffc", "https://esm.sh/axios@1.8.2/denonext/lib/core/AxiosError.mjs": "9f3c2b0ae6c7d65a49f47baee7351e9aea92e07d09656726b9f6fd808b84ef69", "https://esm.sh/axios@1.8.2/denonext/lib/core/AxiosHeaders.mjs": "cc77d6409f0a0cb3eb28b766973322178389aed67ededde5796b278362a957ee", "https://esm.sh/axios@1.8.2/denonext/lib/core/mergeConfig.mjs": "9562efdcc7cf8d644bb82728649b8c8523022b114f16c647ddf762d70f713f87", "https://esm.sh/axios@1.8.2/denonext/lib/defaults/transitional.mjs": "e2fdca54fd40bc433230dc619d4635f20b1d404a167a15aba38afc76930a2c70", "https://esm.sh/axios@1.8.2/denonext/lib/env/data.mjs": "f0072e199aa1ac20297a0b0f58787eb87ab416564bbc0a0c68ce8537d6589a18", "https://esm.sh/axios@1.8.2/denonext/lib/helpers/bind.mjs": "1da7fb1b328612a1759b8c58fcf2ac9ff8641cd72bd78b541c28e141e7ea50f3", "https://esm.sh/axios@1.8.2/denonext/lib/helpers/parseProtocol.mjs": "9e19358801b4427ac0a0e946940e2e56abaa1a6ea4c1919b539ccff69780e628", "https://esm.sh/axios@1.8.2/denonext/lib/helpers/progressEventReducer.mjs": "0185fc2af4ef3e6821034a7821a084dfb64374cd44eb92e4fd806249accaa457", "https://esm.sh/axios@1.8.2/denonext/lib/helpers/resolveConfig.mjs": "dbce4eed9175e4f54eb48442e1e31fbd8224326bf0a779edb2cdc4133be11dd5", "https://esm.sh/axios@1.8.2/denonext/lib/helpers/toFormData.mjs": "bdb45d94e40b142af44294aa6ad4b5c74b52b3284c4108c4240e9e3468f3323a", "https://esm.sh/axios@1.8.2/denonext/lib/platform/index.mjs": "c9b1794ba03e84eb4f37d0327f6760960abe9f3ff92a56db2f026b97d3721c71", "https://esm.sh/axios@1.8.2/denonext/lib/platform/node/classes/FormData.mjs": "419591ce4b61d0624a9f03677f0f24f0788d2311f98b2c48198470295f394639", "https://esm.sh/axios@1.8.2/denonext/unsafe/core/buildFullPath.mjs": "257ad8815cf75504503c8cfa40fe0069fc3e207ceab0ccdfe255bfd8510e4859", "https://esm.sh/axios@1.8.2/denonext/unsafe/core/settle.mjs": "6b508236e249101f5ef4bc5fa9d772f297ac3e4693a050541ea6a1a9ce09e628", "https://esm.sh/axios@1.8.2/denonext/unsafe/helpers/buildURL.mjs": "d6edb5fdb7778903f09fb66968c808336965b1a97c376f7efc321fb241d05e90", "https://esm.sh/axios@1.8.2/denonext/unsafe/helpers/combineURLs.mjs": "ff82e5d67131598edb4c4bc42ebe0e559c324255e13e6c84e73da5d1ba167235", "https://esm.sh/axios@1.8.2/denonext/unsafe/helpers/isAbsoluteURL.mjs": "012a31ebe45a0260a672d032feb8c4f4b0afabb409a37d2e746164c2fecadd0d", "https://esm.sh/axios@1.8.2/denonext/unsafe/utils.mjs": "7f847190a37ad28e58ef5214486a6585d6aa5258a8b9c210f70e8942313d40de", "https://esm.sh/axios@1.8.2?target=denonext": "287715b2c69d77289fa1167243c8437ad935933f75846c67a0a83fd9edc6d53a", "https://esm.sh/bufferutil@4.0.9/denonext/bufferutil.mjs": "13dca4d5bb2c68cbe119f880fa3bd785b9a81a8e02e0834dae604b4b85295cd8", "https://esm.sh/bufferutil@4.0.9?target=denonext": "e32574569ab438facfcc3f412c659b0719bbf05477136ca176938c9a3ac45125", "https://esm.sh/combined-stream@1.0.8/denonext/combined-stream.mjs": "364b91aa4c33e5f0b4075949d93a3407b21a8695031e7c2be29999d588f9ca2c", "https://esm.sh/combined-stream@1.0.8?target=denonext": "a0c89b8b29494e966774c7a708e33cc2df16a0bbe2279c841d088e169e7ab3c4", "https://esm.sh/debug@4.4.0/denonext/debug.mjs": "3077d1ff15cfc5b7baee65b0c00b3200aef8ab51ddddfa960972957c347c1cee", "https://esm.sh/debug@4.4.0?target=denonext": "dc29873ca5518385fcbddb2b2fa0f3b31dc6463ba52bdd790818683b9dbdc6ad", "https://esm.sh/delayed-stream@1.0.0/denonext/delayed-stream.mjs": "051a3501b7b3d3c593b78a2c7305093a8e363c518cd156f1a77117185e312abe", "https://esm.sh/delayed-stream@1.0.0?target=denonext": "d363b81e01f4c886114df14aa660c1a938bbb4be851ff12132260bed0db6126e", "https://esm.sh/esprima-next@5.8.4/denonext/esprima-next.mjs": "4e4e36e11d900bf13756548d3740207d7d1ab96af068f860c769c1e20e34df79", "https://esm.sh/esprima-next@5.8.4?target=denonext": "60bb8f7ef13f956ed4b8f8c4d3f96442a2609fbcebd01b8c4f5ba1ff4e8b75dd", "https://esm.sh/esprima@4.0.1/denonext/esprima.mjs": "d3bea7062572a9a322f748f32aaff4aa22d0b1c22d6853eb0182a2aa68a1fa0e", "https://esm.sh/esprima@4.0.1?target=denonext": "4f91aef49988e0f08b2bad39751d097a908512d01b711f68892e71a826da859d", "https://esm.sh/eventsource-parser@1.0.0/denonext/eventsource-parser.mjs": "2660662b264e8d174c3dfeb7c2e03a68a8c1ed5c664aa8f54f543f70dc276c77", "https://esm.sh/follow-redirects@1.15.9/denonext/follow-redirects.mjs": "90866d22d80eface74d3161906835600fbb1d5c9ded05dc72fd55b40960cfce7", "https://esm.sh/follow-redirects@1.15.9?target=denonext": "c8028ec9d980a1974362377614a4978d1556ba85a75bc32526e8917bede535d1", "https://esm.sh/form-data@4.0.2/denonext/form-data.mjs": "ad3c492eef1c6153bcfa02eb3041894e8bc8e4aa241ad4d9bd53a385f36d2c6f", "https://esm.sh/form-data@4.0.2?target=denonext": "83cf111a2e6f0f7b6c045508635ceae449c5d43d8d22df23693e5b996cc65899", "https://esm.sh/json-schema@0.4.0": "21a68e4e83b0441c3a90fa051c6b7c42d29926e4ff429745eebec153ac53cd7a", "https://esm.sh/json-schema@0.4.0/denonext/json-schema.mjs": "95225c6fc709935fca00cfc112f2ab043d204f7bd07a8aaec75b1b5f24ca8bf6", "https://esm.sh/lodash@4.17.16": "82c3ebc1d6fc31e9210caee417bdd2e0746915308d3c31c8cfd053ab7e61adf2", "https://esm.sh/lodash@4.17.16/denonext/lodash.mjs": "0148096f9d145e240c571ed74816fee009fa9af7e5db1bf57b899b0eb88a9aa4", "https://esm.sh/mime-db@1.52.0/denonext/mime-db.mjs": "f93feb3d7150014b71bd0d06c5bd819db56a089b31b8b79a3b0466bb37ef005e", "https://esm.sh/mime-types@2.1.35/denonext/mime-types.mjs": "704bdb318816fe1360c90a196f7cb3ba6e25fe207707cc2df873f890ad2e5f44", "https://esm.sh/mime-types@2.1.35?target=denonext": "e4cc9a1aabecc1be22d194375ec3b99cc9d51700cc4629ab689975451c0a8ce5", "https://esm.sh/ms@2.1.3/denonext/ms.mjs": "9039464da1f4ae1c2042742d335c82556c048bbe49449b5d0cd5198193afa147", "https://esm.sh/ms@2.1.3?target=denonext": "36f5aa7503ff0ff44ce9e3155a60362d8d3ae5db8db048be5764a3a515b6a263", "https://esm.sh/nanoid@3.3.11/denonext/non-secure.mjs": "1f166118c1c4b4d4b5356ef0050fed87acf28cdb8a76813803fee72188f18b30", "https://esm.sh/nanoid@3.3.11/es2022/non-secure.mjs": "1f166118c1c4b4d4b5356ef0050fed87acf28cdb8a76813803fee72188f18b30", "https://esm.sh/nanoid@3.3.11/non-secure?target=denonext": "ac0c34cc5f9846db51a5d868ce6ee815f831a19b1d57a1b8bae9226fc8d68dec", "https://esm.sh/nanoid@3.3.6/denonext/non-secure.mjs": "4286ba29d7bef3b89b97e6cc3838cee95d48fb667145a1029f8baa9d28a84baa", "https://esm.sh/nanoid@^3.3.8/non-secure?target=es2022": "c4854e141d8a34631acab6e971e526afff82229cc2d47cffd005f8ad7d068365", "https://esm.sh/node-gyp-build@4.8.4/denonext/node-gyp-build.mjs": "9a86f2d044fc77bd60aaa3d697c2ba1b818da5fb1b9aaeedec59a40b8e908803", "https://esm.sh/node-gyp-build@4.8.4?target=denonext": "261a6cedf1fdbf159798141ba1e2311ac1510682c5c8b55dacc8cf5fdee4aa06", "https://esm.sh/node/buffer.mjs": "43d7ffab85c67adc0575a7d7c463e84a61ee8efc455d95665c8eb8d4a31e17cd", "https://esm.sh/node/process.mjs": "2c8eeaf6090c03d21bdcec5eddb33b105208c9ba3ef2c7be98f4d9b2495a624e", "https://esm.sh/proxy-from-env@1.1.0/denonext/proxy-from-env.mjs": "f60f9c79fc3baa07c13c800798d645ae70d1b2059b8d593dcd4f8c5710b50333", "https://esm.sh/proxy-from-env@1.1.0?target=denonext": "bf02a050a1a6aa56ddba25dbea2c355da294630e5c5520fddea4b2f30a9292bc", "https://esm.sh/recast@0.22.0/denonext/lib/util.mjs": "896d7a04d4cd21f07420a62b95b50c04cb9c238befe4cdd4be9cb795bc687275", "https://esm.sh/recast@0.22.0/denonext/recast.mjs": "8320ff0fc2e4a28f0f76ddfaf496f6901d31e9eae59e66ff1a54b1150c260387", "https://esm.sh/recast@0.22.0/lib/util?target=denonext": "d7333452206e96381e1fd459ec70724f417497bf22e5ad8601d839f07ab3de96", "https://esm.sh/recast@0.22.0?target=denonext": "3375c43acc852b186424d080eb05d9d1f60bb0c5f27ca1984989ffd0a542be7b", "https://esm.sh/secure-json-parse@2.7.0/denonext/secure-json-parse.mjs": "bb9840bcb6fdce5857d7412b4c4e272b70044efac66a58cc78d8c97bdb56786d", "https://esm.sh/secure-json-parse@2.7.0/es2022/secure-json-parse.mjs": "d9224c9d4200445e270844b5cb35c3c474b6a95cf27e8484af350e5bf251dd1f", "https://esm.sh/secure-json-parse@2.7.0?target=denonext": "00b85213b6c18313c3de9c07941e27f9666f8c601818c221775a4f744430f89f", "https://esm.sh/secure-json-parse@^2.7.0?target=es2022": "fc877b3ebfcc400b620b80d29de3f2b24b83a97026b65f75b0924dbb8073213b", "https://esm.sh/source-map@0.6.1/denonext/source-map.mjs": "ed3f2cf0c5f9561924c9b923ea8ffc24709ae31f1a90dd6c67be04c99598f626", "https://esm.sh/source-map@0.6.1?target=denonext": "3803db0ea481a551d1b0f98b57c3b32597fe2c03a5204fbec3aa58d1892f5fa0", "https://esm.sh/supports-color@10.0.0/denonext/supports-color.mjs": "239cd39d0828e1a018dee102748da869b1b75c38fe6a9c0c8f0bd4ffbd3e1ea1", "https://esm.sh/supports-color@10.0.0?target=denonext": "4895255248e4ba0cbcce9437003dccf3658b1ac1d1e8eba5225fb8194c454ee1", "https://esm.sh/tr46@0.0.3/denonext/tr46.mjs": "5753ec0a99414f4055f0c1f97691100f13d88e48a8443b00aebb90a512785fa2", "https://esm.sh/tr46@0.0.3?target=denonext": "19cb9be0f0d418a0c3abb81f2df31f080e9540a04e43b0f699bce1149cba0cbb", "https://esm.sh/tslib@2.8.1/denonext/tslib.mjs": "ebce3cd5facb654623020337f867b426ba95f71596ba87acc9e6c6f4e55905ca", "https://esm.sh/tslib@2.8.1?target=denonext": "a89cbf082a0ad54e87ea85d75e6d5492b3e2ff0c5de9e3e478206a015b3f3992", "https://esm.sh/utf-8-validate@6.0.5/denonext/utf-8-validate.mjs": "66b8ea532a0c745068f5b96ddb1bae332c3036703243541d2e89e66331974d98", "https://esm.sh/utf-8-validate@6.0.5?target=denonext": "071bc33ba1a58297e23a34d69dd589fd06df04b0f373b382ff5da544a623f271", "https://esm.sh/webidl-conversions@3.0.1/denonext/webidl-conversions.mjs": "54b5c2d50a294853c4ccebf9d5ed8988c94f4e24e463d84ec859a866ea5fafec", "https://esm.sh/webidl-conversions@3.0.1?target=denonext": "4e20318d50528084616c79d7b3f6e7f0fe7b6d09013bd01b3974d7448d767e29", "https://esm.sh/whatwg-url@5.0.0/denonext/whatwg-url.mjs": "29b16d74ee72624c915745bbd25b617cfd2248c6af0f5120d131e232a9a9af79", "https://esm.sh/whatwg-url@5.0.0?target=denonext": "f001a2cadf81312d214ca330033f474e74d81a003e21e8c5d70a1f46dc97b02d", "https://esm.sh/ws@8.18.1/denonext/ws.mjs": "732cae76ba0acb311a561003d2f7ef569293cb9159d67dd800ab346b84f80432", "https://esm.sh/ws@8.18.1?target=denonext": "e99b670fc49b38e15a7576ddcd5bb01e123fe9b3a017db7f97898127811b4e27", "https://esm.sh/zod-to-json-schema@3.24.5/denonext/zod-to-json-schema.mjs": "7e268801e3d56cc04c2d1564df322a587408470ecd3c9c73815bacef46392966", "https://esm.sh/zod-to-json-schema@3.24.5/es2022/zod-to-json-schema.mjs": "5c45bd74c6482f1af0cf5a2f9496eb22c43cf2d3cfc48fba3e01b9cef5f5cd4c", "https://esm.sh/zod-to-json-schema@3.24.5?target=denonext": "f90b6ae04e9353b29477d688fa3472b617e189092c32cb39ecdde9305e28bdbb", "https://esm.sh/zod-to-json-schema@^3.24.1?target=es2022": "3409dfe9179666b983997e0512ecbf85f2cace130ff6e9836ae5c2dfada93325", "https://esm.sh/zod@3.24.2/denonext/zod.mjs": "742680b93730f63c3a3160f75475b69f661bd353d6b8b46f602c7a685837e76d", "https://esm.sh/zod@3.24.2/es2022/zod.mjs": "742680b93730f63c3a3160f75475b69f661bd353d6b8b46f602c7a685837e76d", "https://esm.sh/zod@3.24.2?target=denonext": "634dc151ffdec010f55988d783c024d926124f7e943e00c27004f73bc5543902", "https://esm.sh/zod@3.24.4": "5a1c52369b00ff7f07ddfc1b926a5e5fd4b3a71ed977551e139651d2bf48c168", "https://esm.sh/zod@3.24.4/denonext/zod.mjs": "0c59b8fa3517c09f174814d332fbdfd826e0b726f3add9defd39976d5a122f86", "https://esm.sh/zod@^3.23.8?target=es2022": "9c4916a0991ebb0a08e2ee3801a34f1f48e237d7f041ee494e11a737b4214949", "https://esm.sh/zod@^3.24.1?target=es2022": "9c4916a0991ebb0a08e2ee3801a34f1f48e237d7f041ee494e11a737b4214949"}, "workspace": {"packageJson": {"dependencies": ["npm:@prisma/client@^6.4.1", "npm:@types/fs-extra@^11.0.4", "npm:@types/node@^20.17.30", "npm:@types/prompts@^2.4.9", "npm:dotenv@^16.4.5", "npm:eslint-config-prettier@^9.1.0", "npm:eslint-plugin-prettier@^5.1.3", "npm:eslint@^9.9.1", "npm:fs-extra@^11.3.0", "npm:js-yaml@^4.1.0", "npm:prettier@^3.2.5", "npm:prisma@^6.4.1", "npm:prompts@^2.4.2", "npm:tsx@^4.19.3", "npm:typescript@^5.5.3"]}, "members": {"packages/emcpe": {"packageJson": {"dependencies": ["npm:@astrojs/react@^4.2.3", "npm:@astrojs/tailwind@^6.0.2", "npm:@floating-ui/react@~0.27.5", "npm:@headlessui/react@^2.2.0", "npm:@nangohq/frontend@~0.56.4", "npm:@supabase/supabase-js@^2.39.7", "npm:@tailwindcss/typography@~0.5.16", "npm:@types/deno@^2.2.0", "npm:@types/lodash@^4.14.202", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.5", "npm:@types/uuid@^9.0.8", "npm:@typescript-eslint/eslint-plugin@^8.29.0", "npm:@typescript-eslint/parser@^8.29.0", "npm:@vitejs/plugin-react@^4.2.1", "npm:astro@^5.5.6", "npm:autoprefixer@^10.4.18", "npm:clsx@^2.1.1", "npm:eslint-plugin-react-hooks@^5.1.0-rc.0", "npm:eslint-plugin-react-refresh@~0.4.5", "npm:eslint@^9.9.1", "npm:lodash@^4.17.21", "npm:lucide-react@0.482.0", "npm:postcss@^8.4.35", "npm:react-dom@^18.3.1", "npm:react-intl@^7.1.6", "npm:react-router-dom@^7.4.1", "npm:react@^18.3.1", "npm:supabase@^2.19.7", "npm:tailwind-scrollbar@^3.1.0", "npm:tailwindcss@^3.4.1", "npm:typescript@^5.5.3", "npm:uuid@^9.0.1", "npm:zod@^3.24.2"]}}, "packages/emcpe-nango-integrations": {"packageJson": {"dependencies": ["npm:eslint@8", "npm:prettier@2"]}}, "packages/emcpe-server": {"packageJson": {"dependencies": ["npm:@modelcontextprotocol/sdk@^1.8.0", "npm:@nangohq/node@~0.58.1", "npm:@supabase/supabase-js@^2.49.4", "npm:@types/express@^5.0.1", "npm:@types/resolve@^1.20.6", "npm:dotenv@^16.4.5", "npm:express@^5.1.0", "npm:tsx@^4.19.3", "npm:typescript@^5.8.2", "npm:zod@^3.24.2"]}}, "packages/ma-next": {"packageJson": {"dependencies": ["npm:@ai-sdk/google@^1.2.10", "npm:@ai-sdk/openai@^1.3.9", "npm:@ai-sdk/react@^1.2.8", "npm:@eslint/eslintrc@3", "npm:@floating-ui/react@~0.27.7", "npm:@headlessui/react@^2.2.1", "npm:@nangohq/frontend@~0.58.5", "npm:@supabase/supabase-js@^2.49.4", "npm:@tailwindcss/postcss@4", "npm:@tailwindcss/typography@~0.5.16", "npm:@types/node@20", "npm:@types/react-dom@19", "npm:@types/react@19", "npm:ai@^4.3.4", "npm:class-variance-authority@~0.7.1", "npm:clsx@^2.1.1", "npm:date-fns@^4.1.0", "npm:eslint-config-next@15.3.0", "npm:eslint@9", "npm:lodash@^4.17.21", "npm:lucide-react@0.487", "npm:next@15.3.0", "npm:postcss@^8.4.35", "npm:react-dom@19", "npm:react-intl@^7.1.10", "npm:react-markdown@^10.1.0", "npm:react@19", "npm:supabase@^2.20.12", "npm:tailwind-merge@^3.2.0", "npm:tailwindcss@4", "npm:typescript@5", "npm:use-debounce@^10.0.4", "npm:uuid@^11.1.0", "npm:zod@^3.24.2"]}}, "packages/mastra": {"packageJson": {"dependencies": ["npm:@ai-sdk/anthropic@latest", "npm:@ai-sdk/google@latest", "npm:@ai-sdk/openai@latest", "npm:@mastra/core@latest", "npm:@mastra/deployer-netlify@latest", "npm:@mastra/libsql@latest", "npm:@mastra/memory@latest", "npm:@nangohq/node@~0.58.6", "npm:@supabase/supabase-js@latest", "npm:@types/node@latest", "npm:mastra@latest", "npm:tsx@latest", "npm:typescript@latest", "npm:zod@latest"]}}, "packages/nango-integrations": {"packageJson": {"dependencies": ["npm:eslint@8", "npm:prettier@2"]}}}}}