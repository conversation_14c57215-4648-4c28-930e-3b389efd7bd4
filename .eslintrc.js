module.exports = {
  settings: {
    'import/resolver': {
      alias: {
        map: [
          ['components', './src/components'],
          ['utils', './src/utils'],
          ['hooks', './src/hooks'],
          ['types', './src/types'],
          ['services', './src/services'],
          ['contexts', './src/contexts'],
          ['constants', './src/constants'],
          ['styles', './src/styles'],
          ['assets', './src/assets'],
          ['src', './src'],
        ],
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
      },
    },
  },
};
