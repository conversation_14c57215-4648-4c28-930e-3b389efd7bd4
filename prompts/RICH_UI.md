Here are the instructions for creating new rich UI components from a list of providerKey and actionKey pairs, using nangoReference.ts:

Objective: Create new rich UI components for specific Nango actions based on a provided list of providerKey and actionKey pairs, ensuring they use the correct TypeScript types from nangoModels.ts.

Process:

Receive the list: Obtain the list of providerKey and actionKey pairs for which new rich UI components are needed.
Determine Component Type: For each pair, determine if the component should display parameters (for actions) or results (for actions or syncs).
If displaying parameters, the component will be added to packages/ma-next/src/components/rich-ui/RichParameterDisplay.tsx and will use the corresponding INPUT model.
If displaying results, the component will be added to packages/ma-next/src/components/rich-ui/RichResultDisplay.tsx and will use the corresponding OUTPUT model.
Find Model Name from Reference: Read the packages/ma-next/src/constants/nangoReference.ts file.
If creating a parameter component, iterate through the ACTION_INPUTS array to find the object where provider matches your providerKey and action matches your actionKey. The model property of this object is the name of the TypeScript model to use.
If creating a result component, iterate through the ACTION_OUTPUTS array to find the object where provider matches your providerKey and action matches your actionKey. The model property of this object is the name of the TypeScript model to use.
Note: You would typically use a tool like read_file to get the content of nangoReference.ts.
Find TypeScript Interface: Read the packages/ma-next/src/config/nangoModels.ts file. Locate the export interface definition that matches the model name found in the previous step. This interface defines the structure of the data the component will receive.
Note: You would typically use a tool like read_file to get the content of nangoModels.ts.
Create New Component File: Create a new .tsx file for the component in the appropriate directory:
For parameter components: packages/ma-next/src/components/rich-ui/rich-parameters/
For result components: packages/ma-next/src/components/rich-ui/rich-results/
Name the file descriptively, e.g., [Provider][Action]Display.tsx.
This step requires switching to 'Code' mode.
Use the write_to_file tool to create the file with initial component structure. Import React and the relevant TypeScript model from src/config/nangoModels.ts. Define the component's props interface using the imported model name found in step 3.
Example Component Structure (parameter component):
import React from 'react';
import { YourModelName } from 'src/config/nangoModels'; // Replace YourModelName

type YourComponentDisplayProps = {
  parameters: YourModelName;
}

function YourComponentDisplay({ parameters }: YourComponentDisplayProps) {
  // Component logic to display parameters/results based on YourModelName structure
  // Refer to the interface definition from step 4 to access properties
  return (
    <div>
      {/* Render UI based on parameters */}
    </div>
  );
}

export { YourComponentDisplay };


Example Component Structure (result component):
import React from 'react';
import { YourModelName } from 'src/config/nangoModels'; // Replace YourModelName

type YourComponentDisplayProps = {
  output: YourModelName;
}

function YourComponentDisplay({ output }: YourComponentDisplayProps) {
  // Component logic to display output based on YourModelName structure
  // Refer to the interface definition from step 4 to access properties
  return (
    <div>
      {/* Render UI based on output */}
    </div>
  );
}

export { YourComponentDisplay };


typescript


Implement Component Logic: Fill in the component logic to display the data from the parameters (or result) prop. Refer to the structure of the TypeScript interface found in step 4 to access the relevant properties (e.g., parameters.propertyName). Design the UI to effectively present the information.
Import and Add Component to Display Map:
Read either packages/ma-next/src/components/rich-ui/RichParameterDisplay.tsx (for parameter components) or packages/ma-next/src/components/rich-ui/RichResultDisplay.tsx using the read_file tool.
This step requires switching to 'Code' mode.
Use the write_to_file tool to modify the file. Import the newly created component at the top of the file. Add an entry to the RICH_PARAM_COMPONENTS or RICH_RESULT_COMPONENTS object, mapping the providerKey:actionKey string to the imported component.
Example modification for RichParameterDisplay.tsx:
import { GmailDraftDisplay } from './rich-parameters/GmailDraftDisplay';
// ... other imports
import { NewProviderNewActionDisplay } from './rich-parameters/NewProviderNewActionDisplay'; // Import new component

const RICH_PARAM_COMPONENTS: Record<string, React.FC<{ parameters: any }>> = {
  'google-mail:compose-draft': GmailDraftDisplay,
  // ... other entries
  'new-provider:new-action': NewProviderNewActionDisplay, // Add new entry
};

// ... rest of the file

typescript


Verify and Refine: Review the new component and the updated display file to ensure everything is correct and type-safe. Make any necessary adjustments to the component's rendering logic based on the actual data structure and desired presentation.
