import type { NangoAction, XSocialPostOutput, XSocialPostInput } from '../../models';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: XSocialPostInput
): Promise<XSocialPostOutput | ErrorResponse> {
  const { text, reply_to, quote } = input;

  if (!text || text.trim() === '') {
    return { error: { status: 400, message: 'Post text is required and cannot be empty' } };
  }

  if (text.length > 280) {
    return {
      error: {
        status: 400,
        message: `Post text exceeds the 280 character limit (current: ${text.length})`,
      },
    };
  }

  const payload: Record<string, any> = {
    text,
  };

  if (reply_to) {
    payload['reply'] = {
      in_reply_to_tweet_id: reply_to,
    };
  }

  if (quote) {
    payload['quote_tweet_id'] = quote;
  }

  try {
    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/2/tweets',
      data: payload,
      retries: 3,
      headers: {
        'Accept-Encoding': '', // <----- ONLY WORKS WITH THIS.
      },
    });

    return response.data.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.detail ||
      error.response?.data?.title ||
      error.message ||
      'Unknown error sending Twitter post';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}
