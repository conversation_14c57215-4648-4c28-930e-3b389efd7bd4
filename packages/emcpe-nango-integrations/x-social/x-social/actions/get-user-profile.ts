import type { NangoAction, XSocialUserProfile } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Get the authenticated user's profile information
 * This action can be used to test if a connection is working correctly
 */
export default async function getXSocialUserProfile(
  action: NangoAction
): Promise<XSocialUserProfile | NangoError> {
  try {
    const response = await action.proxy({
      endpoint:
        '/2/users/me?user.fields=id,name,username,profile_image_url,description,location,url,protected,verified,public_metrics',
      method: 'GET',
      headers: {
        'Accept-Encoding': '', // Disable compression as it's broken for this request in nango.
      },
    });

    return response.data;
  } catch (error: any) {
    await action.log(`Error during XSocial profile fetch or processing: ${error.message}`);
    const errorMessage =
      error.response?.data?.detail ||
      error.response?.data?.title ||
      error.message ||
      'Unknown error getting XSocial user profile';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}
