integrations:
    google-docs:
        actions:
            get-document:
                endpoint: GET /v1/documents/:documentId # Path relative to https://docs.googleapis.com/
                description: Retrieves a specific Google Document.
                input: GoogleDocsGetDocumentInput
                output: GoogleDocsDocument
                scopes:
                    - https://www.googleapis.com/auth/documents.readonly
            create-document:
                endpoint: POST /v1/documents # Path relative to https://docs.googleapis.com/
                description: Creates a blank Google Document.
                input: GoogleDocsCreateDocumentInput
                output: GoogleDocsDocument # Returns the created document object
                scopes:
                    - https://www.googleapis.com/auth/documents # Requires write scope
            update-document:
                endpoint: POST /v1/documents/:documentId:batchUpdate # Path relative to https://docs.googleapis.com/
                description: Applies batch updates to a Google Document.
                input: GoogleDocsUpdateDocumentInput
                output: GoogleDocsUpdateDocumentOutput
                scopes:
                    - https://www.googleapis.com/auth/documents # Requires write scope

models:
    # Google Docs Models
    GoogleDocsGetDocumentInput:
        documentId: string      # The ID of the document to retrieve.

    GoogleDocsCreateDocumentInput:
        title?: string          # Optional title for the new document.

    GoogleDocsUpdateDocumentInput:
        documentId: string      # The ID of the document to update.
        requests: object[]      # Array of update requests (complex structure, use generic object for input)
        writeControl?: object   # Optional write control parameters

    GoogleDocsUpdateDocumentOutput:
        documentId: string      # The ID of the document that was updated.
        replies?: object[]      # Replies for each request in the batch update (complex structure)
        writeControl?: object   # Resulting write control information

    # Simplified Google Docs Document model (can be expanded later)
    GoogleDocsDocument:
        documentId?: string
        title?: string
        body?: object           # Represents the main body content structure (complex)
        headers?: object        # Document headers structure (complex)
        footers?: object        # Document footers structure (complex)
        footnotes?: object      # Document footnotes structure (complex)
        documentStyle?: object  # Document style information (complex)
        namedStyles?: object    # Named styles used (complex)
        revisionId?: string
        suggestionsViewMode?: string
        inlineObjects?: object  # Inline objects (complex)
        positionedObjects?: object # Positioned objects (complex)
        tabs?: object[]         # Added based on validation output
