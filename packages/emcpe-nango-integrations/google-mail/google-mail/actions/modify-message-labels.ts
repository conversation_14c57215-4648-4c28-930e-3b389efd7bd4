import type { NangoAction, GmailMessage, GmailModifyMessageLabelsInput } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailModifyMessageLabelsInput
): Promise<GmailMessage | NangoError> {
  const { messageId, addLabelIds, removeLabelIds } = input;

  try {
    if (!messageId) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Message ID is required to modify labels.',
        },
      };
    }

    if (
      (!addLabelIds || addLabelIds.length === 0) &&
      (!removeLabelIds || removeLabelIds.length === 0)
    ) {
      return {
        error: {
          status: 400,
          message:
            'Input validation failed: Either addLabelIds or removeLabelIds must be provided.',
        },
      };
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: `/gmail/v1/users/me/messages/${messageId}/modify`,
      data: {
        addLabelIds: addLabelIds,
        removeLabelIds: removeLabelIds,
      },
    });

    return response.data as GmailMessage;
  } catch (error: any) {
    console.error('Error modifying message labels:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while modifying message labels.';
    return { error: { status, message } };
  }
}
