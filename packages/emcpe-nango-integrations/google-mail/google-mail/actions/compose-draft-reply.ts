import type { GmailReplyDraftInput, GmailReplyDraftOutput, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailReplyDraftInput
): Promise<GmailReplyDraftOutput | NangoError> {
  const { sender, subject, body, threadId, messageId, references, date, replyBody } = input;

  try {
    const to = sender;
    const replySubject = subject.startsWith('Re:') ? subject : `Re: ${subject}`;
    const decodedBody = body; // Buffer.from(body, 'base64').toString('utf8');
    const decodedReply = replyBody; // Buffer.from(replyBody, 'base64').toString('utf8');

    const isHtml = decodedBody.trim().startsWith('<') && decodedBody.includes('</');

    const htmlBody = `
    <html>
      <body>
        ${decodedReply}
        <br><br>
        <div style="color: #888888">
          <p>On ${new Date(date).toLocaleString()}, ${sender} wrote:</p>
          <blockquote style="margin-left: 1em; padding-left: 1em; border-left: 1px solid #ccc;">
            ${
              isHtml
                ? decodedBody
                : `<pre>${decodedBody
                    .replace(/&/g, '&')
                    .replace(/</g, '<')
                    .replace(/>/g, '>')}</pre>`
            }
          </blockquote>
        </div>
      </body>
    </html>
  `;

    const plainTextBody = [
      decodedReply,
      '',
      `On ${new Date(date).toLocaleString()}, ${sender} wrote:`,
      ...(isHtml
        ? ['> [HTML content]']
        : decodedBody
          ? decodedBody.split('\n').map(line => `> ${line}`)
          : []),
    ]
      .filter(line => line !== '')
      .join('\n');

    const email = [
      `To: ${to}`,
      `In-Reply-To: ${messageId}`,
      `References: ${references ? `${references} ${messageId}` : messageId}`,
      `Subject: ${replySubject}`,
      'MIME-Version: 1.0',
      'Content-Type: multipart/alternative; boundary="boundary-string"',
      '',
      '--boundary-string',
      'Content-Type: text/plain; charset=UTF-8',
      '',
      plainTextBody,
      '',
      '--boundary-string',
      'Content-Type: text/html; charset=UTF-8',
      '',
      htmlBody,
      '--boundary-string--',
    ].join('\n');

    const base64EncodedEmail = Buffer.from(email)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    const draftResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/gmail/v1/users/me/drafts',
      data: {
        message: {
          raw: base64EncodedEmail,
          threadId,
        },
      },
      retries: 10,
    });

    if (draftResponse.status !== 200) {
      console.error('GitHub API Error:', draftResponse.status, draftResponse.data);
      return {
        error: {
          status: draftResponse.status,
          message: `GitHub API Error: Failed to add pull request review comment reply: ${draftResponse.status} ${JSON.stringify(draftResponse.data)}`,
        },
      };
    }

    return {
      id: draftResponse.data.id,
      threadId: draftResponse.data.message?.threadId || null,
    };
  } catch (error: any) {
    console.error('Error composing draft reply:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while composing the draft reply.';
    return { error: { status, message } };
  }
}
