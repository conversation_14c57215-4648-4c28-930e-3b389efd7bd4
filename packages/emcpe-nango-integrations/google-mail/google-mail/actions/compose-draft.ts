import type { GmailDraftInput, GmailDraftOutput, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailDraftInput
): Promise<GmailDraftOutput | NangoError> {
  const { recipient, subject, body } = input;

  try {
    if (!recipient || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(recipient)) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Invalid or missing recipient email address',
        },
      };
    }

    const email = [
      `To: ${recipient}`,
      `Subject: ${subject}`,
      'MIME-Version: 1.0',
      'Content-Type: text/plain; charset=UTF-8',
      '',
      body || '',
    ].join('\n');

    const base64EncodedEmail = Buffer.from(email)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    const draftResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/gmail/v1/users/me/drafts',
      data: {
        message: {
          raw: base64EncodedEmail,
        },
      },
      retries: 10,
    });

    return {
      id: draftResponse.data.id,
      threadId: draftResponse.data.message?.threadId || null,
    };
  } catch (error: any) {
    console.error('Error composing draft:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while composing the draft.';
    return { error: { status, message } };
  }
}
