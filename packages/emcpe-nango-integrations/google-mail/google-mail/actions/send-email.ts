import type { NangoAction, GmailSendEmailOutput, GmailSendEmailInput } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailSendEmailInput
): Promise<GmailSendEmailOutput | NangoError> {
  try {
    const { to, subject, body, from, cc, bcc, attachments } = input;

    if (!to || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(to)) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Invalid or missing recipient email address',
        },
      };
    }

    const headers = [`To: ${to}`, `Subject: ${subject}`, 'MIME-Version: 1.0'];

    if (from) headers.push(`From: ${from}`);
    if (cc) headers.push(`Cc: ${cc}`);
    if (bcc) headers.push(`Bcc: ${bcc}`);

    if (!attachments || attachments.length === 0) {
      headers.push('Content-Type: text/plain; charset=UTF-8');

      const email = [...headers, '', body || ''].join('\n');

      const base64EncodedEmail = Buffer.from(email)
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

      const response = await nango.proxy({
        method: 'POST',
        endpoint: '/gmail/v1/users/me/messages/send',
        data: {
          raw: base64EncodedEmail,
        },
        retries: 3,
      });

      return {
        id: response.data.id,
        threadId: response.data.threadId,
        labelIds: response.data.labelIds || [],
      };
    } else {
      return {
        error: { status: 500, message: 'Attachments are not supported in this implementation' },
      };
    }
  } catch (error: any) {
    console.error('Error sending email:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while sending the email.';
    return { error: { status, message } };
  }
}
