import type { NangoAction, GoogleSheetCreateOutput, GoogleSheetCreateInput } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GoogleSheetCreateInput
): Promise<GoogleSheetCreateOutput | NangoError> {
  const { title, sheets = [] } = input;

  try {
    if (!title) {
      return {
        error: { status: 400, message: 'Input validation failed: Spreadsheet title is required' },
      };
    }

    const spreadsheet: any = {
      properties: {
        title,
      },
    };

    if (sheets && sheets.length > 0) {
      spreadsheet.sheets = sheets.map((sheet, index) => {
        const sheetData: any = {
          properties: {
            title: sheet.title || `Sheet${index + 1}`,
            sheetId: index,
          },
        };

        if (sheet.data && sheet.data.rows && sheet.data.rows.length > 0) {
          const rows = sheet.data.rows.map(row => ({
            values: row.cells.map(cell => ({
              userEnteredValue: {
                stringValue: cell.toString(),
              },
            })),
          }));

          sheetData.data = [
            {
              rowData: rows,
              startRow: 0,
              startColumn: 0,
            },
          ];
        }

        return sheetData;
      });
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: 'https://sheets.googleapis.com/v4/spreadsheets',
      data: spreadsheet,
      retries: 3,
    });

    return {
      id: response.data.spreadsheetId,
      url: `https://docs.google.com/spreadsheets/d/${response.data.spreadsheetId}`,
      title: response.data.properties.title,
    };
  } catch (error: any) {
    console.error('Failed to create Google Sheet:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while creating the Google Sheet.';
    return { error: { status, message } };
  }
}
