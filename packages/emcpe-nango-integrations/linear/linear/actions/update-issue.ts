import type { NangoAction, LinearUpdateIssueInput, LinearIssue } from '../../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: LinearUpdateIssueInput
): Promise<LinearIssue | ActionError> {
  try {
    if (!input.issueId) {
      return {
        error: {
          status: 400,
          message: 'Missing required parameter: issueId is required',
        },
      };
    }

    const updateInput: Record<string, any> = {};

    if (input.title !== undefined) updateInput['title'] = input.title;
    if (input.description !== undefined) updateInput['description'] = input.description;
    if (input.stateId !== undefined) updateInput['stateId'] = input.stateId;
    if (input.assigneeId !== undefined) updateInput['assigneeId'] = input.assigneeId;
    if (input.priority !== undefined) updateInput['priority'] = input.priority;
    if (input.projectId !== undefined) updateInput['projectId'] = input.projectId;
    if (input.labelIds !== undefined) updateInput['labelIds'] = input.labelIds;

    if (Object.keys(updateInput).length === 0) {
      return {
        error: {
          status: 400,
          message: 'No update fields provided for the issue.',
        },
      };
    }

    const mutation = `
            mutation IssueUpdate($issueId: String!, $input: IssueUpdateInput!) {
                issueUpdate(
                    id: $issueId,
                    input: $input
                ) {
                    issue {
                        id title description number priority url createdAt updatedAt
                        state { id name color type }
                        assignee { id name email avatarUrl }
                        team { id name key }
                        project { id name icon color }
                        labels { nodes { id name color } }
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query: mutation,
        variables: {
          issueId: input.issueId,
          input: updateInput,
        },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error updating issue in Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.issueUpdate?.issue) {
      const status = response.status === 200 ? 500 : response.status || 500;
      const message = `Failed to update issue: Invalid response structure. Response: ${JSON.stringify(response.data)}`;
      console.error('Error updating issue in Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to update issue. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error updating issue in Linear:', message);
      return { error: { status, message } };
    }

    const issue = response.data.data.issueUpdate.issue;

    const result: LinearIssue = {
      id: issue.id,
      title: issue.title,
      description: issue.description || '',
      number: issue.number,
      priority: issue.priority,
      url: issue.url,
      createdAt: issue.createdAt,
      updatedAt: issue.updatedAt,
      state: {
        id: issue.state.id,
        name: issue.state.name,
        color: issue.state.color,
        type: issue.state.type,
      },
      team: {
        id: issue.team.id,
        name: issue.team.name,
        key: issue.team.key,
      },
      labels:
        issue.labels?.nodes.map((label: any) => ({
          id: label.id,
          name: label.name,
          color: label.color,
        })) || [],
    };

    if (issue.assignee) {
      result.assignee = {
        id: issue.assignee.id,
        name: issue.assignee.name,
        email: issue.assignee.email,
      };
    }

    if (issue.project) {
      result.project = {
        id: issue.project.id,
        name: issue.project.name,
        color: issue.project.color,
      };
    }

    return result;
  } catch (error: any) {
    console.error('Error updating issue in Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while updating the issue.';

    return {
      error: {
        status: status,
        message: `Failed to update issue: ${message}`,
      },
    };
  }
}
