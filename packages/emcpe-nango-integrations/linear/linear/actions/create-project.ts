import type { NangoAction, LinearProject, LinearCreateProjectInput } from '../../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: LinearCreateProjectInput
): Promise<LinearProject | ActionError> {
  try {
    const { name, description, teamIds, color } = input;

    if (!name) {
      return { error: { status: 400, message: 'Project name is required' } };
    }

    if (!teamIds || teamIds.length === 0) {
      return { error: { status: 400, message: 'At least one team ID is required' } };
    }

    const mutation = `
            mutation CreateProject($name: String!, $description: String, $teamIds: [String!]!, $color: String) {
                projectCreate(input: {
                    name: $name,
                    description: $description,
                    teamIds: $teamIds,
                    color: $color
                }) {
                    success
                    project {
                        id
                        name
                        description
                        url
                        color
                        state
                        url
                        createdAt
                        updatedAt
                        teams {
                            nodes {
                                id
                                name
                                key
                            }
                        }
                        lead {
                            id
                            name
                            email
                        }
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query: mutation,
        variables: {
          name,
          description,
          teamIds,
          color,
        },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error creating project in Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (
      !response.data?.data?.projectCreate?.success ||
      !response.data?.data?.projectCreate?.project
    ) {
      const status = response.status || 500;
      const message = `Failed to create project (success: ${response.data?.data?.projectCreate?.success}). Response: ${JSON.stringify(response.data)}`;
      console.error('Error creating project in Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to create project. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error creating project in Linear:', message);
      return { error: { status, message } };
    }

    const project = response.data.data.projectCreate.project;
    const teams = project.teams.nodes;

    const leadUser = project.lead
      ? {
          id: project.lead.id,
          name: project.lead.name,
          email: project.lead.email,
        }
      : null;

    const teamsList = teams.map((team: any) => ({
      id: team.id,
      name: team.name,
      key: team.key,
    }));

    const returnObj: LinearProject = {
      id: project.id,
      name: project.name,
      state: project.state,
      teams: teamsList,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
    };

    if (project.description !== null && project.description !== undefined) {
      returnObj.description = project.description;
    }
    if (project.url) {
      returnObj.url = project.url;
    }
    if (project.color) {
      returnObj.color = project.color;
    }
    if (leadUser) {
      returnObj.lead = leadUser;
    }

    return returnObj;
  } catch (error: any) {
    console.error('Error creating project in Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while creating the project.';

    return {
      error: {
        status: status,
        message: `Failed to create project: ${message}`,
      },
    };
  }
}
