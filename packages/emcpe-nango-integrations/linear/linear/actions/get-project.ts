import type { NangoAction, LinearProject, LinearProjectInput } from '../../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Get a specific project from Linear by ID
 *
 * @param nango - The Nango SDK instance
 * @param input - Input parameters including the project ID
 * @returns The project details or an error object
 */
export default async function runAction(
  nango: NangoAction,
  input: LinearProjectInput
): Promise<LinearProject | ActionError> {
  try {
    if (!input?.projectId) {
      return { error: { status: 400, message: 'Project ID is required' } };
    }

    const query = `
            query GetProject($projectId: String!) {
                project(id: $projectId) {
                    id
                    name
                    description
                    url
                    color
                    createdAt
                    updatedAt
                    state
                    lead {
                        id
                        name
                        email
                    }
                    teams {
                        nodes {
                            id
                            name
                            key
                        }
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query,
        variables: { projectId: input.projectId },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error fetching project from Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.project) {
      const status = response.status === 200 ? 404 : response.status || 404;
      const message = `Project with ID ${input.projectId} not found.`;
      console.error('Error fetching project from Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to fetch project. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error fetching project from Linear:', message);
      return { error: { status, message } };
    }

    const project = response.data.data.project;

    const result: LinearProject = {
      id: project.id,
      name: project.name,
      description: project.description,
      url: project.url,
      color: project.color,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      state: project.state,
      ...(project.lead
        ? {
            lead: {
              id: project.lead.id,
              name: project.lead.name,
              email: project.lead.email,
            },
          }
        : {}),
      teams: project.teams.nodes.map((team: any) => ({
        id: team.id,
        name: team.name,
        key: team.key,
      })),
    };
    return result;
  } catch (error: any) {
    console.error('Error fetching project from Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching the project.';

    return {
      error: {
        status: status,
        message: `Failed to fetch project: ${message}`,
      },
    };
  }
}
