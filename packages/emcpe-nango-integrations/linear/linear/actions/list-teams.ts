import type { LinearTeam, LinearTeamsInput, NangoAction } from '../../models';
type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

type PageInfo = {
  hasNextPage: boolean;
  endCursor?: string;
};

type PaginatedLinearTeamList = {
  teams: LinearTeam[];
  pageInfo: PageInfo;
};

/**
 * Lists teams from Linear.
 *
 * @param {NangoAction} nango - The Nango action object
 * @param {LinearTeamsInput} input - Optional pagination parameters
 * @returns {Promise<PaginatedLinearTeamList | ActionError>} The list of teams with pagination info or an error object
 */
export default async function runAction(
  nango: NangoAction,
  input?: LinearTeamsInput
): Promise<PaginatedLinearTeamList | ActionError> {
  try {
    const first = input?.first || 10;
    const after = input?.after ? `, after: "${input.after}"` : '';

    const query = `
            query ListTeams {
                teams(first: ${first}${after}) {
                    nodes {
                        id name key description color private createdAt updatedAt
                        members {
                            nodes {
                                id name email displayName avatarUrl
                            }
                        }
                    }
                    pageInfo { # Added pageInfo to query
                        hasNextPage
                        endCursor
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: { query },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error listing teams from Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.teams?.nodes || !response.data?.data?.teams?.pageInfo) {
      const status = response.status === 200 ? 500 : response.status || 500;
      const message = `Failed to list teams: Invalid response structure. Response: ${JSON.stringify(
        response.data
      )}`;
      console.error('Error listing teams from Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to list teams. Status: ${status}. Response: ${JSON.stringify(
        response.data
      )}`;
      console.error('Error listing teams from Linear:', message);
      return { error: { status, message } };
    }

    const teams: LinearTeam[] = response.data.data.teams.nodes.map((team: any) => ({
      id: team.id,
      name: team.name,
      key: team.key,
      description: team.description || '',
      color: team.color,
      private: team.private,
      createdAt: team.createdAt,
      updatedAt: team.updatedAt,
      members: team.members.nodes.map((member: any) => ({
        id: member.id,
        name: member.name,
        email: member.email,
        displayName: member.displayName,
        avatarUrl: member.avatarUrl,
      })),
    }));

    const pageInfo = response.data.data.teams.pageInfo;

    return {
      teams,
      pageInfo: {
        hasNextPage: pageInfo.hasNextPage,
        endCursor: pageInfo.endCursor || undefined,
      },
    };
  } catch (error: any) {
    console.error('Error listing teams from Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while listing teams.';

    return {
      error: {
        status: status,
        message: `Failed to list teams: ${message}`,
      },
    };
  }
}
