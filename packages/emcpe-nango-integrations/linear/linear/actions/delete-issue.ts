import type { NangoAction, LinearIssueInput, LinearDeleteIssueOutput } from '../../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: LinearIssueInput
): Promise<LinearDeleteIssueOutput | ActionError> {
  try {
    if (!input.issueId) {
      return {
        error: {
          status: 400,
          message: 'Missing required parameter: issueId is required',
        },
      };
    }

    const mutation = `
            mutation IssueDelete($issueId: String!) {
                issueDelete(id: $issueId) {
                    success
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query: mutation,
        variables: { issueId: input.issueId },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error deleting issue from Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.issueDelete?.success) {
      const status = response.status || 404;
      const message = `Failed to delete issue (success: ${response.data?.data?.issueDelete?.success}). Issue might not exist or another error occurred. Response: ${JSON.stringify(response.data)}`;
      console.error('Error deleting issue from Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to delete issue. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error deleting issue from Linear:', message);
      return { error: { status, message } };
    }

    const success = response.data.data.issueDelete.success;

    return {
      success,
      issueId: input.issueId,
    };
  } catch (error: any) {
    console.error('Error deleting issue from Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while deleting the issue.';

    return {
      error: {
        status: status,
        message: `Failed to delete issue: ${message}`,
      },
    };
  }
}
