import type { NangoAction, LinearTeam, LinearTeamInput } from '../../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: LinearTeamInput
): Promise<LinearTeam | ActionError> {
  try {
    const { teamId } = input;

    if (!teamId) {
      return { error: { status: 400, message: 'Team ID is required' } };
    }

    const query = `
            query Team($id: String!) {
                team(id: $id) {
                    id
                    name
                    key
                    description
                    color
                    createdAt
                    updatedAt
                    private
                    members {
                        nodes {
                            id
                            name
                            email
                            displayName
                            avatarUrl
                        }
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query,
        variables: {
          id: teamId,
        },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error fetching team from Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.team) {
      const status = response.status === 200 ? 404 : response.status || 404;
      const message = `Team with ID ${teamId} not found.`;
      console.error('Error fetching team from Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to fetch team. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error fetching team from Linear:', message);
      return { error: { status, message } };
    }

    const team = response.data.data.team;

    return {
      id: team.id,
      name: team.name,
      key: team.key,
      description: team.description,
      color: team.color,
      private: team.private,
      createdAt: team.createdAt,
      updatedAt: team.updatedAt,
      members: team.members.nodes.map((member: any) => ({
        id: member.id,
        name: member.name,
        email: member.email,
        displayName: member.displayName || undefined,
        avatarUrl: member.avatarUrl,
      })),
    };
  } catch (error: any) {
    console.error('Error fetching team from Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching the team.';

    return {
      error: {
        status: status,
        message: `Failed to fetch team: ${message}`,
      },
    };
  }
}
