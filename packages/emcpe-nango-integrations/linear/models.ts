export interface LinearIssuesInput {
  teamId?: string;
  projectId?: string;
  states?: string[];
  assigneeId?: string;
  priority?: number;
  sortBy?: string;
  sortOrder?: string;
  limit?: number;
  first?: number;
  after?: string;
}

export interface LinearIssueInput {
  issueId: string;
}

export interface LinearUser {
  id: string;
  name: string;
  email: string;
  displayName?: string;
  avatarUrl?: string | null;
}

export interface LinearState {
  id: string;
  name: string;
  color: string;
  type: string;
}

export interface LinearTeamBasic {
  id: string;
  name: string;
  key: string;
}

export interface LinearProjectBasic {
  id: string;
  name: string;
  icon?: string;
  color?: string;
}

export interface LinearLabel {
  id: string;
  name: string;
  color: string;
}

export interface LinearIssue {
  id: string;
  title: string;
  description?: string;
  number: number;
  priority: number;
  url: string;
  createdAt: string;
  updatedAt: string;
  state: LinearState;
  assignee?: LinearUser;
  team: LinearTeamBasic;
  project?: LinearProjectBasic;
  labels?: LinearLabel[];
}

export interface PageInfo {
  hasNextPage: boolean;
  endCursor?: string;
}

export interface LinearIssueList {
  issues: LinearIssue[];
  pageInfo?: PageInfo;
}

export interface LinearCreateIssueInput {
  teamId: string;
  title: string;
  description?: string;
  stateId?: string;
  assigneeId?: string;
  priority?: number;
  projectId?: string;
  labelIds?: string[];
}

export interface LinearUpdateIssueInput {
  issueId: string;
  title?: string;
  description?: string;
  stateId?: string;
  assigneeId?: string;
  priority?: number;
  projectId?: string;
  labelIds?: string[];
}

export interface LinearDeleteIssueOutput {
  success: boolean;
  issueId: string;
}

export interface LinearTeamInput {
  teamId: string;
}

export interface LinearTeam {
  id: string;
  name: string;
  key: string;
  description?: string | null;
  color?: string;
  private?: boolean;
  createdAt: string;
  updatedAt: string;
  members: LinearUser[];
}

export interface LinearTeamList {
  teams: LinearTeam[];
  pageInfo?: PageInfo;
}

export interface LinearTeamsInput {
  first?: number;
  after?: string;
}

export interface LinearProjectsInput {
  first?: number;
  after?: string;
}

export interface LinearProjectInput {
  projectId: string;
}

export interface LinearUserBasic {
  id: string;
  name: string;
  email?: string;
}

export interface LinearProject {
  id: string;
  name: string;
  description?: string;
  url?: string;
  color?: string;
  state: string;
  lead?: LinearUserBasic;
  teams: LinearTeamBasic[];
  createdAt: string;
  updatedAt: string;
}

export interface LinearProjectList {
  projects: LinearProject[];
  pageInfo?: PageInfo;
}

export interface LinearCreateProjectInput {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  teamIds: string[];
}

export interface LinearUpdateProjectInput {
  projectId: string;
  name?: string;
  description?: string;
  icon?: string;
  color?: string;
  state?: string;
  teamIds?: string[];
}

import type { Nango } from '@nangohq/node';
import type {
  AxiosInstance,
  AxiosInterceptorManager,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import type {
  ApiEndUser,
  DBSyncConfig,
  DBTeam,
  GetPublicIntegration,
  HTTP_METHOD,
  RunnerFlags,
} from '@nangohq/types';
import type { ZodSchema, SafeParseSuccess } from 'zod';

export declare const oldLevelToNewLevel: {
  readonly debug: 'debug';
  readonly info: 'info';
  readonly warn: 'warn';
  readonly error: 'error';
  readonly verbose: 'debug';
  readonly silly: 'debug';
  readonly http: 'info';
};
type LogLevel = 'info' | 'debug' | 'error' | 'warn' | 'http' | 'verbose' | 'silly';
interface Pagination {
  type: string;
  limit?: number;
  response_path?: string;
  limit_name_in_request: string;
  in_body?: boolean;
  on_page?: (paginationState: {
    nextPageParam?: string | number | undefined;
    response: AxiosResponse;
  }) => Promise<void>;
}
interface CursorPagination extends Pagination {
  cursor_path_in_response: string;
  cursor_name_in_request: string;
}
interface LinkPagination extends Pagination {
  link_rel_in_response_header?: string;
  link_path_in_response_body?: string;
}
interface OffsetPagination extends Pagination {
  offset_name_in_request: string;
  offset_start_value?: number;
  offset_calculation_method?: 'per-page' | 'by-response-size';
}
interface RetryHeaderConfig {
  at?: string;
  after?: string;
}
export interface ProxyConfiguration {
  endpoint: string;
  providerConfigKey?: string;
  connectionId?: string;
  method?:
    | 'GET'
    | 'POST'
    | 'PATCH'
    | 'PUT'
    | 'DELETE'
    | 'get'
    | 'post'
    | 'patch'
    | 'put'
    | 'delete';
  headers?: Record<string, string>;
  params?: string | Record<string, string | number>;
  data?: unknown;
  retries?: number;
  baseUrlOverride?: string;
  paginate?: Partial<CursorPagination> | Partial<LinkPagination> | Partial<OffsetPagination>;
  retryHeader?: RetryHeaderConfig;
  responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream' | undefined;
  retryOn?: number[] | null;
}
export interface AuthModes {
  OAuth1: 'OAUTH1';
  OAuth2: 'OAUTH2';
  OAuth2CC: 'OAUTH2_CC';
  Basic: 'BASIC';
  ApiKey: 'API_KEY';
  AppStore: 'APP_STORE';
  Custom: 'CUSTOM';
  App: 'APP';
  None: 'NONE';
  TBA: 'TBA';
  Tableau: 'TABLEAU';
  Jwt: 'JWT';
  Bill: 'BILL';
  TwoStep: 'TWO_STEP';
  Signature: 'SIGNATURE';
}
export type AuthModeType = AuthModes[keyof AuthModes];
interface OAuth1Token {
  oAuthToken: string;
  oAuthTokenSecret: string;
}
interface AppCredentials {
  type: AuthModes['App'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
}
interface AppStoreCredentials {
  type?: AuthModes['AppStore'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
  private_key: string;
}
interface BasicApiCredentials {
  type: AuthModes['Basic'];
  username: string;
  password: string;
}
interface ApiKeyCredentials {
  type: AuthModes['ApiKey'];
  apiKey: string;
}
interface CredentialsCommon<T = Record<string, any>> {
  type: AuthModeType;
  raw: T;
}
interface OAuth2Credentials extends CredentialsCommon {
  type: AuthModes['OAuth2'];
  access_token: string;
  refresh_token?: string;
  expires_at?: Date | undefined;
}
interface OAuth2ClientCredentials extends CredentialsCommon {
  type: AuthModes['OAuth2CC'];
  token: string;
  expires_at?: Date | undefined;
  client_id: string;
  client_secret: string;
}
interface OAuth1Credentials extends CredentialsCommon {
  type: AuthModes['OAuth1'];
  oauth_token: string;
  oauth_token_secret: string;
}
interface TbaCredentials {
  type: AuthModes['TBA'];
  token_id: string;
  token_secret: string;
  config_override: {
    client_id?: string;
    client_secret?: string;
  };
}
interface TableauCredentials extends CredentialsCommon {
  type: AuthModes['Tableau'];
  pat_name: string;
  pat_secret: string;
  content_url?: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface JwtCredentials {
  type: AuthModes['Jwt'];
  privateKeyId?: string;
  issuerId?: string;
  privateKey:
    | {
        id: string;
        secret: string;
      }
    | string;
  token?: string;
  expires_at?: Date | undefined;
}
interface BillCredentials extends CredentialsCommon {
  type: AuthModes['Bill'];
  username: string;
  password: string;
  organization_id: string;
  dev_key: string;
  session_id?: string;
  user_id?: string;
  expires_at?: Date | undefined;
}
interface TwoStepCredentials extends CredentialsCommon {
  type: AuthModes['TwoStep'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface SignatureCredentials {
  type: AuthModes['Signature'];
  username: string;
  password: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface CustomCredentials extends CredentialsCommon {
  type: AuthModes['Custom'];
}
type UnauthCredentials = Record<string, never>;
type AuthCredentials =
  | OAuth2Credentials
  | OAuth2ClientCredentials
  | OAuth1Credentials
  | BasicApiCredentials
  | ApiKeyCredentials
  | AppCredentials
  | AppStoreCredentials
  | UnauthCredentials
  | TbaCredentials
  | TableauCredentials
  | JwtCredentials
  | BillCredentials
  | TwoStepCredentials
  | SignatureCredentials
  | CustomCredentials;
type Metadata = Record<string, unknown>;
interface MetadataChangeResponse {
  metadata: Metadata;
  provider_config_key: string;
  connection_id: string | string[];
}
interface Connection {
  id: number;
  provider_config_key: string;
  connection_id: string;
  connection_config: Record<string, string>;
  created_at: string;
  updated_at: string;
  last_fetched_at: string;
  metadata: Record<string, unknown> | null;
  provider: string;
  errors: {
    type: string;
    log_id: string;
  }[];
  end_user: ApiEndUser | null;
  credentials: AuthCredentials;
}
export declare class ActionError<T = Record<string, unknown>> extends Error {
  type: string;
  payload?: Record<string, unknown>;
  constructor(payload?: T);
}
export interface NangoProps {
  scriptType: 'sync' | 'action' | 'webhook' | 'on-event';
  host?: string;
  secretKey: string;
  team?: Pick<DBTeam, 'id' | 'name'>;
  connectionId: string;
  environmentId: number;
  environmentName?: string;
  activityLogId?: string | undefined;
  providerConfigKey: string;
  provider: string;
  lastSyncDate?: Date;
  syncId?: string | undefined;
  nangoConnectionId?: number;
  syncJobId?: number | undefined;
  dryRun?: boolean;
  track_deletes?: boolean;
  attributes?: object | undefined;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]> | undefined;
  rawDeleteOutput?: Map<string, unknown[]> | undefined;
  stubbedMetadata?: Metadata | undefined;
  abortSignal?: AbortSignal;
  syncConfig: DBSyncConfig;
  runnerFlags: RunnerFlags;
  debug: boolean;
  startedAt: Date;
  endUser: {
    id: number;
    endUserId: string | null;
    orgId: string | null;
  } | null;
  axios?: {
    request?: AxiosInterceptorManager<AxiosRequestConfig>;
    response?: {
      onFulfilled: (value: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;
      onRejected: (value: unknown) => AxiosError | Promise<AxiosError>;
    };
  };
}
export interface EnvironmentVariable {
  name: string;
  value: string;
}
export declare const defaultPersistApi: AxiosInstance;
export declare class NangoAction {
  protected nango: Nango;
  private attributes;
  protected persistApi: AxiosInstance;
  activityLogId?: string | undefined;
  syncId?: string;
  nangoConnectionId?: number;
  environmentId: number;
  environmentName?: string;
  syncJobId?: number;
  dryRun?: boolean;
  abortSignal?: AbortSignal;
  syncConfig?: DBSyncConfig;
  runnerFlags: RunnerFlags;
  connectionId: string;
  providerConfigKey: string;
  provider?: string;
  ActionError: typeof ActionError;
  private memoizedConnections;
  private memoizedIntegration;
  constructor(
    config: NangoProps,
    {
      persistApi,
    }?: {
      persistApi: AxiosInstance;
    }
  );
  protected stringify(): string;
  private proxyConfig;
  protected throwIfAborted(): void;
  proxy<T = any>(config: ProxyConfiguration): Promise<AxiosResponse<T>>;
  get<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  post<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  put<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  patch<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  delete<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  getToken(): Promise<
    | string
    | OAuth1Token
    | OAuth2ClientCredentials
    | BasicApiCredentials
    | ApiKeyCredentials
    | AppCredentials
    | AppStoreCredentials
    | UnauthCredentials
    | CustomCredentials
    | TbaCredentials
    | TableauCredentials
    | JwtCredentials
    | BillCredentials
    | TwoStepCredentials
    | SignatureCredentials
  >;
  /**
   * Get current integration
   */
  getIntegration(
    queries?: GetPublicIntegration['Querystring']
  ): Promise<GetPublicIntegration['Success']['data']>;
  getConnection(
    providerConfigKeyOverride?: string,
    connectionIdOverride?: string
  ): Promise<Connection>;
  setMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  updateMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  /**
   * @deprecated please use setMetadata instead.
   */
  setFieldMapping(fieldMapping: Record<string, string>): Promise<AxiosResponse<object>>;
  getMetadata<T = Metadata>(): Promise<T>;
  getWebhookURL(): Promise<string | null | undefined>;
  /**
   * @deprecated please use getMetadata instead.
   */
  getFieldMapping(): Promise<Metadata>;
  /**
   * Log
   * @desc Log a message to the activity log which shows up in the Nango Dashboard
   * note that the last argument can be an object with a level property to specify the log level
   * @example
   * ```ts
   * await nango.log('This is a log message', { level: 'error' })
   * ```
   */
  log(
    message: any,
    options?:
      | {
          level?: LogLevel;
        }
      | {
          [key: string]: any;
          level?: never;
        }
  ): Promise<void>;
  log(
    message: string,
    ...args: [
      any,
      {
        level?: LogLevel;
      },
    ]
  ): Promise<void>;
  getEnvironmentVariables(): Promise<EnvironmentVariable[] | null>;
  getFlowAttributes<A = object>(): A | null;
  paginate<T = any>(config: ProxyConfiguration): AsyncGenerator<T[], undefined, void>;
  triggerAction<In = unknown, Out = object>(
    providerConfigKey: string,
    connectionId: string,
    actionName: string,
    input?: In
  ): Promise<Out>;
  zodValidateInput<T = any, Z = any>({
    zodSchema,
    input,
  }: {
    zodSchema: ZodSchema<Z>;
    input: T;
  }): Promise<SafeParseSuccess<Z>>;
  triggerSync(
    providerConfigKey: string,
    connectionId: string,
    syncName: string,
    fullResync?: boolean
  ): Promise<void | string>;
  startSync(
    providerConfigKey: string,
    syncs: (string | { name: string; variant: string })[],
    connectionId?: string
  ): Promise<void>;
  /**
   * Uncontrolled fetch is a regular fetch without retry or credentials injection.
   * Only use that method when you want to access resources that are unrelated to the current connection/provider.
   */
  uncontrolledFetch(options: {
    url: URL;
    method?: HTTP_METHOD;
    headers?: Record<string, string> | undefined;
    body?: string | null;
  }): Promise<Response>;
  private sendLogToPersist;
  private logAPICall;
}
export declare class NangoSync extends NangoAction {
  variant: string;
  lastSyncDate?: Date;
  track_deletes: boolean;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]>;
  rawDeleteOutput?: Map<string, unknown[]>;
  stubbedMetadata?: Metadata | undefined;
  private batchSize;
  constructor(config: NangoProps);
  /**
   * @deprecated please use batchSave
   */
  batchSend<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchSave<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchDelete<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchUpdate<T extends object>(results: T[], model: string): Promise<boolean | null>;
  getMetadata<T = Metadata>(): Promise<T>;
  setMergingStrategy(
    merging: { strategy: 'ignore_if_modified_after' | 'override' },
    model: string
  ): Promise<void>;
  getRecordsByIds<K = string | number, T = any>(ids: K[], model: string): Promise<Map<K, T>>;
}
/**
 * @internal
 *
 * This function will enable tracing on the SDK
 * It has been split from the actual code to avoid making the code too dirty and to easily enable/disable tracing if there is an issue with it
 */
export declare function instrumentSDK(rawNango: NangoAction | NangoSync): NangoAction | NangoSync;
export {};

export const NangoFlows = [
  {
    providerConfigKey: 'linear',
    syncs: [],
    actions: [
      {
        name: 'list-issues',
        type: 'action',
        description: 'Lists issues from Linear.',
        version: '',
        scopes: [],
        input: 'LinearIssuesInput',
        output: ['LinearIssueList'],
        usedModels: [
          'LinearIssueList',
          'LinearIssue',
          'LinearState',
          'LinearUser',
          'LinearTeamBasic',
          'LinearProjectBasic',
          'LinearLabel',
          'PageInfo',
          'LinearIssuesInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-issues',
        },
      },
      {
        name: 'get-issue',
        type: 'action',
        description: 'Gets a specific issue by ID.',
        version: '',
        scopes: [],
        input: 'LinearIssueInput',
        output: ['LinearIssue'],
        usedModels: [
          'LinearIssue',
          'LinearState',
          'LinearUser',
          'LinearTeamBasic',
          'LinearProjectBasic',
          'LinearLabel',
          'LinearIssueInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-issue',
        },
      },
      {
        name: 'create-issue',
        type: 'action',
        description: 'Creates a new issue in Linear.',
        version: '',
        scopes: [],
        input: 'LinearCreateIssueInput',
        output: ['LinearIssue'],
        usedModels: [
          'LinearIssue',
          'LinearState',
          'LinearUser',
          'LinearTeamBasic',
          'LinearProjectBasic',
          'LinearLabel',
          'LinearCreateIssueInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-issue',
        },
      },
      {
        name: 'update-issue',
        type: 'action',
        description: 'Updates an existing issue in Linear.',
        version: '',
        scopes: [],
        input: 'LinearUpdateIssueInput',
        output: ['LinearIssue'],
        usedModels: [
          'LinearIssue',
          'LinearState',
          'LinearUser',
          'LinearTeamBasic',
          'LinearProjectBasic',
          'LinearLabel',
          'LinearUpdateIssueInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-issue',
        },
      },
      {
        name: 'delete-issue',
        type: 'action',
        description: 'Deletes an issue in Linear.',
        version: '',
        scopes: [],
        input: 'LinearIssueInput',
        output: ['LinearDeleteIssueOutput'],
        usedModels: ['LinearDeleteIssueOutput', 'LinearIssueInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-issue',
        },
      },
      {
        name: 'list-projects',
        type: 'action',
        description: 'List all projects from Linear',
        version: '',
        scopes: [],
        input: 'LinearProjectsInput',
        output: ['LinearProjectList'],
        usedModels: [
          'LinearProjectList',
          'LinearProject',
          'LinearUserBasic',
          'LinearTeamBasic',
          'PageInfo',
          'LinearProjectsInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-projects',
        },
      },
      {
        name: 'get-project',
        type: 'action',
        description: 'Gets a specific project by ID.',
        version: '',
        scopes: [],
        input: 'LinearProjectInput',
        output: ['LinearProject'],
        usedModels: ['LinearProject', 'LinearUserBasic', 'LinearTeamBasic', 'LinearProjectInput'],
        endpoint: {
          method: 'GET',
          path: '/get-project',
        },
      },
      {
        name: 'create-project',
        type: 'action',
        description: 'Creates a new project in Linear.',
        version: '',
        scopes: [],
        input: 'LinearCreateProjectInput',
        output: ['LinearProject'],
        usedModels: [
          'LinearProject',
          'LinearUserBasic',
          'LinearTeamBasic',
          'LinearCreateProjectInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-project',
        },
      },
      {
        name: 'update-project',
        type: 'action',
        description: 'Updates an existing project in Linear.',
        version: '',
        scopes: [],
        input: 'LinearUpdateProjectInput',
        output: ['LinearProject'],
        usedModels: [
          'LinearProject',
          'LinearUserBasic',
          'LinearTeamBasic',
          'LinearUpdateProjectInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-project',
        },
      },
      {
        name: 'list-teams',
        type: 'action',
        description: 'Lists teams from Linear.',
        version: '',
        scopes: [],
        input: 'LinearTeamsInput',
        output: ['LinearTeamList'],
        usedModels: ['LinearTeamList', 'LinearTeam', 'LinearUser', 'PageInfo', 'LinearTeamsInput'],
        endpoint: {
          method: 'GET',
          path: '/list-teams',
        },
      },
      {
        name: 'get-team',
        type: 'action',
        description: 'Gets a specific team by ID.',
        version: '',
        scopes: [],
        input: 'LinearTeamInput',
        output: ['LinearTeam'],
        usedModels: ['LinearTeam', 'LinearUser', 'LinearTeamInput'],
        endpoint: {
          method: 'GET',
          path: '/get-team',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
] as const;
