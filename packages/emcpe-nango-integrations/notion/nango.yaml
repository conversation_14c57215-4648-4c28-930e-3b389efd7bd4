integrations:
    notion:
        actions:
            search:
                endpoint: POST /search # Simplified endpoint for display
                description: Searches pages and databases in Notion. IMPORTANT - Use "" to search for everything.
                input: NotionSearchInput
                output: NotionSearchOutput
                scopes: [] # Notion uses API keys, not OAuth scopes managed by <PERSON><PERSON> here
            get-page:
                endpoint: GET /pages/:pageId # Simplified endpoint for display
                description: Retrieves a specific Notion Page object by its ID.
                input: NotionGetPageInput
                output: NotionPageOrDatabase # Reusing the model from search, may need refinement
                scopes: []
            get-database:
                endpoint: GET /databases/:databaseId # Simplified endpoint for display
                description: Retrieves a specific Notion Database object by its ID.
                input: NotionGetDatabaseInput
                output: NotionDatabase
                scopes: []
            query-database:
                endpoint: POST /databases/:databaseId/query # Simplified endpoint for display
                description: Queries a Notion database for pages, with optional filters and sorts.
                input: NotionQueryDatabaseInput
                output: NotionQueryDatabaseOutput # Similar to search output
                scopes: []
            create-database:
                endpoint: POST /databases # Simplified endpoint for display
                description: Creates a new Notion database as a subpage of a specified page.
                input: NotionCreateDatabaseInput
                output: NotionDatabase # Returns the created database object
                scopes: []
            create-page:
                endpoint: POST /pages # Simplified endpoint for display
                description: Creates a new Notion page.
                input: NotionCreatePageInput
                output: NotionPageOrDatabase # Returns the created page object
                scopes: []
            update-page:
                endpoint: PATCH /pages/:pageId # Simplified endpoint for display
                description: Updates properties of an existing Notion page. ALSO USED TO "delete" a page, set archive to true.
                input: NotionUpdatePageInput
                output: NotionPageOrDatabase # Returns the updated page object
                scopes: []
            update-database:
                endpoint: PATCH /databases/:databaseId # Simplified endpoint for display
                description: Updates properties of an existing Notion database. ALSO USED TO "delete" a database, set archive to true.
                input: NotionUpdateDatabaseInput
                output: NotionDatabase # Returns the updated database object
                scopes: []

models:
    # Notion Models
    NotionSort:
        direction?: string      # "ascending" or "descending"
        timestamp?: string      # "last_edited_time"

    NotionFilter:
        value?: string          # "page" or "database"
        property?: string       # "object"

    NotionSearchInput:
        query?: string          # The text query to search against
        sort?: NotionSort       # Sort configuration
        filter?: NotionFilter   # Filter configuration
        start_cursor?: string   # Pagination cursor
        page_size?: number      # Number of results per page (default 100)

    NotionGetPageInput:
        pageId: string          # The ID of the page to retrieve

    NotionGetDatabaseInput:
        databaseId: string      # The ID of the database to retrieve

    # Input model for creating a database
    NotionCreateDatabaseInput:
        parentId: string        # ID of the parent page for the new database
        title: NotionRichText[] # Title for the new database (using RichText array)
        properties: object      # Schema for the database properties (use generic object for input)

    # Input model for querying a database
    NotionQueryDatabaseInput:
        databaseId: string      # The ID of the database to query
        filter?: object         # Notion filter object structure (complex, keep as object for now)
        sorts?: object[]        # Array of Notion sort objects (complex, keep as object for now)
        start_cursor?: string   # Pagination cursor
        page_size?: number      # Number of results per page (default 100)

    # Input model for creating a page
    NotionCreatePageInput:
        parentId: string        # ID of the parent page or database
        parentType?: string     # Optional: 'page' or 'database'. Defaults to 'page' if omitted.
        properties: object      # Page properties (use generic object for input flexibility)
        children?: object[]     # Optional array of block objects to add as content

    # Input model for updating a page
    NotionUpdatePageInput:
        pageId: string          # ID of the page to update
        properties?: object     # Page properties to update (use generic object)
        archived?: boolean      # Optional: Set archive status
        icon?: object | null    # Optional: Page icon object
        cover?: object | null   # Optional: Page cover object

    # Input model for updating a database
    NotionUpdateDatabaseInput:
        databaseId: string          # ID of the database to update
        title?: NotionRichText[]    # Optional: New title for the database
        description?: NotionRichText[] # Optional: New description for the database
        properties?: object         # Optional: Database properties to update (use generic object)
        archived?: boolean          # Optional: Set archive status

    # Defining more representative models for Notion results, avoiding 'object' type.
    NotionUserReference:
        object: string          # "user"
        id: string

    NotionParentReference:
        type: string            # e.g., "workspace", "page_id", "database_id"
        workspace?: boolean
        page_id?: string
        database_id?: string

    # Placeholder models for complex nested structures
    NotionCover:
        _placeholder?: string # Define actual structure if needed later

    NotionIcon:
        _placeholder?: string # Define actual structure if needed later

    # Placeholder for nested text object within RichText
    NotionRichTextContent:
        content?: string
        link?: object | null

    # Placeholder for nested annotations object within RichText
    NotionRichTextAnnotations:
        bold?: boolean
        italic?: boolean
        strikethrough?: boolean
        underline?: boolean
        code?: boolean
        color?: string

    # Define the RichText object structure
    NotionRichText:
        type?: string                   # e.g., "text"
        text?: NotionRichTextContent
        annotations?: NotionRichTextAnnotations
        plain_text?: string
        href?: string | null

    # Define the structure for a Title property based on observed output
    NotionTitleProperty:
        id?: string
        type?: string           # Should be "title"
        title?: NotionRichText[] # Array of rich text objects

    # Define NotionProperties with the common 'title' property
    NotionProperties:
        title?: NotionTitleProperty # Add other common properties if needed

    NotionGenericObjectPlaceholder:
         _placeholder?: string # For fields like page_or_database

    NotionPageOrDatabase:
        object: string          # "page" or "database"
        id: string
        created_time?: string
        last_edited_time?: string
        created_by?: NotionUserReference
        last_edited_by?: NotionUserReference
        cover?: NotionCover | null
        icon?: NotionIcon | null
        parent?: NotionParentReference
        archived?: boolean
        in_trash?: boolean
        properties?: NotionProperties # Use the defined NotionProperties model
        url?: string
        public_url?: string | null
        # Database specific fields (optional)
        title?: NotionRichText[]        # Use placeholder model for rich text array
        description?: NotionRichText[]  # Use placeholder model for rich text array
        is_inline?: boolean
        request_id?: string     # Added based on validation output

    # Model for the full Database object (distinct from Page)
    NotionDatabase:
        object: string          # Should be "database"
        id: string
        created_time?: string
        last_edited_time?: string
        created_by?: NotionUserReference
        last_edited_by?: NotionUserReference
        icon?: NotionIcon | null
        cover?: NotionCover | null
        parent?: NotionParentReference
        archived?: boolean
        in_trash?: boolean      # Added based on general Notion object structure
        properties: NotionProperties # Database properties schema
        url?: string            # Added based on general Notion object structure
        public_url?: string | null # Added based on general Notion object structure
        title?: NotionRichText[]
        description?: NotionRichText[]
        is_inline?: boolean
        request_id?: string     # Added based on validation output

    # Output model for database query results (similar to search)
    NotionQueryDatabaseOutput:
        object: string          # Should be "list"
        results: NotionPageOrDatabase[] # Array of Page objects from the database
        next_cursor?: string | null # Cursor for next page
        has_more: boolean       # Whether there are more results
        type?: string           # Should be "page" or "page_or_database" depending on Notion version/context
        page?: NotionGenericObjectPlaceholder # Placeholder from Notion docs (using generic placeholder)
        request_id?: string     # Added based on validation output

    NotionSearchOutput:
        object: string          # Should be "list"
        results: NotionPageOrDatabase[] # Array of Page or Database objects
        next_cursor?: string | null # Cursor for next page
        has_more: boolean       # Whether there are more results
        type?: string           # Should be "page_or_database"
        page_or_database?: NotionGenericObjectPlaceholder # Use placeholder model
        request_id?: string     # Added based on validation output
