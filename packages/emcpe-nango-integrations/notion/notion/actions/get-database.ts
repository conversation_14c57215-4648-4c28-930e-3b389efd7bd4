import type { NangoAction, NotionGetDatabaseInput, NotionDatabase } from '../../models';

const NOTION_API_VERSION = '2022-06-28';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: NotionGetDatabaseInput
): Promise<NotionDatabase | ErrorResponse> {
  const { databaseId } = input;

  if (!databaseId) {
    return { error: { status: 400, message: 'Database ID is required.' } };
  }

  try {
    const config = {
      method: 'GET' as const,
      endpoint: `/v1/databases/${databaseId}`,
      headers: {
        'Notion-Version': NOTION_API_VERSION,
      },
      retries: 3,
    };

    const response = await nango.proxy<NotionDatabase>(config);

    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      `Unknown error getting Notion database ${databaseId}`;
    const status = error.response?.status ?? 500;
    await nango.log(
      `Error getting Notion database ${databaseId}: Status ${status}, Message: ${errorMessage}`
    );
    return { error: { status: status, message: errorMessage } };
  }
}
