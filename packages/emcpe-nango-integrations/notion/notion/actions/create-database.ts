import type { NangoAction, NotionCreateDatabaseInput, NotionDatabase } from '../../models';

const NOTION_API_VERSION = '2022-06-28';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: NotionCreateDatabaseInput
): Promise<NotionDatabase | ErrorResponse> {
  const { parentId, title, properties } = input;

  if (!parentId) {
    return { error: { status: 400, message: 'Parent Page ID (parentId) is required.' } };
  }
  if (!title || title.length === 0) {
    return { error: { status: 400, message: 'Database title is required.' } };
  }
  if (!properties || typeof properties !== 'object' || Object.keys(properties).length === 0) {
    return {
      error: {
        status: 400,
        message: 'Database properties schema is required and must be a non-empty object.',
      },
    };
  }

  const requestBody = {
    parent: { page_id: parentId },
    title: title,
    properties: properties,
  };

  try {
    const config = {
      method: 'POST' as const,
      endpoint: `/v1/databases`,
      data: requestBody,
      headers: {
        'Content-Type': 'application/json',
        'Notion-Version': NOTION_API_VERSION,
      },
      retries: 3,
    };

    const response = await nango.proxy<NotionDatabase>(config);

    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message || error.message || 'Unknown error creating Notion database';
    const status = error.response?.status ?? 500;
    await nango.log(`Error creating Notion database: Status ${status}, Message: ${errorMessage}`);
    return { error: { status: status, message: errorMessage } };
  }
}
