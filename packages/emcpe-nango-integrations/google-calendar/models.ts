// ---------------------------
// This file was generated by <PERSON><PERSON> (v0.58.6)
// It's recommended to version this file
// https://nango.dev
// ---------------------------

// ------ Models
export interface GoogleCalendarList {
  calendars: {
    id: string;
    summary: string;
    description?: string;
    location?: string;
    timeZone: string;
    accessRole: string;
    primary: boolean;
    backgroundColor?: string;
    foregroundColor?: string;
  }[];
  nextPageToken?: string;
}

export interface GoogleCalendarEventsInput {
  calendarId: string;
  timeMin?: string;
  timeMax?: string;
  maxResults?: number;
  pageToken?: string;
  orderBy?: string;
  q?: string;
  singleEvents?: boolean;
  timeZone?: string;
}

export interface GoogleCalendarEventList {
  events: {
    id: string;
    summary: string;
    description?: string;
    location?: string;
    start: { dateTime?: string; date?: string; timeZone?: string };
    end: { dateTime?: string; date?: string; timeZone?: string };
    status: string;
    creator: { id?: string; email: string; displayName?: string; self?: boolean };
    organizer: { id?: string; email: string; displayName?: string; self?: boolean };
    attendees?: {
      id?: string;
      email: string;
      displayName?: string;
      responseStatus: string;
      optional?: boolean;
      resource?: boolean;
      comment?: string;
    }[];
    htmlLink: string;
    created: string;
    updated: string;
    recurrence?: string[];
  }[];
  nextPageToken?: string;
  timeZone: string;
}

export interface GoogleCalendarEventInput {
  summary: string;
  description?: string;
  location?: string;
  start: string;
  end: string;
  timeZone?: string;
  attendees?: string[];
}

export interface GoogleCalendarEventDateTimeInput {
  dateTime?: string;
  date?: string;
  timeZone?: string;
}

export interface GoogleCalendarAttendeeInput {
  id?: string;
  email: string;
  displayName?: string;
  responseStatus: string;
  optional?: boolean;
  resource?: boolean;
  comment?: string;
}

export interface GoogleCalendarEventUpdateInput {
  calendarId: string;
  eventId: string;
  sendUpdates?: string;
  summary?: string;
  description?: string;
  location?: string;
  start?: string;
  end?: string;
  timeZone?: string;
  attendees?: GoogleCalendarAttendeeInput[];
}

export interface GoogleCalendarEventOutput {
  kind: string;
  etag: string;
  id: string;
  status: string;
  htmlLink: string;
  created: string;
  updated: string;
  summary: string;
  description?: string;
  location?: string;
  creator: { email: string; self?: boolean };
  organizer: { email: string; self?: boolean };
  start: { dateTime?: string; date?: string; timeZone?: string };
  end: { dateTime?: string; date?: string; timeZone?: string };
  iCalUID: string;
  sequence: number;
  reminders: { useDefault: boolean };
  eventType: string;
  attendees?: {
    email: string;
    responseStatus?: string;
    displayName?: string;
    optional?: boolean;
    resource?: boolean;
    comment?: string;
  }[];
  recurrence?: string[];
  anyoneCanAddSelf?: boolean;
  guestsCanInviteOthers?: boolean;
  guestsCanSeeOtherGuests?: boolean;
  guestsCanModify?: boolean;
  privateCopy?: boolean;
  transparency?: string;
  visibility?: string;
  colorId?: string;
  attachments?: { fileUrl: string; title: string; mimeType: string; fileId: string }[];
}

export interface GoogleCalendarEventDeleteInput {
  calendarId: string;
  eventId: string;
  sendUpdates?: string;
}
// ------ /Models

// ------ SDK

import type { Nango } from '@nangohq/node';
import type {
  AxiosInstance,
  AxiosInterceptorManager,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import type {
  ApiEndUser,
  DBSyncConfig,
  DBTeam,
  GetPublicIntegration,
  HTTP_METHOD,
  RunnerFlags,
  PostPublicTrigger,
} from '@nangohq/types';
import type { ZodSchema, SafeParseSuccess } from 'zod';

export declare const oldLevelToNewLevel: {
  readonly debug: 'debug';
  readonly info: 'info';
  readonly warn: 'warn';
  readonly error: 'error';
  readonly verbose: 'debug';
  readonly silly: 'debug';
  readonly http: 'info';
};
type LogLevel = 'info' | 'debug' | 'error' | 'warn' | 'http' | 'verbose' | 'silly';
interface Pagination {
  type: string;
  limit?: number;
  response_path?: string;
  limit_name_in_request: string;
  in_body?: boolean;
  on_page?: (paginationState: {
    nextPageParam?: string | number | undefined;
    response: AxiosResponse;
  }) => Promise<void>;
}
interface CursorPagination extends Pagination {
  cursor_path_in_response: string;
  cursor_name_in_request: string;
}
interface LinkPagination extends Pagination {
  link_rel_in_response_header?: string;
  link_path_in_response_body?: string;
}
interface OffsetPagination extends Pagination {
  offset_name_in_request: string;
  offset_start_value?: number;
  offset_calculation_method?: 'per-page' | 'by-response-size';
}
interface RetryHeaderConfig {
  at?: string;
  after?: string;
}
export interface ProxyConfiguration {
  endpoint: string;
  providerConfigKey?: string;
  connectionId?: string;
  method?:
    | 'GET'
    | 'POST'
    | 'PATCH'
    | 'PUT'
    | 'DELETE'
    | 'get'
    | 'post'
    | 'patch'
    | 'put'
    | 'delete';
  headers?: Record<string, string>;
  params?: string | Record<string, string | number>;
  data?: unknown;
  retries?: number;
  baseUrlOverride?: string;
  paginate?: Partial<CursorPagination> | Partial<LinkPagination> | Partial<OffsetPagination>;
  retryHeader?: RetryHeaderConfig;
  responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream' | undefined;
  retryOn?: number[] | null;
}
export interface AuthModes {
  OAuth1: 'OAUTH1';
  OAuth2: 'OAUTH2';
  OAuth2CC: 'OAUTH2_CC';
  Basic: 'BASIC';
  ApiKey: 'API_KEY';
  AppStore: 'APP_STORE';
  Custom: 'CUSTOM';
  App: 'APP';
  None: 'NONE';
  TBA: 'TBA';
  Tableau: 'TABLEAU';
  Jwt: 'JWT';
  Bill: 'BILL';
  TwoStep: 'TWO_STEP';
  Signature: 'SIGNATURE';
}
export type AuthModeType = AuthModes[keyof AuthModes];
interface OAuth1Token {
  oAuthToken: string;
  oAuthTokenSecret: string;
}
interface AppCredentials {
  type: AuthModes['App'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
}
interface AppStoreCredentials {
  type?: AuthModes['AppStore'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
  private_key: string;
}
interface BasicApiCredentials {
  type: AuthModes['Basic'];
  username: string;
  password: string;
}
interface ApiKeyCredentials {
  type: AuthModes['ApiKey'];
  apiKey: string;
}
interface CredentialsCommon<T = Record<string, any>> {
  type: AuthModeType;
  raw: T;
}
interface OAuth2Credentials extends CredentialsCommon {
  type: AuthModes['OAuth2'];
  access_token: string;
  refresh_token?: string;
  expires_at?: Date | undefined;
}
interface OAuth2ClientCredentials extends CredentialsCommon {
  type: AuthModes['OAuth2CC'];
  token: string;
  expires_at?: Date | undefined;
  client_id: string;
  client_secret: string;
}
interface OAuth1Credentials extends CredentialsCommon {
  type: AuthModes['OAuth1'];
  oauth_token: string;
  oauth_token_secret: string;
}
interface TbaCredentials {
  type: AuthModes['TBA'];
  token_id: string;
  token_secret: string;
  config_override: {
    client_id?: string;
    client_secret?: string;
  };
}
interface TableauCredentials extends CredentialsCommon {
  type: AuthModes['Tableau'];
  pat_name: string;
  pat_secret: string;
  content_url?: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface JwtCredentials {
  type: AuthModes['Jwt'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface BillCredentials extends CredentialsCommon {
  type: AuthModes['Bill'];
  username: string;
  password: string;
  organization_id: string;
  dev_key: string;
  session_id?: string;
  user_id?: string;
  expires_at?: Date | undefined;
}
interface TwoStepCredentials extends CredentialsCommon {
  type: AuthModes['TwoStep'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface SignatureCredentials {
  type: AuthModes['Signature'];
  username: string;
  password: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface CustomCredentials extends CredentialsCommon {
  type: AuthModes['Custom'];
}
type UnauthCredentials = Record<string, never>;
type AuthCredentials =
  | OAuth2Credentials
  | OAuth2ClientCredentials
  | OAuth1Credentials
  | BasicApiCredentials
  | ApiKeyCredentials
  | AppCredentials
  | AppStoreCredentials
  | UnauthCredentials
  | TbaCredentials
  | TableauCredentials
  | JwtCredentials
  | BillCredentials
  | TwoStepCredentials
  | SignatureCredentials
  | CustomCredentials;
type Metadata = Record<string, unknown>;
interface MetadataChangeResponse {
  metadata: Metadata;
  provider_config_key: string;
  connection_id: string | string[];
}
interface Connection {
  id: number;
  provider_config_key: string;
  connection_id: string;
  connection_config: Record<string, string>;
  created_at: string;
  updated_at: string;
  last_fetched_at: string;
  metadata: Record<string, unknown> | null;
  provider: string;
  errors: {
    type: string;
    log_id: string;
  }[];
  end_user: ApiEndUser | null;
  credentials: AuthCredentials;
}
export declare class ActionError<T = Record<string, unknown>> extends Error {
  type: string;
  payload?: Record<string, unknown>;
  constructor(payload?: T);
}
export interface NangoProps {
  scriptType: 'sync' | 'action' | 'webhook' | 'on-event';
  host?: string;
  secretKey: string;
  team?: Pick<DBTeam, 'id' | 'name'>;
  connectionId: string;
  environmentId: number;
  environmentName?: string;
  activityLogId?: string | undefined;
  providerConfigKey: string;
  provider: string;
  lastSyncDate?: Date;
  syncId?: string | undefined;
  nangoConnectionId?: number;
  syncJobId?: number | undefined;
  dryRun?: boolean;
  track_deletes?: boolean;
  attributes?: object | undefined;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]> | undefined;
  rawDeleteOutput?: Map<string, unknown[]> | undefined;
  stubbedMetadata?: Metadata | undefined;
  abortSignal?: AbortSignal;
  syncConfig: DBSyncConfig;
  runnerFlags: RunnerFlags;
  debug: boolean;
  startedAt: Date;
  endUser: {
    id: number;
    endUserId: string | null;
    orgId: string | null;
  } | null;
  axios?: {
    request?: AxiosInterceptorManager<AxiosRequestConfig>;
    response?: {
      onFulfilled: (value: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;
      onRejected: (value: unknown) => AxiosError | Promise<AxiosError>;
    };
  };
}
export interface EnvironmentVariable {
  name: string;
  value: string;
}
export declare const defaultPersistApi: AxiosInstance;
export declare class NangoAction {
  protected nango: Nango;
  private attributes;
  protected persistApi: AxiosInstance;
  activityLogId?: string | undefined;
  syncId?: string;
  nangoConnectionId?: number;
  environmentId: number;
  environmentName?: string;
  syncJobId?: number;
  dryRun?: boolean;
  abortSignal?: AbortSignal;
  syncConfig?: DBSyncConfig;
  runnerFlags: RunnerFlags;
  connectionId: string;
  providerConfigKey: string;
  provider?: string;
  ActionError: typeof ActionError;
  private memoizedConnections;
  private memoizedIntegration;
  constructor(
    config: NangoProps,
    {
      persistApi,
    }?: {
      persistApi: AxiosInstance;
    }
  );
  protected stringify(): string;
  private proxyConfig;
  protected throwIfAborted(): void;
  proxy<T = any>(config: ProxyConfiguration): Promise<AxiosResponse<T>>;
  get<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  post<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  put<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  patch<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  delete<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  getToken(): Promise<
    | string
    | OAuth1Token
    | OAuth2ClientCredentials
    | BasicApiCredentials
    | ApiKeyCredentials
    | AppCredentials
    | AppStoreCredentials
    | UnauthCredentials
    | CustomCredentials
    | TbaCredentials
    | TableauCredentials
    | JwtCredentials
    | BillCredentials
    | TwoStepCredentials
    | SignatureCredentials
  >;
  /**
   * Get current integration
   */
  getIntegration(
    queries?: GetPublicIntegration['Querystring']
  ): Promise<GetPublicIntegration['Success']['data']>;
  getConnection(
    providerConfigKeyOverride?: string,
    connectionIdOverride?: string
  ): Promise<Connection>;
  setMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  updateMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  /**
   * @deprecated please use setMetadata instead.
   */
  setFieldMapping(fieldMapping: Record<string, string>): Promise<AxiosResponse<object>>;
  getMetadata<T = Metadata>(): Promise<T>;
  getWebhookURL(): Promise<string | null | undefined>;
  /**
   * @deprecated please use getMetadata instead.
   */
  getFieldMapping(): Promise<Metadata>;
  /**
   * Log
   * @desc Log a message to the activity log which shows up in the Nango Dashboard
   * note that the last argument can be an object with a level property to specify the log level
   * @example
   * ```ts
   * await nango.log('This is a log message', { level: 'error' })
   * ```
   */
  log(
    message: any,
    options?:
      | {
          level?: LogLevel;
        }
      | {
          [key: string]: any;
          level?: never;
        }
  ): Promise<void>;
  log(
    message: string,
    ...args: [
      any,
      {
        level?: LogLevel;
      },
    ]
  ): Promise<void>;
  getEnvironmentVariables(): Promise<EnvironmentVariable[] | null>;
  getFlowAttributes<A = object>(): A | null;
  paginate<T = any>(config: ProxyConfiguration): AsyncGenerator<T[], undefined, void>;
  triggerAction<In = unknown, Out = object>(
    providerConfigKey: string,
    connectionId: string,
    actionName: string,
    input?: In
  ): Promise<Out>;
  zodValidateInput<T = any, Z = any>({
    zodSchema,
    input,
  }: {
    zodSchema: ZodSchema<Z>;
    input: T;
  }): Promise<SafeParseSuccess<Z>>;
  triggerSync(
    providerConfigKey: string,
    connectionId: string,
    sync: string | { name: string; variant: string },
    syncMode?: PostPublicTrigger['Body']['sync_mode'] | boolean
  ): Promise<void | string>;
  startSync(
    providerConfigKey: string,
    syncs: (string | { name: string; variant: string })[],
    connectionId?: string
  ): Promise<void>;
  /**
   * Uncontrolled fetch is a regular fetch without retry or credentials injection.
   * Only use that method when you want to access resources that are unrelated to the current connection/provider.
   */
  uncontrolledFetch(options: {
    url: URL;
    method?: HTTP_METHOD;
    headers?: Record<string, string> | undefined;
    body?: string | null;
  }): Promise<Response>;
  tryAcquireLock(props: { key: string; ttlMs: number }): Promise<boolean>;
  releaseLock(props: { key: string }): Promise<boolean>;
  private sendLogToPersist;
  private logAPICall;
}
export declare class NangoSync extends NangoAction {
  variant: string;
  lastSyncDate?: Date;
  track_deletes: boolean;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]>;
  rawDeleteOutput?: Map<string, unknown[]>;
  stubbedMetadata?: Metadata | undefined;
  private batchSize;
  constructor(config: NangoProps);
  /**
   * @deprecated please use batchSave
   */
  batchSend<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchSave<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchDelete<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchUpdate<T extends object>(results: T[], model: string): Promise<boolean | null>;
  getMetadata<T = Metadata>(): Promise<T>;
  setMergingStrategy(
    merging: { strategy: 'ignore_if_modified_after' | 'override' },
    model: string
  ): Promise<void>;
  getRecordsByIds<K = string | number, T = any>(ids: K[], model: string): Promise<Map<K, T>>;
}
/**
 * @internal
 *
 * This function will enable tracing on the SDK
 * It has been split from the actual code to avoid making the code too dirty and to easily enable/disable tracing if there is an issue with it
 */
export declare function instrumentSDK(rawNango: NangoAction | NangoSync): NangoAction | NangoSync;
export {};

// ------ /SDK

// ------ Flows
export const NangoFlows = [
  {
    providerConfigKey: 'google-calendar',
    syncs: [],
    actions: [
      {
        name: 'create-event',
        type: 'action',
        description:
          'Creates a new event in Google Calendar. Can either be full-day or time-based.\n- summary: Event title / name.\n- description: Contains e.g. the agenda or specifics of the meeting. Can contain HTML.\n- location: Free form text.\n- start: Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.\n- end: Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.\n- timeZone: An IANA Time Zone e.g. (Area/City)\n- attendees: A list of attendee email addresses.',
        version: '',
        scopes: [
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events',
        ],
        input: 'GoogleCalendarEventInput',
        output: ['GoogleCalendarEventOutput'],
        usedModels: ['GoogleCalendarEventOutput', 'GoogleCalendarEventInput'],
        endpoint: {
          method: 'POST',
          path: '/create-event',
        },
      },
      {
        name: 'update-event',
        type: 'action',
        description:
          'Updates an event in Google Calendar.\n- calendarId: Calendar identifier. Use "primary" unless otherwise advised.\n- eventId: Event identifier.\n- sendUpdates: Whether to send notifications about the event update.\n- summary: Event title / name.\n- description: Contains e.g. the agenda or specifics of the meeting. Can contain HTML.\n- location: Free form text.\n- start: Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.\n- end: Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.\n- timeZone: An IANA Time Zone e.g. (Area/City)\n- attendees: A list of attendee email addresses.',
        version: '',
        scopes: [
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events',
        ],
        input: 'GoogleCalendarEventUpdateInput',
        output: ['GoogleCalendarEventOutput'],
        usedModels: [
          'GoogleCalendarEventOutput',
          'GoogleCalendarEventUpdateInput',
          'GoogleCalendarAttendeeInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-event',
        },
      },
      {
        name: 'list-calendars',
        type: 'action',
        description:
          'Lists all calendars available to the authenticated user.\nAVOID USING THIS ENDPOINT; Normally you can just pass "primary" as the calendarId to other endpoints, unless explicitly requested otherwise.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/calendar.readonly'],
        input: null,
        output: ['GoogleCalendarList'],
        usedModels: ['GoogleCalendarList'],
        endpoint: {
          method: 'GET',
          path: '/list-calendars',
        },
      },
      {
        name: 'list-events',
        type: 'action',
        description:
          'Lists events from a specified calendar. By default will only include future events. To include past events, set the timeMin to some time in the past.\n- calendarId: Calendar identifier. Use "primary" unless otherwise advised.\n- timeMin: Lower bound (inclusive) for an event\'s end time to filter by. Defaults to now. ISO8601 string format.\n- timeMax: Upper bound (exclusive) for an event\'s start time to filter by. Defaults to unbounded. ISO8601 string format.\n- maxResults: Defaults to 250. Max 2500.\n- pageToken: Token as per a previous response to get another page of results.\n- q: Free text search terms to find events that match these terms.\n- timeZone: Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/calendar.readonly'],
        input: 'GoogleCalendarEventsInput',
        output: ['GoogleCalendarEventList'],
        usedModels: ['GoogleCalendarEventList', 'GoogleCalendarEventsInput'],
        endpoint: {
          method: 'GET',
          path: '/list-events',
        },
      },
      {
        name: 'delete-event',
        type: 'action',
        description:
          'Deletes an event from Google Calendar.\n- calendarId: Calendar identifier. Use "primary" unless otherwise advised.\n- eventId: Event identifier.\n- sendUpdates: Whether to send notifications about the deletion of the event.',
        version: '',
        scopes: [
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events',
        ],
        input: 'GoogleCalendarEventDeleteInput',
        output: null,
        usedModels: ['GoogleCalendarEventDeleteInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-event',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
] as const;
// ------ /Flows
