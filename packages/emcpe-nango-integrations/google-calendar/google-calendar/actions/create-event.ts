import type {
  GoogleCalendarEventInput,
  GoogleCalendarEventOutput,
  NangoAction,
} from '../../models';

type NangoError = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: GoogleCalendarEventInput
): Promise<GoogleCalendarEventOutput | NangoError> {
  const { summary, description, location, start, end, attendees, timeZone } = input;

  try {
    if (!summary) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Event summary (title) is required',
        },
      };
    }

    if (!start || !end) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Event start and end times are required',
        },
      };
    }

    const startDateTime =
      start.length <= 10 ? { date: start } : { dateTime: start, timeZone: timeZone };
    const endDateTime = end.length <= 10 ? { date: end } : { dateTime: end, timeZone: timeZone };

    const event = {
      summary,
      description,
      location,
      start: startDateTime,
      end: endDateTime,
      attendees: attendees?.map(email => ({ email })),
    };

    const eventResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/calendar/v3/calendars/primary/events',
      data: event,
      retries: 3,
    });

    return eventResponse.data;
  } catch (error: any) {
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while creating the event.';
    return { error: { status, message } };
  }
}
