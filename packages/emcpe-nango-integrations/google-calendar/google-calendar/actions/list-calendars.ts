import type { NangoAction, GoogleCalendarList } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Lists all calendars available to the authenticated user.
 *
 * @param nango - The Nango SDK instance
 * @param input - Optional parameters for pagination
 * @returns A list of calendars and a nextPageToken if more results are available
 */
export default async function runAction(
  nango: NangoAction,
  input: any = {}
): Promise<GoogleCalendarList | NangoError> {
  try {
    const params: Record<string, string | number> = {};

    if (input.pageToken) {
      params['pageToken'] = input.pageToken;
    }

    if (input.maxResults) {
      params['maxResults'] = input.maxResults;
    } else {
      params['maxResults'] = 100;
    }

    if (input.showHidden !== undefined) {
      params['showHidden'] = input.showHidden ? 'true' : 'false';
    }

    if (input.minAccessRole) {
      params['minAccessRole'] = input.minAccessRole;
    }

    const response = await nango.get({
      endpoint: 'calendar/v3/users/me/calendarList',
      params,
    });

    if (response.status !== 200) {
      console.error('Google Calendar API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `Google Calendar API Error: Failed to list calendars: Status Code ${response.status}`,
        },
      };
    }

    const calendars = (response.data.items || []).map((calendar: any) => ({
      id: calendar.id,
      summary: calendar.summary,
      description: calendar.description,
      location: calendar.location,
      timeZone: calendar.timeZone,
      accessRole: calendar.accessRole,
      primary: !!calendar.primary,
      backgroundColor: calendar.backgroundColor,
      foregroundColor: calendar.foregroundColor,
    }));

    return {
      calendars,
      nextPageToken: response.data.nextPageToken,
    };
  } catch (error: any) {
    console.error('Error listing Google Calendars:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while listing Google Calendars.';
    return { error: { status, message } };
  }
}
