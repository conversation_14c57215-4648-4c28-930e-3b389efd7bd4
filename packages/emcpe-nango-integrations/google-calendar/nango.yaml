integrations:
    google-calendar:
        actions:
            create-event:
                endpoint: POST /create-event
                description: |
                    Creates a new event in Google Calendar. Can either be full-day or time-based.
                    - summary: Event title / name.
                    - description: Contains e.g. the agenda or specifics of the meeting. Can contain HTML.
                    - location: Free form text.
                    - start: Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.
                    - end: Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.
                    - timeZone: An IANA Time Zone e.g. (Area/City)
                    - attendees: A list of attendee email addresses.
                input: GoogleCalendarEventInput
                output: GoogleCalendarEventOutput
                scopes:
                  - https://www.googleapis.com/auth/calendar
                  - https://www.googleapis.com/auth/calendar.events
            update-event:
                endpoint: PATCH /update-event
                description: |
                    Updates an event in Google Calendar.
                    - calendarId: Calendar identifier. Use "primary" unless otherwise advised.
                    - eventId: Event identifier.
                    - sendUpdates: Whether to send notifications about the event update.
                    - summary: Event title / name.
                    - description: Contains e.g. the agenda or specifics of the meeting. Can contain HTML.
                    - location: Free form text.
                    - start: Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.
                    - end: Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.
                    - timeZone: An IANA Time Zone e.g. (Area/City)
                    - attendees: A list of attendee email addresses.
                input: GoogleCalendarEventUpdateInput
                output: GoogleCalendarEventOutput
                scopes:
                  - https://www.googleapis.com/auth/calendar
                  - https://www.googleapis.com/auth/calendar.events
            list-calendars:
                endpoint: GET /list-calendars
                description: |
                    Lists all calendars available to the authenticated user.
                    AVOID USING THIS ENDPOINT; Normally you can just pass "primary" as the calendarId to other endpoints, unless explicitly requested otherwise.
                output: GoogleCalendarList
                scopes:
                  - https://www.googleapis.com/auth/calendar.readonly
            list-events:
                endpoint: GET /list-events
                description: |
                    Lists events from a specified calendar. By default will only include future events. To include past events, set the timeMin to some time in the past.
                    - calendarId: Calendar identifier. Use "primary" unless otherwise advised.
                    - timeMin: Lower bound (inclusive) for an event's end time to filter by. Defaults to now. ISO8601 string format.
                    - timeMax: Upper bound (exclusive) for an event's start time to filter by. Defaults to unbounded. ISO8601 string format.
                    - maxResults: Defaults to 250. Max 2500.
                    - pageToken: Token as per a previous response to get another page of results.
                    - q: Free text search terms to find events that match these terms.
                    - timeZone: Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar.
                input: GoogleCalendarEventsInput
                output: GoogleCalendarEventList
                scopes:
                  - https://www.googleapis.com/auth/calendar.readonly
            delete-event:
                endpoint: DELETE /delete-event
                description: |
                    Deletes an event from Google Calendar.
                    - calendarId: Calendar identifier. Use "primary" unless otherwise advised.
                    - eventId: Event identifier.
                    - sendUpdates: Whether to send notifications about the deletion of the event.
                input: GoogleCalendarEventDeleteInput
                scopes:
                  - https://www.googleapis.com/auth/calendar
                  - https://www.googleapis.com/auth/calendar.events


models:
    GoogleCalendarList:
        calendars:
            - id: string
              summary: string
              description?: string
              location?: string
              timeZone: string
              accessRole: string
              primary: boolean
              backgroundColor?: string
              foregroundColor?: string
        nextPageToken?: string
    GoogleCalendarEventsInput:
        calendarId: string
        timeMin?: string
        timeMax?: string
        maxResults?: number
        pageToken?: string
        orderBy?: string
        q?: string
        singleEvents?: boolean
        timeZone?: string
    GoogleCalendarEventList:
        events:
            - id: string
              summary: string
              description?: string
              location?: string
              start:
                dateTime?: string
                date?: string
                timeZone?: string
              end:
                dateTime?: string
                date?: string
                timeZone?: string
              status: string
              creator:
                id?: string
                email: string
                displayName?: string
                self?: boolean
              organizer:
                id?: string
                email: string
                displayName?: string
                self?: boolean
              attendees?:
                - id?: string
                  email: string
                  displayName?: string
                  responseStatus: string
                  optional?: boolean
                  resource?: boolean
                  comment?: string
              htmlLink: string
              created: string
              updated: string
              recurrence?: string[]
        nextPageToken?: string
        timeZone: string
    GoogleCalendarEventInput:
        summary: string
        description?: string
        location?: string
        start: string
        end: string
        timeZone?: string
        attendees?: string[]
    GoogleCalendarEventDateTimeInput:
        dateTime?: string
        date?: string
        timeZone?: string
    GoogleCalendarAttendeeInput:
        id?: string
        email: string
        displayName?: string
        responseStatus: string
        optional?: boolean
        resource?: boolean
        comment?: string
    GoogleCalendarEventUpdateInput:
        calendarId: string
        eventId: string
        sendUpdates?: string
        summary?: string
        description?: string
        location?: string
        start?: string
        end?: string
        timeZone?: string
        attendees?: GoogleCalendarAttendeeInput[]


    GoogleCalendarEventOutput:
        kind: string
        etag: string
        id: string
        status: string
        htmlLink: string
        created: string
        updated: string
        summary: string
        description?: string
        location?: string
        creator:
          email: string
          self?: boolean
        organizer:
          email: string
          self?: boolean
        start:
          dateTime?: string
          date?: string
          timeZone?: string
        end:
          dateTime?: string
          date?: string
          timeZone?: string
        iCalUID: string
        sequence: number
        reminders:
          useDefault: boolean
        eventType: string
        attendees?:
          - email: string
            responseStatus?: string
            displayName?: string
            optional?: boolean
            resource?: boolean
            comment?: string
        recurrence?: string[]
        anyoneCanAddSelf?: boolean
        guestsCanInviteOthers?: boolean
        guestsCanSeeOtherGuests?: boolean
        guestsCanModify?: boolean
        privateCopy?: boolean
        transparency?: string
        visibility?: string
        colorId?: string
        attachments?:
          - fileUrl: string
            title: string
            mimeType: string
            fileId: string
    GoogleCalendarEventDeleteInput:
        calendarId: string
        eventId: string
        sendUpdates?: string
