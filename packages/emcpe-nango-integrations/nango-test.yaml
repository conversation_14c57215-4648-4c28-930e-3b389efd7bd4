integrations:
  harvest:
    actions:
      list-projects:
        endpoint: GET /list-projects
        description: Lists projects from Harvest.
        input: HarvestProjectsInput
        output: HarvestProjectList

models:
  HarvestProjectsInput:
    page?: number        # Page number
    per_page?: number    # Number of results per page
  
  HarvestProjectList:
    projects: HarvestProject[]
    per_page: number
    total_pages: number
    total_entries: number
    next_page?: number
    previous_page?: number
    page: number
  
  HarvestProject:
    id: number
    name: string
    is_active: boolean
