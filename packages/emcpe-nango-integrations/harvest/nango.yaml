integrations:
    harvest:
        actions:
            # Time entry CRUD actions
            list-time-entries:
                endpoint: GET /list-time-entries
                description: Lists time entries from Harvest.
                input: HarvestTimeEntriesInput
                output: HarvestTimeEntryList
            get-time-entry:
                endpoint: GET /get-time-entry
                description: Gets a specific time entry by ID.
                input: HarvestTimeEntryInput
                output: HarvestTimeEntry
            add-historical-time-entry:
                endpoint: POST /add-historical-time-entry
                description: Adds a completed time entry for a specific duration or start/end time. Checks company settings for duration vs timestamp tracking.
                input: HarvestAddHistoricalTimeEntryInput
                output: HarvestTimeEntry
            start-timer:
                endpoint: POST /start-timer
                description: Starts a new timer for a task. Checks company settings for duration vs timestamp tracking.
                input: HarvestStartTimerInput
                output: HarvestTimeEntry
            stop-timer:
                endpoint: PATCH /stop-timer
                description: Stops a running time entry in Harvest.
                input: HarvestTimeEntryInput
                output: HarvestTimeEntry
            restart-timer:
                endpoint: POST /restart-timer
                description: Restarts a stopped time entry in Harvest.
                input: HarvestTimeEntryInput
                output: HarvestTimeEntry
            update-time-entry:
                endpoint: PATCH /update-time-entry
                description: Updates an existing time entry in Harvest.
                input: HarvestUpdateTimeEntryInput
                output: HarvestTimeEntry
            delete-time-entry:
                endpoint: DELETE /delete-time-entry
                description: Deletes a time entry in Harvest.
                input: HarvestTimeEntryInput
                output: HarvestDeleteTimeEntryOutput
            # Project CRUD actions
            list-projects:
                endpoint: GET /list-projects
                description: Lists projects from Harvest.
                input: HarvestProjectsInput
                output: HarvestProjectList
            get-project:
                endpoint: GET /get-project
                description: Gets a specific project by ID.
                input: HarvestProjectInput
                output: HarvestProject
            delete-project:
                endpoint: DELETE /delete-project
                description: Deletes a project in Harvest.
                input: HarvestProjectInput
                output: HarvestDeleteProjectOutput
            create-project:
                endpoint: POST /v2/projects
                description: Creates a new project in Harvest.
                input: HarvestCreateProjectInput
                output: HarvestProject
            # Client CRUD actions
            list-clients:
                endpoint: GET /list-clients
                description: Lists clients from Harvest.
                output: HarvestClientList
            get-client:
                endpoint: GET /get-client
                description: Gets a specific client by ID.
                input: HarvestClientInput
                output: HarvestClient
            create-client:
                endpoint: POST /v2/clients
                description: Creates a new client in Harvest.
                input: HarvestCreateClientInput
                output: HarvestClient

            list-tasks:
                endpoint: GET /list-tasks
                description: Lists tasks from Harvest.
                input: HarvestTasksInput
                output: HarvestTaskList
            list-project-tasks:
                endpoint: GET /list-project-tasks
                description: Lists task assignments for a specific project in Harvest.
                input: HarvestProjectTasksInput
                output: HarvestProjectTaskList

models:
    HarvestCreateProjectInput:
        client_id: number       # ID of the client the project belongs to
        name: string            # Name of the project
        is_billable: boolean    # Whether the project is billable
        bill_by: string         # Billing method: 'Project', 'Tasks', 'People', 'none'
        budget_by: string       # Budget method: 'project', 'project_cost', 'task', 'task_fees', 'person', 'none'
        is_fixed_fee?: boolean  # Optional: Whether the project is fixed fee
        fee?: number | null     # Optional: Fixed fee amount (if is_fixed_fee is true)
        hourly_rate?: number | null # Optional: Default hourly rate
        budget?: number | null  # Optional: Project budget amount
        budget_is_monthly?: boolean # Optional: Whether the budget resets monthly
        notify_when_over_budget?: boolean # Optional: Whether to send notifications when over budget
        over_budget_notification_percentage?: number # Optional: Percentage threshold for over budget notification
        show_budget_to_all?: boolean # Optional: Whether to show budget to all project managers
        cost_budget?: number | null # Optional: Cost budget amount
        cost_budget_include_expenses?: boolean # Optional: Whether to include expenses in cost budget
        notes?: string | null   # Optional: Project notes
        starts_on?: string | null # Optional: Project start date (YYYY-MM-DD)
        ends_on?: string | null   # Optional: Project end date (YYYY-MM-DD)

    HarvestCreateClientInput:
        name: string            # Name of the client
        is_active?: boolean     # Optional: Whether the client is active (defaults to true)
        address?: string | null # Optional: Client address
        currency?: string | null # Optional: Client currency code (e.g., "USD", "EUR")

    HarvestTasksInput:
        is_active?: boolean    # Optional filter for active tasks only
        updated_since?: string # Optional filter for tasks updated since a specific date
        page?: number          # Optional page number for pagination
        per_page?: number      # Optional number of results per page

    HarvestTaskList:
        tasks: HarvestTask[]   # List of tasks
        per_page: number       # Number of results per page
        total_pages: number    # Total number of pages
        total_entries: number  # Total number of entries
        next_page?: number | null # Next page number, if available
        previous_page?: number | null # Previous page number, if available
        page: number           # Current page number
        links?: object         # Pagination links

    HarvestTask:
        id: number             # Task ID
        name: string           # Task name
        is_active: boolean     # Whether the task is active
        billable_by_default: boolean # Whether the task is billable by default
        is_default?: boolean   # Whether this is a default task
        created_at: string     # When the task was created
        updated_at: string     # When the task was last updated
        default_hourly_rate?: number | null # Default hourly rate for the task

    # Task reference within TimeEntry (make fields optional here)
    HarvestTaskInTimeEntry:
        id: number             # Task ID
        name: string           # Task name
        # Fields below are optional as they might not be present in the time entry context
        is_active?: boolean
        billable_by_default?: boolean
        is_default?: boolean
        created_at?: string
        updated_at?: string
        default_hourly_rate?: number | null

    HarvestTimeEntriesInput:
        userId?: number      # Optional user ID to filter time entries by
        clientId?: number    # Optional client ID to filter time entries by
        projectId?: number   # Optional project ID to filter time entries by
        taskId?: number      # Optional task ID to filter time entries by
        from?: string        # Optional start date (YYYY-MM-DD)
        to?: string          # Optional end date (YYYY-MM-DD)
        page?: number        # Optional page number for pagination
        perPage?: number     # Optional number of entries per page

    HarvestTimeEntryInput:
        timeEntryId: number  # ID of the time entry to retrieve
        id?: number          # ID of the time entry to retrieve

    HarvestUser:
        id: number           # User ID
        name: string         # User name
        email?: string       # User email (optional)

    # Simplified client reference used within Project model
    HarvestClientReference:
        id: number           # Client ID
        name: string         # Client name
        currency: string     # Client currency code

    HarvestClient:
        id: number           # Client ID
        name: string         # Client name
        is_active: boolean   # Whether the client is active
        address?: string | null # Client address
        statement_key?: string # Statement key
        created_at?: string  # Creation timestamp
        updated_at?: string  # Last update timestamp
        currency?: string    # Client currency code

    # Client reference within TimeEntry (make is_active optional here)
    HarvestClientInTimeEntry:
        id: number           # Client ID
        name: string         # Client name
        currency?: string    # Client currency code
        is_active?: boolean # Optional in this context

    HarvestProject:
        id: number           # Project ID
        name: string         # Project name
        code?: string | null # Project code (nullable)
        client: HarvestClientReference # Use the simplified reference
        is_active: boolean   # Whether the project is active
        is_billable: boolean # Whether the project is billable
        is_fixed_fee: boolean # Whether the project has a fixed fee
        bill_by: string      # How the project is billed
        budget?: number | null # Project budget (nullable)
        budget_by: string    # How the budget is calculated
        budget_is_monthly: boolean # Whether the budget is monthly
        notify_when_over_budget: boolean # Whether to notify when over budget
        over_budget_notification_percentage?: number # Percentage for over budget notification
        show_budget_to_all: boolean # Whether to show budget to all users
        created_at: string   # Creation timestamp
        updated_at: string   # Last update timestamp
        starts_on?: string | null # Project start date (nullable)
        ends_on?: string | null # Project end date (nullable)
        over_budget_notification_date?: string | null # Over budget notification date (nullable)
        notes?: string | null # Project notes (nullable)
        cost_budget?: number | null # Cost budget (nullable)
        cost_budget_include_expenses: boolean # Whether to include expenses in cost budget
        hourly_rate?: number | null # Hourly rate (nullable)
        fee?: number | null  # Project fee (nullable)

    HarvestTimeEntry:
        id: number           # Time entry ID
        spent_date: string   # Date the time was spent (YYYY-MM-DD)
        hours: number        # Hours logged
        notes?: string       # Optional notes
        is_locked: boolean   # Whether the time entry is locked
        is_running: boolean  # Whether the timer is running
        is_billed: boolean   # Whether the time entry is billed
        timer_started_at?: string | null # When the timer was started
        started_time?: string | null # Time of day the entry was started
        ended_time?: string | null  # Time of day the entry was ended
        user: HarvestUser    # User who logged the time
        client: HarvestClientInTimeEntry # Use the context-specific client model
        project: ProjectReference # Project the time entry is for
        task: HarvestTaskInTimeEntry    # Use the context-specific task model
        created_at: string   # When the time entry was created
        updated_at: string   # When the time entry was last updated
        # Add missing optional fields observed in API response
        hours_without_timer?: number
        rounded_hours?: number
        locked_reason?: string | null
        is_closed?: boolean
        billable?: boolean
        budgeted?: boolean
        billable_rate?: number | null
        cost_rate?: number | null
        # Nested objects observed in API response (define simple inline types or separate models if complex)
        user_assignment?: object # Define more specifically if needed
        task_assignment?: object # Define more specifically if needed
        invoice?: object | null  # Define more specifically if needed
        external_reference?: HarvestExternalReferenceInput | null # Use existing model, make nullable

    HarvestTimeEntryList:
        time_entries: HarvestTimeEntry[] # List of time entries
        per_page: number     # Number of entries per page
        total_pages: number  # Total number of pages
        total_entries: number # Total number of entries
        next_page?: number | null   # Next page number
        previous_page?: number | null # Previous page number
        page?: number        # Current page number
        links?: object       # Pagination links

    HarvestCompany:
        id: number
        name: string
        is_active: boolean
        base_uri: string
        full_domain: string
        currency: string
        thousands_separator: string
        decimal_separator: string
        timezone: string
        week_start_day: string # e.g., "Monday"
        time_format: string # e.g., "12h"
        date_format: string # e.g., "%Y-%m-%d"
        users_can_create_projects: boolean
        users_can_create_invoices: boolean
        expense_feature_enabled: boolean
        invoice_feature_enabled: boolean
        wants_timestamp_timers: boolean # Key field for our logic
        created_at: string
        updated_at: string

    HarvestAddHistoricalTimeEntryInput:
        project_id: number   # Project ID
        task_id: number      # Task ID
        spent_date: string   # Date the time was spent (YYYY-MM-DD)
        hours?: number       # Required if company uses duration tracking. Mutually exclusive with started/ended_time.
        started_time?: string # Required if company uses timestamp tracking. Mutually exclusive with hours. Example: "8:00am"
        ended_time?: string  # Required if company uses timestamp tracking. Mutually exclusive with hours. Example: "9:00am"
        notes?: string       # Optional notes
        user_id?: number     # Optional: The ID of the user to associate with the time entry. Defaults to the currently authenticated user’s ID.
        external_reference?: HarvestExternalReferenceInput # Optional external reference

    HarvestStartTimerInput:
        project_id: number   # Project ID
        task_id: number      # Task ID
        spent_date: string   # Date the timer is for (YYYY-MM-DD)
        started_time?: string # Optional: Time the entry started (used if company uses timestamp tracking). Defaults to the current time. Example: "8:00am".
        notes?: string       # Optional notes
        user_id?: number     # Optional: The ID of the user to associate with the time entry. Defaults to the currently authenticated user’s ID.
        external_reference?: HarvestExternalReferenceInput # Optional external reference

    HarvestUpdateTimeEntryInput:
        time_entry_id: number # ID of the time entry to update
        project_id?: number  # Optional new project ID
        task_id?: number     # Optional new task ID
        spent_date?: string  # Optional new date (YYYY-MM-DD)
        hours?: number       # Optional new hours (for duration tracking)
        started_time?: string # Optional new start time (for timestamp tracking, e.g., "8:00am")
        ended_time?: string  # Optional new end time (for timestamp tracking, e.g., "9:00am")
        notes?: string       # Optional new notes
        external_reference?: HarvestExternalReferenceInput # Optional external reference

    HarvestDeleteTimeEntryOutput:
        success: boolean     # Whether the deletion was successful
        message: string      # Status message

    HarvestProjectsInput:
        client_id?: number   # Optional client ID to filter projects by
        is_active?: boolean  # Optional active status to filter by
        page?: number        # Optional page number for pagination
        per_page?: number    # Optional number of projects per page

    HarvestProjectInput:
        project_id: number   # ID of the project to retrieve

    # Pagination links model
    HarvestPaginationLinks:
        first?: string       # Link to first page
        next?: string | null # Link to next page (nullable)
        previous?: string | null # Link to previous page (nullable)
        last?: string        # Link to last page

    HarvestProjectList:
        projects: HarvestProject[] # List of projects
        per_page: number     # Number of projects per page
        total_pages: number  # Total number of pages
        total_entries: number # Total number of projects
        next_page?: number | null # Next page number (nullable)
        previous_page?: number | null # Previous page number (nullable)
        page?: number        # Current page number
        links?: HarvestPaginationLinks # Pagination links (optional)

    HarvestClientInput:
        client_id: number    # ID of the client to retrieve

    HarvestClientList:
        clients: HarvestClient[] # List of clients
        per_page: number     # Number of clients per page
        total_pages: number  # Total number of pages
        total_entries: number # Total number of clients
        next_page?: number | null   # Next page number
        previous_page?: number | null # Previous page number
        page?: number        # Current page number
        links?: object       # Pagination links

    HarvestExternalReferenceInput:
        id?: string          # Optional: The ID of the external reference.
        group_id?: string    # Optional: The group ID of the external reference.
        account_id?: string  # Optional: The account ID of the external reference.
        permalink?: string   # Optional: The permalink of the external reference.
        service?: string     # Optional: The service of the external reference.
        service_icon_url?: string # Optional: The service icon URL of the external reference.

    HarvestProjectTasksInput:
        project_id: number   # ID of the project to get task assignments for

    # Simplified Task model used in task assignments
    TaskInAssignment:
        id: number           # Task ID
        name: string         # Task name

    # Project reference in task assignment
    ProjectReference:
        id: number           # Project ID
        name: string         # Project name
        code?: string | null # Project code (nullable)
        is_active?: boolean  # Whether the project is active
        is_billable?: boolean # Whether the project is billable

    # Links in pagination
    PaginationLinks:
        first?: string       # Link to first page
        next?: string | null # Link to next page (nullable)
        previous?: string | null # Link to previous page (nullable)
        last?: string        # Link to last page

    HarvestProjectTask:
        id: number           # Task assignment ID
        billable: boolean    # Whether the task is billable
        is_active: boolean   # Whether the task assignment is active
        created_at: string   # Creation timestamp
        updated_at: string   # Last update timestamp
        hourly_rate?: number | null # Hourly rate for this task assignment (nullable)
        budget?: number | null      # Budget for this task assignment (nullable)
        project?: ProjectReference # Project details (optional)
        task: TaskInAssignment # Task details

    HarvestProjectTaskList:
        task_assignments: HarvestProjectTask[] # List of task assignments
        per_page?: number    # Number of task assignments per page
        total_pages?: number # Total number of pages
        total_entries?: number # Total number of task assignments
        next_page?: number | null   # Next page number (nullable)
        previous_page?: number | null # Previous page number (nullable)
        page?: number        # Current page number
        links?: PaginationLinks # Pagination links (optional)

    HarvestDeleteProjectOutput:
        success: boolean     # Whether the deletion was successful
        message: string     # A message describing the result
