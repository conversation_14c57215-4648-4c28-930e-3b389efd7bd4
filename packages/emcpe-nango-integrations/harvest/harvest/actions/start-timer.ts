import type {
  NangoAction,
  HarvestStartTimerInput,
  HarvestCompany,
  HarvestTimeEntry,
} from '../../models';
import { getHarvestAccountId } from './harvestHelpers';
type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

const METADATA_KEY = 'harvestCompanySettings';

async function getCompanySettings(nango: NangoAction): Promise<HarvestCompany | ActionError> {
  try {
    const cachedSettings = (await nango.getMetadata()) as { [METADATA_KEY]?: HarvestCompany };
    if (cachedSettings && cachedSettings[METADATA_KEY]) {
      return cachedSettings[METADATA_KEY];
    }
  } catch (error: any) {
    console.warn('Failed to retrieve metadata, fetching from API:', error);
  }

  try {
    const accountIdResult = await getHarvestAccountId(nango);
    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error(
        'Failed to get Harvest Account ID in getCompanySettings:',
        accountIdResult.error.message
      );
      return accountIdResult;
    }
    const accountId = accountIdResult;

    const response = await nango.proxy({
      endpoint: '/v2/company',
      method: 'GET',
      headers: {
        'Harvest-Account-Id': accountId,
      },
      retries: 2,
    });

    if (response.status !== 200 || !response.data) {
      const status = response.status || 500;
      const message = `Failed to fetch company settings. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('FETCH_COMPANY_FAILED:', message);
      return { error: { status, message: `FETCH_COMPANY_FAILED: ${message}` } };
    }

    const companyData = response.data as HarvestCompany;

    try {
      await nango.setMetadata({ [METADATA_KEY]: companyData });
    } catch (error: any) {
      console.error('Failed to set metadata:', error);
    }

    return companyData;
  } catch (error: any) {
    console.error('Error fetching company settings:', error);
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching company settings.';
    return { error: { status, message: `Failed to fetch company settings: ${message}` } };
  }
}

export default async function runAction(
  nango: NangoAction,
  input: HarvestStartTimerInput
): Promise<HarvestTimeEntry | ActionError> {
  try {
    const accountIdResult = await getHarvestAccountId(nango);
    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error('Failed to get Harvest Account ID:', accountIdResult.error.message);
      return accountIdResult;
    }
    const accountId = accountIdResult;

    const companySettingsResult = await getCompanySettings(nango);
    if ('error' in companySettingsResult) {
      console.error('Failed to get Company Settings:', companySettingsResult.error.message);
      return companySettingsResult;
    }
    const companySettings = companySettingsResult;
    const wantsTimestampTimers = companySettings.wants_timestamp_timers;

    const payload: Record<string, any> = {
      project_id: input.project_id,
      task_id: input.task_id,
      spent_date: input.spent_date,
      notes: input.notes,
      user_id: input.user_id,
      external_reference: input.external_reference,
    };

    if (wantsTimestampTimers) {
      if (input.started_time) {
        payload['started_time'] = input.started_time;
      }
    } else {
    }

    Object.keys(payload).forEach(key => payload[key] === undefined && delete payload[key]);

    const response = await nango.proxy({
      endpoint: '/v2/time_entries',
      method: 'POST',
      data: payload,
      headers: {
        'Harvest-Account-Id': accountId,
      },
      retries: 2,
    });

    if (response.status !== 201 || !response.data) {
      const status = response.status || 500;
      const message = `Failed to start timer. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('START_TIMER_FAILED:', message);
      return { error: { status, message: `START_TIMER_FAILED: ${message}` } };
    }

    return response.data as HarvestTimeEntry;
  } catch (error: any) {
    console.error('Error starting timer:', error);
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while starting the timer.';
    return { error: { status, message: `Failed to start timer: ${message}` } };
  }
}
