import type { NangoAction, HarvestProject, HarvestProjectInput } from '../../models';
import { getHarvestAccountId } from './harvestHelpers';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: HarvestProjectInput
): Promise<HarvestProject | NangoError> {
  try {
    if (!input.project_id) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Missing required parameter: project_id is required',
        },
      };
    }

    const accountId = await getHarvestAccountId(nango);
    if (typeof accountId !== 'string') {
      return accountId as NangoError;
    }
    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/v2/projects/${input.project_id}`,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    const project = response.data;

    return {
      ...project,
      client: {
        ...project.client,
        is_active: true,
      },
    };
  } catch (error: any) {
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while fetching the project.';
    console.error('Error fetching project from Harvest:', error);
    return { error: { status, message } };
  }
}
