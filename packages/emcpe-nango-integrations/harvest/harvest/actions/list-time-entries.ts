import type { HarvestTimeEntriesInput, HarvestTimeEntryList, NangoAction } from '../../models';
import { getHarvestAccountId } from './harvestHelpers';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Lists time entries from Harvest
 */
export default async function runAction(
  nango: NangoAction,
  input?: HarvestTimeEntriesInput
): Promise<HarvestTimeEntryList | ActionError> {
  try {
    const accountIdResult = await getHarvestAccountId(nango);

    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error('Failed to get Harvest Account ID:', accountIdResult.error.message);
      return accountIdResult;
    }
    const accountId = accountIdResult;

    const params: Record<string, any> = {
      per_page: input?.perPage || 100,
      page: input?.page || 1,
    };

    if (input?.userId) {
      params['user_id'] = input.userId;
    }

    if (input?.clientId) {
      params['client_id'] = input.clientId;
    }

    if (input?.projectId) {
      params['project_id'] = input.projectId;
    }

    if (input?.taskId) {
      params['task_id'] = input.taskId;
    }

    if (input?.from) {
      params['from'] = input.from;
    }

    if (input?.to) {
      params['to'] = input.to;
    }

    const response = await nango.proxy({
      method: 'GET',
      endpoint: '/v2/time_entries',
      params,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    const timeEntries = response.data.time_entries.map((entry: any) => ({
      id: entry.id,
      spent_date: entry.spent_date,
      hours: entry.hours,
      notes: entry.notes,
      is_locked: entry.is_locked,
      is_running: entry.is_running,
      is_billed: entry.is_billed,
      timer_started_at: entry.timer_started_at,
      started_time: entry.started_time,
      ended_time: entry.ended_time,
      user: {
        id: entry.user.id,
        name: entry.user.name,
        ...(entry.user.email && { email: entry.user.email }),
      },
      client: {
        id: entry.client.id,
        name: entry.client.name,
        is_active: true,
      },
      project: {
        id: entry.project.id,
        name: entry.project.name,
        code: entry.project.code,
        is_active: entry.project.is_active || true,
        is_billable: entry.project.is_billable || false,
      },
      task: {
        id: entry.task.id,
        name: entry.task.name,
        is_active: true,
        billable_by_default: entry.task.billable_by_default || false,
        created_at: entry.task.created_at || entry.created_at,
        updated_at: entry.task.updated_at || entry.updated_at,
      },
      created_at: entry.created_at,
      updated_at: entry.updated_at,
    }));

    return {
      time_entries: timeEntries,
      per_page: response.data.per_page,
      total_pages: response.data.total_pages,
      total_entries: response.data.total_entries,
      next_page: response.data.next_page,
      previous_page: response.data.previous_page,
      page: response.data.page || 1,
      links: response.data.links || {},
    };
  } catch (error: any) {
    console.error('Error fetching time entries from Harvest:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching time entries.';

    return {
      error: {
        status: status,
        message: `Failed to fetch time entries: ${message}`,
      },
    };
  }
}
