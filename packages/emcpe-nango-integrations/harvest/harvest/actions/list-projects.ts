import type { <PERSON>goAction, HarvestProjectList, HarvestProjectsInput } from '../../models';
import { getHarvestAccountId } from './harvestHelpers';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: HarvestProjectsInput = {}
): Promise<HarvestProjectList | ActionError> {
  try {
    const accountIdResult = await getHarvestAccountId(nango);

    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error('Failed to get Harvest Account ID:', accountIdResult.error.message);
      return accountIdResult;
    }
    const accountId = accountIdResult;

    const params: Record<string, any> = {
      per_page: input.per_page || 100,
      page: input.page || 1,
    };

    if (input.client_id) {
      params['client_id'] = input.client_id;
    }

    if (input.is_active !== undefined) {
      params['is_active'] = input.is_active;
    }

    const response = await nango.proxy({
      method: 'GET',
      endpoint: '/v2/projects',
      params,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    const projects = response.data.projects.map((project: any) => ({
      ...project,
      client: {
        ...project.client,
        is_active: true,
      },
    }));

    return {
      projects,
      per_page: response.data.per_page,
      total_pages: response.data.total_pages,
      total_entries: response.data.total_entries,
      next_page: response.data.next_page,
      previous_page: response.data.previous_page,
      page: response.data.page || 1,
      links: response.data.links || {},
    };
  } catch (error: any) {
    console.error('Error fetching projects from Harvest:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching projects.';

    return {
      error: {
        status: status,
        message: `Failed to fetch projects: ${message}`,
      },
    };
  }
}
