// ---------------------------
// This file was generated by <PERSON><PERSON> (v0.58.7)
// It's recommended to version this file
// https://nango.dev
// ---------------------------

// ------ Models
export interface HarvestCreateProjectInput {
  client_id: number;
  name: string;
  is_billable: boolean;
  bill_by: string;
  budget_by: string;
  is_fixed_fee?: boolean;
  fee?: number | null;
  hourly_rate?: number | null;
  budget?: number | null;
  budget_is_monthly?: boolean;
  notify_when_over_budget?: boolean;
  over_budget_notification_percentage?: number;
  show_budget_to_all?: boolean;
  cost_budget?: number | null;
  cost_budget_include_expenses?: boolean;
  notes?: string | null;
  starts_on?: string | null;
  ends_on?: string | null;
}

export interface HarvestCreateClientInput {
  name: string;
  is_active?: boolean;
  address?: string | null;
  currency?: string | null;
}

export interface HarvestTasksInput {
  is_active?: boolean;
  updated_since?: string;
  page?: number;
  per_page?: number;
}

export interface HarvestTask {
  id: number;
  name: string;
  is_active: boolean;
  billable_by_default: boolean;
  is_default?: boolean;
  created_at: string;
  updated_at: string;
  default_hourly_rate?: number | null;
}

export interface HarvestTaskList {
  tasks: HarvestTask[];
  per_page: number;
  total_pages: number;
  total_entries: number;
  next_page?: number | null;
  previous_page?: number | null;
  page: number;
  links?: Record<string, any>;
}

export interface HarvestTaskInTimeEntry {
  id: number;
  name: string;
  is_active?: boolean;
  billable_by_default?: boolean;
  is_default?: boolean;
  created_at?: string;
  updated_at?: string;
  default_hourly_rate?: number | null;
}

export interface HarvestTimeEntriesInput {
  userId?: number;
  clientId?: number;
  projectId?: number;
  taskId?: number;
  from?: string;
  to?: string;
  page?: number;
  perPage?: number;
}

export interface HarvestTimeEntryInput {
  timeEntryId: number;
  id?: number;
}

export interface HarvestUser {
  id: number;
  name: string;
  email?: string;
}

export interface HarvestClientReference {
  id: number;
  name: string;
  currency: string;
}

export interface HarvestClient {
  id: number;
  name: string;
  is_active: boolean;
  address?: string | null;
  statement_key?: string;
  created_at?: string;
  updated_at?: string;
  currency?: string;
}

export interface HarvestClientInTimeEntry {
  id: number;
  name: string;
  currency?: string;
  is_active?: boolean;
}

export interface HarvestProject {
  id: number;
  name: string;
  code?: string | null;
  client: HarvestClientReference;
  is_active: boolean;
  is_billable: boolean;
  is_fixed_fee: boolean;
  bill_by: string;
  budget?: number | null;
  budget_by: string;
  budget_is_monthly: boolean;
  notify_when_over_budget: boolean;
  over_budget_notification_percentage?: number;
  show_budget_to_all: boolean;
  created_at: string;
  updated_at: string;
  starts_on?: string | null;
  ends_on?: string | null;
  over_budget_notification_date?: string | null;
  notes?: string | null;
  cost_budget?: number | null;
  cost_budget_include_expenses: boolean;
  hourly_rate?: number | null;
  fee?: number | null;
}

export interface ProjectReference {
  id: number;
  name: string;
  code?: string | null;
  is_active?: boolean;
  is_billable?: boolean;
}

export interface HarvestExternalReferenceInput {
  id?: string;
  group_id?: string;
  account_id?: string;
  permalink?: string;
  service?: string;
  service_icon_url?: string;
}

export interface HarvestTimeEntry {
  id: number;
  spent_date: string;
  hours: number;
  notes?: string;
  is_locked: boolean;
  is_running: boolean;
  is_billed: boolean;
  timer_started_at?: string | null;
  started_time?: string | null;
  ended_time?: string | null;
  user: HarvestUser;
  client: HarvestClientInTimeEntry;
  project: ProjectReference;
  task: HarvestTaskInTimeEntry;
  created_at: string;
  updated_at: string;
  hours_without_timer?: number;
  rounded_hours?: number;
  locked_reason?: string | null;
  is_closed?: boolean;
  billable?: boolean;
  budgeted?: boolean;
  billable_rate?: number | null;
  cost_rate?: number | null;
  user_assignment?: Record<string, any>;
  task_assignment?: Record<string, any>;
  invoice?: Record<string, any> | null;
  external_reference?: HarvestExternalReferenceInput | null;
}

export interface HarvestTimeEntryList {
  time_entries: HarvestTimeEntry[];
  per_page: number;
  total_pages: number;
  total_entries: number;
  next_page?: number | null;
  previous_page?: number | null;
  page?: number;
  links?: Record<string, any>;
}

export interface HarvestCompany {
  id: number;
  name: string;
  is_active: boolean;
  base_uri: string;
  full_domain: string;
  currency: string;
  thousands_separator: string;
  decimal_separator: string;
  timezone: string;
  week_start_day: string;
  time_format: string;
  date_format: string;
  users_can_create_projects: boolean;
  users_can_create_invoices: boolean;
  expense_feature_enabled: boolean;
  invoice_feature_enabled: boolean;
  wants_timestamp_timers: boolean;
  created_at: string;
  updated_at: string;
}

export interface HarvestAddHistoricalTimeEntryInput {
  project_id: number;
  task_id: number;
  spent_date: string;
  hours?: number;
  started_time?: string;
  ended_time?: string;
  notes?: string;
  user_id?: number;
  external_reference?: HarvestExternalReferenceInput;
}

export interface HarvestStartTimerInput {
  project_id: number;
  task_id: number;
  spent_date: string;
  started_time?: string;
  notes?: string;
  user_id?: number;
  external_reference?: HarvestExternalReferenceInput;
}

export interface HarvestUpdateTimeEntryInput {
  time_entry_id: number;
  project_id?: number;
  task_id?: number;
  spent_date?: string;
  hours?: number;
  started_time?: string;
  ended_time?: string;
  notes?: string;
  external_reference?: HarvestExternalReferenceInput;
}

export interface HarvestDeleteTimeEntryOutput {
  success: boolean;
  message: string;
}

export interface HarvestProjectsInput {
  client_id?: number;
  is_active?: boolean;
  page?: number;
  per_page?: number;
}

export interface HarvestProjectInput {
  project_id: number;
}

export interface HarvestPaginationLinks {
  first?: string;
  next?: string | null;
  previous?: string | null;
  last?: string;
}

export interface HarvestProjectList {
  projects: HarvestProject[];
  per_page: number;
  total_pages: number;
  total_entries: number;
  next_page?: number | null;
  previous_page?: number | null;
  page?: number;
  links?: HarvestPaginationLinks;
}

export interface HarvestClientInput {
  client_id: number;
}

export interface HarvestClientList {
  clients: HarvestClient[];
  per_page: number;
  total_pages: number;
  total_entries: number;
  next_page?: number | null;
  previous_page?: number | null;
  page?: number;
  links?: Record<string, any>;
}

export interface HarvestProjectTasksInput {
  project_id: number;
}

export interface TaskInAssignment {
  id: number;
  name: string;
}

export interface PaginationLinks {
  first?: string;
  next?: string | null;
  previous?: string | null;
  last?: string;
}

export interface HarvestProjectTask {
  id: number;
  billable: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  hourly_rate?: number | null;
  budget?: number | null;
  project?: ProjectReference;
  task: TaskInAssignment;
}

export interface HarvestProjectTaskList {
  task_assignments: HarvestProjectTask[];
  per_page?: number;
  total_pages?: number;
  total_entries?: number;
  next_page?: number | null;
  previous_page?: number | null;
  page?: number;
  links?: PaginationLinks;
}

export interface HarvestDeleteProjectOutput {
  success: boolean;
  message: string;
}
// ------ /Models

// ------ SDK

import type { Nango } from '@nangohq/node';
import type {
  AxiosInstance,
  AxiosInterceptorManager,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import type {
  ApiEndUser,
  DBSyncConfig,
  DBTeam,
  GetPublicIntegration,
  HTTP_METHOD,
  RunnerFlags,
  PostPublicTrigger,
} from '@nangohq/types';
import type { ZodSchema, SafeParseSuccess } from 'zod';

export declare const oldLevelToNewLevel: {
  readonly debug: 'debug';
  readonly info: 'info';
  readonly warn: 'warn';
  readonly error: 'error';
  readonly verbose: 'debug';
  readonly silly: 'debug';
  readonly http: 'info';
};
type LogLevel = 'info' | 'debug' | 'error' | 'warn' | 'http' | 'verbose' | 'silly';
interface Pagination {
  type: string;
  limit?: number;
  response_path?: string;
  limit_name_in_request: string;
  in_body?: boolean;
  on_page?: (paginationState: {
    nextPageParam?: string | number | undefined;
    response: AxiosResponse;
  }) => Promise<void>;
}
interface CursorPagination extends Pagination {
  cursor_path_in_response: string;
  cursor_name_in_request: string;
}
interface LinkPagination extends Pagination {
  link_rel_in_response_header?: string;
  link_path_in_response_body?: string;
}
interface OffsetPagination extends Pagination {
  offset_name_in_request: string;
  offset_start_value?: number;
  offset_calculation_method?: 'per-page' | 'by-response-size';
}
interface RetryHeaderConfig {
  at?: string;
  after?: string;
}
export interface ProxyConfiguration {
  endpoint: string;
  providerConfigKey?: string;
  connectionId?: string;
  method?:
    | 'GET'
    | 'POST'
    | 'PATCH'
    | 'PUT'
    | 'DELETE'
    | 'get'
    | 'post'
    | 'patch'
    | 'put'
    | 'delete';
  headers?: Record<string, string>;
  params?: string | Record<string, string | number>;
  data?: unknown;
  retries?: number;
  baseUrlOverride?: string;
  paginate?: Partial<CursorPagination> | Partial<LinkPagination> | Partial<OffsetPagination>;
  retryHeader?: RetryHeaderConfig;
  responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream' | undefined;
  retryOn?: number[] | null;
}
export interface AuthModes {
  OAuth1: 'OAUTH1';
  OAuth2: 'OAUTH2';
  OAuth2CC: 'OAUTH2_CC';
  Basic: 'BASIC';
  ApiKey: 'API_KEY';
  AppStore: 'APP_STORE';
  Custom: 'CUSTOM';
  App: 'APP';
  None: 'NONE';
  TBA: 'TBA';
  Tableau: 'TABLEAU';
  Jwt: 'JWT';
  Bill: 'BILL';
  TwoStep: 'TWO_STEP';
  Signature: 'SIGNATURE';
}
export type AuthModeType = AuthModes[keyof AuthModes];
interface OAuth1Token {
  oAuthToken: string;
  oAuthTokenSecret: string;
}
interface AppCredentials {
  type: AuthModes['App'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
}
interface AppStoreCredentials {
  type?: AuthModes['AppStore'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
  private_key: string;
}
interface BasicApiCredentials {
  type: AuthModes['Basic'];
  username: string;
  password: string;
}
interface ApiKeyCredentials {
  type: AuthModes['ApiKey'];
  apiKey: string;
}
interface CredentialsCommon<T = Record<string, any>> {
  type: AuthModeType;
  raw: T;
}
interface OAuth2Credentials extends CredentialsCommon {
  type: AuthModes['OAuth2'];
  access_token: string;
  refresh_token?: string;
  expires_at?: Date | undefined;
}
interface OAuth2ClientCredentials extends CredentialsCommon {
  type: AuthModes['OAuth2CC'];
  token: string;
  expires_at?: Date | undefined;
  client_id: string;
  client_secret: string;
}
interface OAuth1Credentials extends CredentialsCommon {
  type: AuthModes['OAuth1'];
  oauth_token: string;
  oauth_token_secret: string;
}
interface TbaCredentials {
  type: AuthModes['TBA'];
  token_id: string;
  token_secret: string;
  config_override: {
    client_id?: string;
    client_secret?: string;
  };
}
interface TableauCredentials extends CredentialsCommon {
  type: AuthModes['Tableau'];
  pat_name: string;
  pat_secret: string;
  content_url?: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface JwtCredentials {
  type: AuthModes['Jwt'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface BillCredentials extends CredentialsCommon {
  type: AuthModes['Bill'];
  username: string;
  password: string;
  organization_id: string;
  dev_key: string;
  session_id?: string;
  user_id?: string;
  expires_at?: Date | undefined;
}
interface TwoStepCredentials extends CredentialsCommon {
  type: AuthModes['TwoStep'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface SignatureCredentials {
  type: AuthModes['Signature'];
  username: string;
  password: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface CustomCredentials extends CredentialsCommon {
  type: AuthModes['Custom'];
}
type UnauthCredentials = Record<string, never>;
type AuthCredentials =
  | OAuth2Credentials
  | OAuth2ClientCredentials
  | OAuth1Credentials
  | BasicApiCredentials
  | ApiKeyCredentials
  | AppCredentials
  | AppStoreCredentials
  | UnauthCredentials
  | TbaCredentials
  | TableauCredentials
  | JwtCredentials
  | BillCredentials
  | TwoStepCredentials
  | SignatureCredentials
  | CustomCredentials;
type Metadata = Record<string, unknown>;
interface MetadataChangeResponse {
  metadata: Metadata;
  provider_config_key: string;
  connection_id: string | string[];
}
interface Connection {
  id: number;
  provider_config_key: string;
  connection_id: string;
  connection_config: Record<string, string>;
  created_at: string;
  updated_at: string;
  last_fetched_at: string;
  metadata: Record<string, unknown> | null;
  provider: string;
  errors: {
    type: string;
    log_id: string;
  }[];
  end_user: ApiEndUser | null;
  credentials: AuthCredentials;
}
export declare class ActionError<T = Record<string, unknown>> extends Error {
  type: string;
  payload?: Record<string, unknown>;
  constructor(payload?: T);
}
export interface NangoProps {
  scriptType: 'sync' | 'action' | 'webhook' | 'on-event';
  host?: string;
  secretKey: string;
  team?: Pick<DBTeam, 'id' | 'name'>;
  connectionId: string;
  environmentId: number;
  environmentName?: string;
  activityLogId?: string | undefined;
  providerConfigKey: string;
  provider: string;
  lastSyncDate?: Date;
  syncId?: string | undefined;
  nangoConnectionId?: number;
  syncJobId?: number | undefined;
  dryRun?: boolean;
  track_deletes?: boolean;
  attributes?: object | undefined;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]> | undefined;
  rawDeleteOutput?: Map<string, unknown[]> | undefined;
  stubbedMetadata?: Metadata | undefined;
  abortSignal?: AbortSignal;
  syncConfig: DBSyncConfig;
  runnerFlags: RunnerFlags;
  debug: boolean;
  startedAt: Date;
  endUser: {
    id: number;
    endUserId: string | null;
    orgId: string | null;
  } | null;
  axios?: {
    request?: AxiosInterceptorManager<AxiosRequestConfig>;
    response?: {
      onFulfilled: (value: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;
      onRejected: (value: unknown) => AxiosError | Promise<AxiosError>;
    };
  };
}
export interface EnvironmentVariable {
  name: string;
  value: string;
}
export declare const defaultPersistApi: AxiosInstance;
export declare class NangoAction {
  protected nango: Nango;
  private attributes;
  protected persistApi: AxiosInstance;
  activityLogId?: string | undefined;
  syncId?: string;
  nangoConnectionId?: number;
  environmentId: number;
  environmentName?: string;
  syncJobId?: number;
  dryRun?: boolean;
  abortSignal?: AbortSignal;
  syncConfig?: DBSyncConfig;
  runnerFlags: RunnerFlags;
  connectionId: string;
  providerConfigKey: string;
  provider?: string;
  ActionError: typeof ActionError;
  private memoizedConnections;
  private memoizedIntegration;
  constructor(
    config: NangoProps,
    {
      persistApi,
    }?: {
      persistApi: AxiosInstance;
    }
  );
  protected stringify(): string;
  private proxyConfig;
  protected throwIfAborted(): void;
  proxy<T = any>(config: ProxyConfiguration): Promise<AxiosResponse<T>>;
  get<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  post<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  put<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  patch<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  delete<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  getToken(): Promise<
    | string
    | OAuth1Token
    | OAuth2ClientCredentials
    | BasicApiCredentials
    | ApiKeyCredentials
    | AppCredentials
    | AppStoreCredentials
    | UnauthCredentials
    | CustomCredentials
    | TbaCredentials
    | TableauCredentials
    | JwtCredentials
    | BillCredentials
    | TwoStepCredentials
    | SignatureCredentials
  >;
  /**
   * Get current integration
   */
  getIntegration(
    queries?: GetPublicIntegration['Querystring']
  ): Promise<GetPublicIntegration['Success']['data']>;
  getConnection(
    providerConfigKeyOverride?: string,
    connectionIdOverride?: string
  ): Promise<Connection>;
  setMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  updateMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  /**
   * @deprecated please use setMetadata instead.
   */
  setFieldMapping(fieldMapping: Record<string, string>): Promise<AxiosResponse<object>>;
  getMetadata<T = Metadata>(): Promise<T>;
  getWebhookURL(): Promise<string | null | undefined>;
  /**
   * @deprecated please use getMetadata instead.
   */
  getFieldMapping(): Promise<Metadata>;
  /**
   * Log
   * @desc Log a message to the activity log which shows up in the Nango Dashboard
   * note that the last argument can be an object with a level property to specify the log level
   * @example
   * ```ts
   * await nango.log('This is a log message', { level: 'error' })
   * ```
   */
  log(
    message: any,
    options?:
      | {
          level?: LogLevel;
        }
      | {
          [key: string]: any;
          level?: never;
        }
  ): Promise<void>;
  log(
    message: string,
    ...args: [
      any,
      {
        level?: LogLevel;
      },
    ]
  ): Promise<void>;
  getEnvironmentVariables(): Promise<EnvironmentVariable[] | null>;
  getFlowAttributes<A = object>(): A | null;
  paginate<T = any>(config: ProxyConfiguration): AsyncGenerator<T[], undefined, void>;
  triggerAction<In = unknown, Out = object>(
    providerConfigKey: string,
    connectionId: string,
    actionName: string,
    input?: In
  ): Promise<Out>;
  zodValidateInput<T = any, Z = any>({
    zodSchema,
    input,
  }: {
    zodSchema: ZodSchema<Z>;
    input: T;
  }): Promise<SafeParseSuccess<Z>>;
  triggerSync(
    providerConfigKey: string,
    connectionId: string,
    sync: string | { name: string; variant: string },
    syncMode?: PostPublicTrigger['Body']['sync_mode'] | boolean
  ): Promise<void | string>;
  startSync(
    providerConfigKey: string,
    syncs: (string | { name: string; variant: string })[],
    connectionId?: string
  ): Promise<void>;
  /**
   * Uncontrolled fetch is a regular fetch without retry or credentials injection.
   * Only use that method when you want to access resources that are unrelated to the current connection/provider.
   */
  uncontrolledFetch(options: {
    url: URL;
    method?: HTTP_METHOD;
    headers?: Record<string, string> | undefined;
    body?: string | null;
  }): Promise<Response>;
  tryAcquireLock(props: { key: string; ttlMs: number }): Promise<boolean>;
  releaseLock(props: { key: string }): Promise<boolean>;
  private sendLogToPersist;
  private logAPICall;
}
export declare class NangoSync extends NangoAction {
  variant: string;
  lastSyncDate?: Date;
  track_deletes: boolean;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]>;
  rawDeleteOutput?: Map<string, unknown[]>;
  stubbedMetadata?: Metadata | undefined;
  private batchSize;
  constructor(config: NangoProps);
  /**
   * @deprecated please use batchSave
   */
  batchSend<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchSave<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchDelete<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchUpdate<T extends object>(results: T[], model: string): Promise<boolean | null>;
  getMetadata<T = Metadata>(): Promise<T>;
  setMergingStrategy(
    merging: { strategy: 'ignore_if_modified_after' | 'override' },
    model: string
  ): Promise<void>;
  getRecordsByIds<K = string | number, T = any>(ids: K[], model: string): Promise<Map<K, T>>;
}
/**
 * @internal
 *
 * This function will enable tracing on the SDK
 * It has been split from the actual code to avoid making the code too dirty and to easily enable/disable tracing if there is an issue with it
 */
export declare function instrumentSDK(rawNango: NangoAction | NangoSync): NangoAction | NangoSync;
export {};

// ------ /SDK

// ------ Flows
export const NangoFlows = [
  {
    providerConfigKey: 'harvest',
    syncs: [],
    actions: [
      {
        name: 'list-time-entries',
        type: 'action',
        description: 'Lists time entries from Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntriesInput',
        output: ['HarvestTimeEntryList'],
        usedModels: [
          'HarvestTimeEntryList',
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestTimeEntriesInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-time-entries',
        },
      },
      {
        name: 'get-time-entry',
        type: 'action',
        description: 'Gets a specific time entry by ID.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestTimeEntryInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-time-entry',
        },
      },
      {
        name: 'add-historical-time-entry',
        type: 'action',
        description:
          'Adds a completed time entry for a specific duration or start/end time. Checks company settings for duration vs timestamp tracking.',
        version: '',
        scopes: [],
        input: 'HarvestAddHistoricalTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestAddHistoricalTimeEntryInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/add-historical-time-entry',
        },
      },
      {
        name: 'start-timer',
        type: 'action',
        description:
          'Starts a new timer for a task. Checks company settings for duration vs timestamp tracking.',
        version: '',
        scopes: [],
        input: 'HarvestStartTimerInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestStartTimerInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/start-timer',
        },
      },
      {
        name: 'update-time-entry',
        type: 'action',
        description: 'Updates an existing time entry in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestUpdateTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestUpdateTimeEntryInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-time-entry',
        },
      },
      {
        name: 'stop-timer',
        type: 'action',
        description: 'Stops a running time entry in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestTimeEntryInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/v2/time_entries/id/stop',
        },
      },
      {
        name: 'delete-time-entry',
        type: 'action',
        description: 'Deletes a time entry in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntryInput',
        output: ['HarvestDeleteTimeEntryOutput'],
        usedModels: ['HarvestDeleteTimeEntryOutput', 'HarvestTimeEntryInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-time-entry',
        },
      },
      {
        name: 'list-projects',
        type: 'action',
        description: 'Lists projects from Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestProjectsInput',
        output: ['HarvestProjectList'],
        usedModels: [
          'HarvestProjectList',
          'HarvestProject',
          'HarvestClientReference',
          'HarvestPaginationLinks',
          'HarvestProjectsInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-projects',
        },
      },
      {
        name: 'get-project',
        type: 'action',
        description: 'Gets a specific project by ID.',
        version: '',
        scopes: [],
        input: 'HarvestProjectInput',
        output: ['HarvestProject'],
        usedModels: ['HarvestProject', 'HarvestClientReference', 'HarvestProjectInput'],
        endpoint: {
          method: 'GET',
          path: '/get-project',
        },
      },
      {
        name: 'delete-project',
        type: 'action',
        description: 'Deletes a project in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestProjectInput',
        output: ['HarvestDeleteProjectOutput'],
        usedModels: ['HarvestDeleteProjectOutput', 'HarvestProjectInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-project',
        },
      },
      {
        name: 'create-project',
        type: 'action',
        description: 'Creates a new project in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestCreateProjectInput',
        output: ['HarvestProject'],
        usedModels: ['HarvestProject', 'HarvestClientReference', 'HarvestCreateProjectInput'],
        endpoint: {
          method: 'POST',
          path: '/v2/projects',
        },
      },
      {
        name: 'list-clients',
        type: 'action',
        description: 'Lists clients from Harvest.',
        version: '',
        scopes: [],
        input: null,
        output: ['HarvestClientList'],
        usedModels: ['HarvestClientList', 'HarvestClient'],
        endpoint: {
          method: 'GET',
          path: '/list-clients',
        },
      },
      {
        name: 'get-client',
        type: 'action',
        description: 'Gets a specific client by ID.',
        version: '',
        scopes: [],
        input: 'HarvestClientInput',
        output: ['HarvestClient'],
        usedModels: ['HarvestClient', 'HarvestClientInput'],
        endpoint: {
          method: 'GET',
          path: '/get-client',
        },
      },
      {
        name: 'create-client',
        type: 'action',
        description: 'Creates a new client in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestCreateClientInput',
        output: ['HarvestClient'],
        usedModels: ['HarvestClient', 'HarvestCreateClientInput'],
        endpoint: {
          method: 'POST',
          path: '/v2/clients',
        },
      },
      {
        name: 'list-tasks',
        type: 'action',
        description: 'Lists tasks from Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTasksInput',
        output: ['HarvestTaskList'],
        usedModels: ['HarvestTaskList', 'HarvestTask', 'HarvestTasksInput'],
        endpoint: {
          method: 'GET',
          path: '/list-tasks',
        },
      },
      {
        name: 'list-project-tasks',
        type: 'action',
        description: 'Lists task assignments for a specific project in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestProjectTasksInput',
        output: ['HarvestProjectTaskList'],
        usedModels: [
          'HarvestProjectTaskList',
          'HarvestProjectTask',
          'ProjectReference',
          'TaskInAssignment',
          'PaginationLinks',
          'HarvestProjectTasksInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-project-tasks',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
] as const;
// ------ /Flows
