import type { NangoAction, ListDocumentsInput, GoogleDriveDocumentList } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Lists documents in Google Drive with optional filtering by folder ID and document type.
 *
 * @param nango - The Nango SDK instance
 * @param input - Optional parameters for filtering documents
 * @returns A list of documents and a nextPageToken if more results are available
 */
export default async function runAction(
  nango: NangoAction,
  input: ListDocumentsInput = {}
): Promise<GoogleDriveDocumentList | NangoError> {
  try {
    let query = "mimeType != 'application/vnd.google-apps.folder'";
    if (input.folderId) {
      query += ` and '${input.folderId}' in parents`;
    } else {
      query += " and 'root' in parents";
    }

    if (input.mimeType) {
      query += ` and mimeType = '${input.mimeType}'`;
    }

    const params: Record<string, string | number> = {
      q: query,
      fields:
        'files(id,name,mimeType,webViewLink,modifiedTime,createdTime,parents,size),nextPageToken',
      supportsAllDrives: 'true',
      includeItemsFromAllDrives: 'true',
    };

    if (input.pageSize) {
      params['pageSize'] = input.pageSize;
    } else {
      params['pageSize'] = 100;
    }

    if (input.pageToken) {
      params['pageToken'] = input.pageToken;
    }

    if (input.orderBy) {
      params['orderBy'] = input.orderBy;
    } else {
      params['orderBy'] = 'modifiedTime desc';
    }

    const response = await nango.get({
      endpoint: 'drive/v3/files',
      params,
    });

    if (response.status !== 200) {
      console.error('Google Drive API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `Google Drive API Error: Failed to list documents: Status Code ${response.status}`,
        },
      };
    }

    return {
      documents: response.data.files || [],
      nextPageToken: response.data.nextPageToken,
    };
  } catch (error: any) {
    console.error('Error listing Google Drive documents:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while listing Google Drive documents.';
    return { error: { status, message } };
  }
}
