import type { NangoAction, GoogleDriveFolderList } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Lists folders at the root level of Google Drive.
 *
 * @param nango - The Nango SDK instance
 * @param input - Optional parameters for the API request
 * @returns A list of root folders and a nextPageToken if more results are available
 */
export default async function runAction(
  nango: NangoAction,
  input: any = {}
): Promise<GoogleDriveFolderList | NangoError> {
  try {
    const query = "mimeType='application/vnd.google-apps.folder' and 'root' in parents";

    const params: Record<string, string | number> = {
      q: query,
      fields: 'files(id,name,mimeType,webViewLink,modifiedTime,createdTime,parents),nextPageToken',
      supportsAllDrives: 'true',
      includeItemsFromAllDrives: 'true',
    };

    if (input.pageSize) {
      params['pageSize'] = input.pageSize;
    } else {
      params['pageSize'] = 100;
    }

    if (input.pageToken) {
      params['pageToken'] = input.pageToken;
    }

    if (input.orderBy) {
      params['orderBy'] = input.orderBy;
    } else {
      params['orderBy'] = 'name';
    }

    const response = await nango.get({
      endpoint: 'drive/v3/files',
      params,
    });

    if (response.status !== 200) {
      console.error('Google Drive API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `Google Drive API Error: Failed to list root folders: Status Code ${response.status}`,
        },
      };
    }

    return {
      folders: response.data.files || [],
      nextPageToken: response.data.nextPageToken,
    };
  } catch (error: any) {
    console.error('Error listing Google Drive root folders:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while listing Google Drive root folders.';
    return { error: { status, message } };
  }
}
