---
name: New Integration / Enhancement Request
about: Suggest a new Nango integration or an enhancement for MCP use
title: '[FEAT] Provider: Brief description of request'
labels: enhancement, new integration
assignees: ''
---

## What integration/enhancement are you requesting?
Please describe the new provider integration or the enhancement to an existing one.
- **Provider Name:** [e.g., Slack, Notion, GitHub]
- **Desired Actions/Syncs:** [e.g., `send-message`, `create-page`, `list-issues`, `sync-contacts`]

## Why is this needed for MCP?
Explain how this integration or enhancement would be useful in an MCP server context. What MCP tools or resources would this enable?

## Describe the solution you'd like
A clear and concise description of the desired functionality.
- What inputs should the actions/syncs take?
- What outputs should they produce?

## Provider API Documentation
Please provide links to the relevant API documentation for the provider and the specific endpoints needed.
- [Link to main developer docs]
- [Link to specific endpoint docs, if known]

## Authentication Method
What authentication method does the provider use?
- [ ] OAuth 2.0 (Authorization Code)
- [ ] OAuth 2.0 (Client Credentials)
- [ ] API Key
- [ ] Other (Please describe)

## Additional context
Add any other context, examples, or use cases here.
