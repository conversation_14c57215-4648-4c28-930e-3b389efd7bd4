import type { NangoSync, ProxyConfiguration, DropboxSyncDocument } from '../../models';
import type { DropboxFile, DropboxFileList, DropboxSyncScopeMetadata } from '../types';

const batchSize = 100;

export default async function fetchData(nango: NangoSync): Promise<void> {
  const metadata = (await nango.getMetadata<DropboxSyncScopeMetadata>()) || {};
  const folders = metadata.folders && metadata.folders.length > 0 ? [...metadata.folders] : [''];
  const files = metadata.files ? [...metadata.files] : [];
  const depthLimit = metadata.folders || metadata.files ? Infinity : 1;

  const batch: DropboxSyncDocument[] = [];
  for (const folder of folders) {
    await fetchFolder(nango, folder, depthLimit, batch, 0);
  }

  for (const filePath of files) {
    const metadata = await fetchFile(nango, filePath);
    batch.push(metadata);
    if (batch.length >= batchSize) {
      await nango.batchSave(batch, 'DropboxSyncDocument');
      batch.length = 0;
    }
  }

  if (batch.length) {
    await nango.batchSave(batch, 'DropboxSyncDocument');
  }
}

async function fetchFolder(
  nango: NangoSync,
  path: string,
  depthLimit: number,
  batch: DropboxSyncDocument[],
  depth: number
): Promise<void> {
  const config: ProxyConfiguration = {
    endpoint: `/2/files/list_folder`,
    retries: 10,
    data: {
      path,
      limit: 100,
      recursive: false,
      include_mounted_folders: true,
      include_non_downloadable_files: false,
    },
  };

  let hasMore = true;
  let cursor: string | undefined;

  do {
    const response = await nango.post<DropboxFileList>(
      cursor
        ? {
            endpoint: `/2/files/list_folder/continue`,
            retries: 10,
            data: { cursor },
          }
        : config
    );

    const { entries, has_more, cursor: newCursor } = response.data;
    cursor = newCursor;
    hasMore = has_more;

    for (const entry of entries) {
      if (entry['.tag'] === 'file') {
        batch.push({
          id: entry.id || entry.path_lower,
          url: entry.path_lower,
          mimeType: '',
          title: entry.name,
          updatedAt: entry.client_modified ?? '',
        });
        if (batch.length >= batchSize) {
          await nango.batchSave(batch, 'DropboxSyncDocument');
          batch.length = 0;
        }
      } else if (entry['.tag'] === 'folder' && depth + 1 <= depthLimit) {
        await fetchFolder(nango, entry.path_lower, depthLimit, batch, depth + 1);
      }
    }
  } while (hasMore);
}

async function fetchFile(nango: NangoSync, path: string): Promise<DropboxSyncDocument> {
  const config: ProxyConfiguration = {
    endpoint: '/2/files/get_metadata',
    retries: 10,
    data: { path },
  };

  const response = await nango.post<DropboxFile>(config);
  const { data } = response;
  return {
    id: data.id || data.path_lower,
    url: data.path_lower,
    mimeType: data.content_type ?? '',
    title: data.name,
    updatedAt: data.server_modified ?? data.client_modified ?? '',
  };
}
