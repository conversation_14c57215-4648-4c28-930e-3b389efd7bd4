import type { DropboxCopyInput, DropboxEntry, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: DropboxCopyInput
): Promise<DropboxEntry | NangoError> {
  try {
    const { from_path, to_path, allow_shared_folder = false, autorename = false } = input;

    if (!from_path) {
      return { error: { status: 400, message: 'Input validation failed: from_path is required' } };
    }

    if (!to_path) {
      return { error: { status: 400, message: 'Input validation failed: to_path is required' } };
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/2/files/copy_v2',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        from_path,
        to_path,
        allow_shared_folder,
        autorename,
      },
      retries: 3,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error copying file/folder in Dropbox:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error_summary ||
      error?.message ||
      'An unknown error occurred while copying the file/folder in Dropbox.';
    return { error: { status, message } };
  }
}
