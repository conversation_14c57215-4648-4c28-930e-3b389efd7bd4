import type { DropboxFile, DropboxGetFileInput, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: DropboxGetFileInput
): Promise<DropboxFile | NangoError> {
  try {
    const { path } = input;

    if (!path) {
      return { error: { status: 400, message: 'Input validation failed: Path is required' } };
    }

    const metadataResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/2/files/get_metadata',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        path,
        include_media_info: false,
        include_deleted: false,
        include_has_explicit_shared_members: false,
      },
      retries: 3,
    });

    if (metadataResponse.data['.tag'] !== 'file') {
      return {
        error: { status: 400, message: 'Validation failed: The specified path is not a file.' },
      };
    }

    const sharedLinkResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/2/sharing/list_shared_links',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        path,
        direct_only: true,
      },
      retries: 3,
    });

    let linkData;
    if (sharedLinkResponse.data.links && sharedLinkResponse.data.links.length > 0) {
      linkData = sharedLinkResponse.data.links[0];
    } else {
      const createLinkResponse = await nango.proxy({
        method: 'POST',
        endpoint: '/2/sharing/create_shared_link_with_settings',
        headers: {
          'Content-Type': 'application/json',
        },
        data: { path },
        retries: 3,
      });
      linkData = createLinkResponse.data;
    }

    let downloadUrl = linkData.url;
    downloadUrl = downloadUrl.replace('www.dropbox.com', 'dl.dropboxusercontent.com');

    return {
      id: metadataResponse.data.id,
      name: metadataResponse.data.name,
      path_display: metadataResponse.data.path_display,
      path_lower: metadataResponse.data.path_lower,
      size: metadataResponse.data.size,
      content_hash: metadataResponse.data.content_hash,
      server_modified: metadataResponse.data.server_modified,
      content_type: metadataResponse.data.content_type || 'application/octet-stream',
      download_url: downloadUrl,
    };
  } catch (error: any) {
    console.error('Error getting Dropbox file:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error_summary ||
      error?.message ||
      'An unknown error occurred while getting the Dropbox file.';
    return { error: { status, message } };
  }
}
