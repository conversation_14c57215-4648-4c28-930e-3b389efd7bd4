integrations:
    linkedin:
        actions:
            get-user-profile:
                endpoint: GET /get-user-profile
                description: Gets the authenticated user's profile information from LinkedIn.
                output: LinkedInUserProfile
                scopes:
                  - r_liteprofile
                  - r_emailaddress
            send-post:
                endpoint: POST /send-post
                description: Creates a new post on LinkedIn.
                input: LinkedInPostInput
                output: LinkedInPostOutput
                scopes:
                  - w_member_social # Scope for posting
                  - openid          # OIDC scope
                  - profile         # OIDC scope for user info (sub, name, etc.)

models:
    LinkedInUserProfile:
        sub: string              # User ID
        email_verified: boolean  # Whether the email is verified
        name: string             # Full name
        locale: object           # Locale information
        given_name: string       # First name
        family_name: string      # Last name
        email: string            # Email address
        picture: string          # Profile picture URL
    LinkedInPostInput:
        text: string             # The content of the post (Required)
        visibility: string       # Visibility of the post (PUBLIC or CONNECTIONS), defaults to PUBLIC if omitted (Optional)
    LinkedInPostOutput:
        id: string               # The URN of the created post (e.g., urn:li:share:12345)
