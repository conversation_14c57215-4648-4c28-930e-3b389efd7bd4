import type { NangoAction, SlackGetPermalinkInput, SlackPermalinkOutput } from '../../models';

interface SlackPermalinkResponse {
  ok: boolean;
  permalink?: string;
  channel?: string;
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackGetPermalinkInput
): Promise<SlackPermalinkOutput | ErrorResponse> {
  if (!input || !input.channel || !input.message_ts) {
    return {
      error: {
        status: 400,
        message: 'Channel ID and message timestamp are required to get a permalink.',
      },
    };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const params = {
      channel: input.channel,
      message_ts: input.message_ts,
    };

    const config = {
      method: 'GET' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'chat.getPermalink',
      params: params,
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
      retries: 3,
    };

    const response = await nango.proxy<SlackPermalinkResponse>(config);

    if (!response.data.ok) {
      const errorMessage = `Slack API error: ${response.data.error || 'Unknown error getting permalink'}`;
      await nango.log(errorMessage);
      const status = response.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    const result: SlackPermalinkOutput = {
      ok: response.data.ok,
      ...(response.data.permalink && { permalink: response.data.permalink }),
      ...(response.data.channel && { channel: response.data.channel }),
    };

    return result;
  } catch (error: any) {
    await nango.log(`Error getting Slack message permalink: ${error.message}`);
    const errorMessage =
      error.response?.data?.error ||
      error.message ||
      'Unknown error getting Slack message permalink';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      await nango.log('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      await nango.log('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    await nango.log(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}
