import type { NangoAction, SlackSearchMessagesInput, SlackSearchResultList } from '../../models';

interface SlackSearchResponse {
  ok: boolean;
  query?: string;
  messages?: {
    matches?: any[];
    pagination?: any;
  };
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackSearchMessagesInput
): Promise<SlackSearchResultList | ErrorResponse> {
  if (!input || !input.query) {
    return { error: { status: 400, message: 'Search query is required.' } };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const params: { [key: string]: string | number } = {
      query: input.query,
    };
    if (input.sort !== undefined) {
      params['sort'] = input.sort;
    }
    if (input.sort_dir !== undefined) {
      params['sort_dir'] = input.sort_dir;
    }
    if (input.count !== undefined) {
      params['count'] = input.count;
    }
    if (input.page !== undefined) {
      params['page'] = input.page;
    }

    const config = {
      method: 'GET' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'search.messages',
      params: params,
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
      retries: 3,
    };

    const response = await nango.proxy<SlackSearchResponse>(config);

    if (!response.data.ok) {
      const errorMessage = `Slack API error: ${response.data.error || 'Unknown error during search'}`;
      await nango.log(errorMessage);
      const status = response.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    const result: SlackSearchResultList = {
      ok: response.data.ok,
      query: response.data.query || input.query,
      messages: response.data.messages || { matches: [], pagination: {} },
    };

    return result;
  } catch (error: any) {
    await nango.log(`Error searching Slack messages: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error searching Slack messages';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      await nango.log('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      await nango.log('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    await nango.log(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}
