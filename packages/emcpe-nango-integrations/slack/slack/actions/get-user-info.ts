import type {
  NangoAction,
  SlackGetUserInfoInput,
  SlackUserInfo,
  SlackUserProfile,
} from '../../models';

interface SlackUserInfoResponse {
  ok: boolean;
  user?: SlackApiUser;
  error?: string;
}

interface SlackApiUser {
  id: string;
  name: string;
  is_bot: boolean;
  is_admin?: boolean;
  is_owner?: boolean;
  tz?: string;
  profile?: SlackApiUserProfile;
}

interface SlackApiUserProfile {
  real_name?: string;
  display_name?: string;
  email?: string;
  image_original?: string;
  image_512?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackGetUserInfoInput
): Promise<SlackUserInfo | ErrorResponse> {
  if (!input || !input.user) {
    return { error: { status: 400, message: 'User ID is required to get user info.' } };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const params = {
      user: input.user,
    };

    const config = {
      method: 'GET' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'users.info',
      params: params,
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
      retries: 3,
    };
    const response = await nango.proxy<SlackUserInfoResponse>(config);

    if (!response.data.ok || !response.data.user) {
      const errorMessage = `Slack API error: ${response.data.error || 'User not found or unknown error getting user info'}`;
      await nango.log(errorMessage);
      const status = response.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    const apiUser = response.data.user;
    const apiProfile = apiUser.profile || {};

    const userProfile: SlackUserProfile = {
      ...(apiProfile.real_name && { real_name: apiProfile.real_name }),
      ...(apiProfile.display_name && { display_name: apiProfile.display_name }),
      ...(apiProfile.email && { email: apiProfile.email }),
      ...(apiProfile.image_original && { image_original: apiProfile.image_original }),
      ...(apiProfile.image_512 && { image_512: apiProfile.image_512 }),
    };

    const userInfo: SlackUserInfo = {
      id: apiUser.id,
      name: apiUser.name,
      is_bot: apiUser.is_bot,
      ...(apiUser.is_admin !== undefined && { is_admin: apiUser.is_admin }),
      ...(apiUser.is_owner !== undefined && { is_owner: apiUser.is_owner }),
      ...(apiUser.tz && { tz: apiUser.tz }),
      ...(Object.keys(userProfile).length > 0 && { profile: userProfile }),
    };

    return userInfo;
  } catch (error: any) {
    await nango.log(`Error getting Slack user info: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error getting Slack user info';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      await nango.log('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      await nango.log('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    await nango.log(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}
