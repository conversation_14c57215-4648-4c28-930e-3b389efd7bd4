import type { NangoAction, SlackSendMessageInput, SlackSendMessageOutput } from '../../models';

interface SlackPostMessageResponse {
  ok: boolean;
  ts?: string;
  channel?: string;
  message?: {
    text?: string;
  };
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackSendMessageInput
): Promise<SlackSendMessageOutput | ErrorResponse> {
  if (!input.channel || !input.text) {
    return {
      error: { status: 400, message: 'Channel and text are required for sending a Slack message.' },
    };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const dataPayload = {
      channel: input.channel,
      text: input.text,
      ...(input.thread_ts && { thread_ts: input.thread_ts }),
    };

    const config = {
      method: 'POST' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'chat.postMessage',
      headers: {
        Authorization: `Bearer ${userToken}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      data: dataPayload,
      retries: 3,
    };

    const response = await nango.proxy<SlackPostMessageResponse>(config);

    if (!response.data.ok) {
      const errorMessage = `Slack API error: ${response.data.error || 'Unknown error sending message'}`;
      await nango.log(errorMessage);
      const status = response.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    if (!response.data.ts || !response.data.channel) {
      const errorMessage = 'Slack API response missing required fields (ts or channel).';
      await nango.log(errorMessage);
      return { error: { status: 500, message: errorMessage } };
    }

    const result: SlackSendMessageOutput = {
      ok: response.data.ok,
      ts: response.data.ts,
      channel: response.data.channel,
      ...(response.data.message?.text && { message_text: response.data.message.text }),
    };
    return result;
  } catch (error: any) {
    await nango.log(`Error sending Slack message: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error sending Slack message';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      await nango.log('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      await nango.log('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    await nango.log(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}
