import type { NangoAction, SlackAddReactionInput, SlackReactionOutput } from '../../models';

interface SlackReactionResponse {
  ok: boolean;
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackAddReactionInput
): Promise<SlackReactionOutput | ErrorResponse> {
  if (!input || !input.name || !input.channel || !input.timestamp) {
    return {
      error: {
        status: 400,
        message: 'Emoji name, channel ID, and message timestamp are required to add a reaction.',
      },
    };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const dataPayload = {
      name: input.name,
      channel: input.channel,
      timestamp: input.timestamp,
    };

    const config = {
      method: 'POST' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'reactions.add',
      headers: {
        Authorization: `Bearer ${userToken}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      data: dataPayload,
      retries: 3,
    };
    const response = await nango.proxy<SlackReactionResponse>(config);

    if (!response.data.ok) {
      const errorMessage = `Slack API error: ${response.data.error || 'Unknown error adding reaction'}`;
      await nango.log(errorMessage);
      const status = response.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    return {
      ok: response.data.ok,
    };
  } catch (error: any) {
    await nango.log(`Error adding Slack reaction: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error adding Slack reaction';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      await nango.log('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      await nango.log('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    await nango.log(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}
