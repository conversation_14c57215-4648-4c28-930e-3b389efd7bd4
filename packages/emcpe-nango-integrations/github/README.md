```
nango dryrun list-pull-requests -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife"}'
nango dryrun get-pull-request-files -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pullNumber": 36275}' --validation
nango dryrun get-pull-request -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pullNumber": 36275}' --validation
nango dryrun add-pull-request-review-comment -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pull_number": 36275, "body": "This is a test comment", "commit_id": "8411dc2782b4d0fe169ce3c22fd2d8da3c06be30", "path": "index.html", "line": 16, "side": "RIGHT", "diff_hunk": "@@ -15,6 +15,8 @@\n <p>\n Fork me? Fork you, @octocat!\n </p>\n-\\n+<p>\\n+ Conribution de Rhisland depuis un fork !\\n+ </p>\\n </body>\\n </html>"}' --validation
nango dryrun create-issue -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "title": "Test Issue from Nango Dryrun"}' --validation
nango dryrun create-pull-request-review -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pullNumber": 36275, "body": "Test review from Nango dryrun.", "event": "COMMENT"}' --validation
nango dryrun create-pull-request -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "title": "Test Pull Request", "head": "test-branch", "base": "main"}' --validation
nango dryrun create-repository -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"name": "test-repo-from-nango"}' --validation
nango dryrun delete-repository -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "test-repo-to-delete"}' --validation
nango dryrun get-issue -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "issue_number": 1}' --validation
nango dryrun get-pull-request-comments -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pullNumber": 36275}' --validation
nango dryrun get-pull-request-status -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pullNumber": 36275}' --validation
nango dryrun list-issues -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife"}' --validation
nango dryrun merge-pull-request -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pullNumber": 36275}' --validation
nango dryrun update-issue -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "issue_number": 1, "title": "Updated Test Issue"}' --validation
nango dryrun update-pull-request-branch -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pullNumber": 36275}' --validation
nango dryrun update-pull-request -e prod 2cb3ea83-b71f-4056-9c3b-efb56e77c60c --integration-id github --input '{"owner": "octocat", "repo": "Spoon-Knife", "pullNumber": 36275, "title": "Updated Pull Request Title"}' --validation
```
