import type { GithubListBranchesInput, GithubBranchList, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubListBranchesInput
): Promise<GithubBranchList | NangoError> {
  try {
    const { owner, repo, protected: isProtected, per_page, page } = input;

    if (!owner || !repo) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner and name are required',
        },
      };
    }

    const params: Record<string, any> = {};
    if (typeof isProtected !== 'undefined') params['protected'] = isProtected;
    if (per_page) params['per_page'] = per_page;
    if (page) params['page'] = page;

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/branches`,
      params,
    });

    return { branches: response.data };
  } catch (error: any) {
    console.error('Error fetching branches:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while fetching the branches.';
    return { error: { status, message } };
  }
}
