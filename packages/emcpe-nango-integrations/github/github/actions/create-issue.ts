import type { GithubCreateIssueInput, GithubIssue, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubCreateIssueInput
): Promise<GithubIssue | NangoError> {
  try {
    const { owner, repo, title, body, assignees, labels } = input;

    if (!owner || !repo || !title) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner, name, and issue title are required',
        },
      };
    }

    const payload: Record<string, any> = {
      title,
    };

    if (body !== undefined) payload['body'] = body;
    if (assignees !== undefined) payload['assignees'] = assignees;
    if (labels !== undefined) payload['labels'] = labels;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: `/repos/${owner}/${repo}/issues`,
      data: payload,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error creating issue:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while creating the issue.';
    return { error: { status, message } };
  }
}
