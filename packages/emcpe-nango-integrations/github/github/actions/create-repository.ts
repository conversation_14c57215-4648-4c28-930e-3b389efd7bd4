import type { GithubCreateRepositoryInput, GithubRepository, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubCreateRepositoryInput
): Promise<GithubRepository | NangoError> {
  try {
    const {
      name,
      description,
      private: isPrivate,
      has_issues,
      has_projects,
      has_wiki,
      auto_init,
      gitignore_template,
      license_template,
    } = input;

    if (!name) {
      return {
        error: { status: 400, message: 'Input validation failed: Repository name is required' },
      };
    }

    const payload: Record<string, any> = {
      name,
      private: isPrivate !== undefined ? isPrivate : true,
    };

    if (description !== undefined) payload['description'] = description;
    if (has_issues !== undefined) payload['has_issues'] = has_issues;
    if (has_projects !== undefined) payload['has_projects'] = has_projects;
    if (has_wiki !== undefined) payload['has_wiki'] = has_wiki;
    if (auto_init !== undefined) payload['auto_init'] = auto_init;
    if (gitignore_template !== undefined) payload['gitignore_template'] = gitignore_template;
    if (license_template !== undefined) payload['license_template'] = license_template;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/user/repos',
      data: payload,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error creating repository:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while creating the repository.';
    return { error: { status, message } };
  }
}
