import type { GithubPullRequestFileList, GithubPullRequestInput, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubPullRequestInput
): Promise<GithubPullRequestFileList | NangoError> {
  try {
    const { owner, repo, pullNumber } = input;

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/pulls/${pullNumber}/files`,
    });

    if (response.status !== 200) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to get pull request files: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return {
      files: response.data,
    };
  } catch (error: any) {
    console.error('Error getting pull request files:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while getting the pull request files.';
    return { error: { status, message } };
  }
}
