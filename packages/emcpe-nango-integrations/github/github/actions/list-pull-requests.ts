import type { GithubListPullRequestsInput, GithubPullRequestList, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubListPullRequestsInput
): Promise<GithubPullRequestList | NangoError> {
  try {
    const { owner, repo, state, head, base, sort, direction, per_page, page } = input;

    const params: { [key: string]: any } = {
      state,
      head,
      base,
      sort,
      direction,
      per_page,
      page,
    };

    Object.keys(params).forEach(key => (params[key] === undefined ? delete params[key] : {}));

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/pulls`,
      params: params,
    });

    if (response.status !== 200) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to list pull requests: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return { pull_requests: response.data };
  } catch (error: any) {
    console.error('Error listing pull requests:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while listing pull requests.';
    return { error: { status, message } };
  }
}
