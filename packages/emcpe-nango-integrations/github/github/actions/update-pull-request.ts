import type { GithubPullRequest, GithubUpdatePullRequestInput, NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubUpdatePullRequestInput
): Promise<GithubPullRequest | NangoError> {
  try {
    const { owner, repo, pullNumber, ...updateParams } = input;

    const filteredUpdateParams: { [key: string]: any } = Object.fromEntries(
      Object.entries(updateParams).filter(([_, v]) => v !== undefined)
    );

    if (Object.keys(filteredUpdateParams).length === 0) {
      return {
        error: { status: 400, message: 'Input validation failed: No update parameters provided.' },
      };
    }

    const response = await nango.proxy({
      method: 'PATCH',
      endpoint: `/repos/${owner}/${repo}/pulls/${pullNumber}`,
      data: filteredUpdateParams,
    });

    if (response.status !== 200) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to update pull request: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return response.data;
  } catch (error: any) {
    console.error('Error updating pull request:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while updating the pull request.';
    return { error: { status, message } };
  }
}
