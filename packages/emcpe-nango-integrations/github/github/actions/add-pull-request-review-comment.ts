import type {
  GithubAddPullRequestReviewCommentInput,
  GithubPullRequestComment,
  NangoAction,
} from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubAddPullRequestReviewCommentInput
): Promise<GithubPullRequestComment | NangoError> {
  const {
    owner,
    repo,
    pull_number,
    body,
    commit_id,
    path,
    subject_type,
    line,
    side,
    start_line,
    start_side,
    in_reply_to,
  } = input;

  try {
    if (in_reply_to) {
      const response = await nango.proxy({
        method: 'POST',
        endpoint: `/repos/${owner}/${repo}/pulls/${pull_number}/comments/${in_reply_to}/replies`,
        data: {
          body: body,
        },
      });

      if (response.status !== 200) {
        console.error('GitHub API Error:', response.status, response.data);
        return {
          error: {
            status: response.status,
            message: `GitHub API Error: Failed to add pull request review comment reply: ${response.status} ${JSON.stringify(response.data)}`,
          },
        };
      }

      return response.data;
    } else {
      if (!commit_id || !path) {
        return {
          error: {
            status: 400,
            message:
              'Input validation failed: commit_id and path are required unless in_reply_to is specified',
          },
        };
      }

      const data: { [key: string]: any } = {
        body: body,
        commit_id: commit_id,
        path: path,
      };

      if (subject_type) {
        data['subject_type'] = subject_type;
      }
      if (line) {
        data['line'] = line;
      }
      if (side) {
        data['side'] = side;
      }
      if (start_line) {
        data['start_line'] = start_line;
      }
      if (start_side) {
        data['start_side'] = start_side;
      }

      const response = await nango.proxy({
        method: 'POST',
        endpoint: `/repos/${owner}/${repo}/pulls/${pull_number}/comments`,
        data: data,
      });

      if (response.status !== 200) {
        console.error('GitHub API Error:', response.status, response.data);
        return {
          error: {
            status: response.status,
            message: `GitHub API Error: Failed to add pull request review comment: ${response.status} ${JSON.stringify(response.data)}`,
          },
        };
      }

      return response.data;
    }
  } catch (error: any) {
    console.error('Error adding pull request review comment:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while adding the pull request review comment.';
    return { error: { status, message } };
  }
}
