export interface GithubRepositoryInput {
  owner: string;
  repo: string;
}

export interface GithubIssueCreator {
  login: string;
  id: number;
  node_id: string;
  avatar_url: string;
  gravatar_id: string;
  url: string;
  html_url: string;
  followers_url: string;
  following_url: string;
  gists_url: string;
  starred_url: string;
  subscriptions_url: string;
  organizations_url: string;
  repos_url: string;
  events_url: string;
  received_events_url: string;
  type: string;
  user_view_type: string;
  site_admin: boolean;
}

export interface GithubRepository {
  id: number;
  node_id: string;
  name: string;
  full_name: string;
  private: boolean;
  owner: GithubIssueCreator;
  html_url: string;
  description: string | null;
  fork: boolean;
  url: string;
  forks_url: string;
  keys_url: string;
  collaborators_url: string;
  teams_url: string;
  hooks_url: string;
  issue_events_url: string;
  events_url: string;
  assignees_url: string;
  branches_url: string;
  tags_url: string;
  blobs_url: string;
  git_tags_url: string;
  git_refs_url: string;
  trees_url: string;
  statuses_url: string;
  languages_url: string;
  stargazers_url: string;
  contributors_url: string;
  subscribers_url: string;
  subscription_url: string;
  commits_url: string;
  git_commits_url: string;
  comments_url: string;
  issue_comment_url: string;
  contents_url: string;
  compare_url: string;
  merges_url: string;
  archive_url: string;
  downloads_url: string;
  issues_url: string;
  pulls_url: string;
  milestones_url: string;
  notifications_url: string;
  labels_url: string;
  releases_url: string;
  deployments_url: string;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  git_url: string;
  ssh_url: string;
  clone_url: string;
  svn_url: string;
  homepage: string | null;
  size: number;
  stargazers_count: number;
  watchers_count: number;
  language: string | null;
  has_issues: boolean;
  has_projects: boolean;
  has_downloads: boolean;
  has_wiki: boolean;
  has_pages: boolean;
  has_discussions: boolean;
  forks_count: number;
  mirror_url: string | null;
  archived: boolean;
  disabled: boolean;
  open_issues_count: number;
  license: string | null;
  allow_forking: boolean;
  is_template: boolean;
  web_commit_signoff_required: boolean;
  topics: string[];
  visibility: string;
  forks: number;
  open_issues: number;
  watchers: number;
  default_branch: string;
}

export interface GithubRepositoryList {
  note: string | null;
  repositories: GithubRepository[];
}

export interface GithubCreateRepositoryInput {
  name: string;
  description?: string;
  private?: boolean;
  has_issues?: boolean;
  has_projects?: boolean;
  has_wiki?: boolean;
  auto_init?: boolean;
  gitignore_template?: string;
  license_template?: string;
}

export interface GithubUpdateRepositoryInput {
  owner: string;
  repo: string;
  name?: string;
  description?: string;
  private?: boolean;
  has_issues?: boolean;
  has_projects?: boolean;
  has_wiki?: boolean;
  default_branch?: string;
}

export interface GithubDeleteRepositoryOutput {
  success: boolean;
  message: string;
}

export interface GithubCreateOrganizationRepositoryInput {
  org: string;
  name: string;
  description?: string;
  homepage?: string;
  private?: boolean;
  has_issues?: boolean;
  has_projects?: boolean;
  has_wiki?: boolean;
}

export interface GithubIssuesInput {
  owner: string;
  repo: string;
  state?: string;
  sort?: string;
  direction?: string;
  per_page?: number;
  page?: number;
}

export interface GithubIssueInput {
  owner: string;
  repo: string;
  issue_number: number;
}

export interface GithubIssueCreatorLite {
  login: string;
  id: number;
  avatar_url: string;
  html_url: string;
}

export interface GithubIssueLabel {
  id: number;
  name: string;
  color: string;
  description: string;
}

export interface GithubSubIssuesSummary {
  total: number;
  completed: number;
  percent_completed: number;
}

export interface GithubReactions {
  url: string;
  total_count: number;
  '+1': number;
  '-1': number;
  laugh: number;
  hooray: number;
  confused: number;
  heart: number;
  rocket: number;
  eyes: number;
}

export interface GithubIssue {
  id: number;
  node_id: string;
  url: string;
  repository_url: string;
  labels_url: string;
  comments_url: string;
  events_url: string;
  html_url: string;
  number: number;
  title: string;
  state: string;
  locked: boolean;
  body: string | null;
  user: GithubIssueCreatorLite | GithubIssueCreator;
  labels: GithubIssueLabel[];
  assignee: Record<string, any> | null;
  assignees: Record<string, any>[];
  milestone: string | null;
  comments: number;
  created_at: string;
  updated_at: string;
  closed_at: string | null;
  author_association: string;
  active_lock_reason: string | null;
  sub_issues_summary: GithubSubIssuesSummary;
  closed_by: GithubIssueCreator | null;
  reactions: GithubReactions;
  timeline_url: string;
  performed_via_github_app: Record<string, any> | null;
  state_reason: string | null;
}

export interface GithubIssueAssignee {
  id: number;
  login: string;
  avatar_url: string;
  html_url: string;
}

export interface GithubIssueList {
  issues: GithubIssue[];
}

export interface GithubCreateIssueInput {
  owner: string;
  repo: string;
  title: string;
  body?: string;
  assignees?: string[];
  labels?: string[];
}

export interface GithubUpdateIssueInput {
  owner: string;
  repo: string;
  issue_number: number;
  title?: string;
  body?: string;
  state?: string;
  assignees?: string[];
  labels?: string[];
}

export interface GithubPullRequestInput {
  owner: string;
  repo: string;
  pullNumber: number;
}

export interface GithubTeamRef {
  id: number;
  name: string;
}

export interface GithubPullRequest {
  url: string;
  id: number;
  node_id: string;
  html_url: string;
  diff_url: string;
  patch_url: string;
  issue_url: string;
  number: number;
  state: string;
  locked: boolean;
  title: string;
  user: GithubIssueCreator;
  body: string | null;
  created_at: string;
  updated_at: string;
  closed_at: string | null;
  merged_at: string | null;
  merge_commit_sha: string | null;
  assignee: GithubIssueAssignee | null;
  assignees: GithubIssueAssignee[];
  requested_reviewers: GithubIssueAssignee[];
  requested_teams: GithubTeamRef[];
  labels: GithubIssueLabel[];
  milestone: string | null;
  draft: boolean;
  commits_url: string;
  review_comments_url: string;
  review_comment_url: string;
  comments_url: string;
  statuses_url: string;
  head: Record<string, any>;
  base: Record<string, any>;
  _links: Record<string, any>;
  author_association: string;
  auto_merge: Record<string, any> | null;
  active_lock_reason: string | null;
  merged: boolean;
  mergeable: boolean | null;
  rebaseable: boolean | null;
  mergeable_state: string;
  merged_by: GithubIssueCreator | null;
  comments: number;
  review_comments: number;
  maintainer_can_modify: boolean;
  commits: number;
  additions: number;
  deletions: number;
  changed_files: number;
}

export interface GithubPullRequestBranch {
  label: string;
  ref: string;
  sha: string;
  user: GithubIssueCreator;
}

export interface GithubTeam {
  id: number;
  name: string;
  slug: string;
  description: string;
  privacy: string;
  url: string;
  html_url: string;
  members_url: string;
  repositories_url: string;
  permission: string;
}

export interface GithubUpdatePullRequestInput {
  owner: string;
  repo: string;
  pullNumber: number;
  title?: string;
  body?: string;
  state?: string;
  base?: string;
  maintainer_can_modify?: boolean;
}

export interface GithubListPullRequestsInput {
  owner: string;
  repo: string;
  state?: string;
  head?: string;
  base?: string;
  sort?: string;
  direction?: string;
  per_page?: number;
  page?: number;
}

export interface GithubPullRequestList {
  pull_requests: GithubPullRequest[];
}

export interface GithubMergePullRequestInput {
  owner: string;
  repo: string;
  pullNumber: number;
  commit_title?: string;
  commit_message?: string;
  merge_method?: string;
}

export interface GithubMergeResult {
  sha: string;
  merged: boolean;
  message: string;
}

export interface GithubPullRequestFile {
  sha: string;
  filename: string;
  status: string;
  additions: number;
  deletions: number;
  changes: number;
  blob_url: string;
  raw_url: string;
  contents_url: string;
  patch: string;
}

export interface GithubPullRequestFileList {
  files: GithubPullRequestFile[];
}

export interface GithubStatus {
  url: string;
  id: number;
  node_id: string;
  state: string;
  context: string;
  description: string;
  target_url: string;
  created_at: string;
  updated_at: string;
}

export interface GithubRepositoryForGithubCombinedStatus {
  id: number;
  node_id: string;
  name: string;
  full_name: string;
  private: boolean;
  owner: GithubIssueCreator;
  html_url: string;
  description: string;
  fork: boolean;
  url: string;
  forks_url: string;
  keys_url: string;
  collaborators_url: string;
  teams_url: string;
  hooks_url: string;
  issue_events_url: string;
  events_url: string;
  assignees_url: string;
  branches_url: string;
  tags_url: string;
  blobs_url: string;
  git_tags_url: string;
  git_refs_url: string;
  trees_url: string;
  statuses_url: string;
  languages_url: string;
  stargazers_url: string;
  contributors_url: string;
  subscribers_url: string;
  subscription_url: string;
  commits_url: string;
  git_commits_url: string;
  comments_url: string;
  issue_comment_url: string;
  contents_url: string;
  compare_url: string;
  merges_url: string;
  archive_url: string;
  downloads_url: string;
  issues_url: string;
  pulls_url: string;
  milestones_url: string;
  notifications_url: string;
  labels_url: string;
  releases_url: string;
  deployments_url: string;
}

export interface GithubCombinedStatus {
  state: string;
  sha: string;
  total_count: number;
  statuses: GithubStatus[];
  repository: GithubRepositoryForGithubCombinedStatus;
  commit_url: string;
}

export interface GithubUpdatePullRequestBranchInput {
  owner: string;
  repo: string;
  pullNumber: number;
  expectedHeadSha?: string;
}

export interface GithubBranchUpdateResult {
  message: string;
  url: string;
}

export interface GithubBranch {
  name: string;
  commit: {
    sha: string;
    url: string;
  };
  protected: boolean;
  protection?: Record<string, any>;
  protection_url?: string;
}

export interface GithubBranchList {
  branches: GithubBranch[];
}

export interface GithubListBranchesInput {
  owner: string;
  repo: string;
  protected?: boolean;
  per_page?: number;
  page?: number;
}

export interface GithubPullRequestComment {
  id: number;
  node_id: string;
  url: string;
  pull_request_review_id: number;
  diff_hunk: string;
  path: string;
  position: number;
  original_position: number;
  commit_id: string;
  original_commit_id: string;
  user: GithubIssueCreator;
  body: string;
  created_at: string;
  updated_at: string;
  html_url: string;
  pull_request_url: string;
  author_association: string;
  _links: Record<string, any>;
  reactions: GithubReactions;
  start_line: number | null;
  original_start_line: number | null;
  start_side: string | null;
  line: number;
  original_line: number;
  side: string;
  in_reply_to_id?: number;
  subject_type: string;
}

export interface GithubPullRequestCommentList {
  comments: GithubPullRequestComment[];
}

export interface GithubAddPullRequestReviewCommentInput {
  owner: string;
  repo: string;
  pull_number: number;
  body: string;
  commit_id?: string;
  path?: string;
  subject_type?: string;
  line?: number;
  side?: string;
  start_line?: number;
  start_side?: string;
  in_reply_to?: number;
  diff_hunk?: string;
}

export interface GithubPullRequestReview {
  id: number;
  node_id: string;
  user: GithubIssueCreator;
  body: string;
  state: string;
  html_url: string;
  pull_request_url: string;
  submitted_at: string;
  commit_id: string;
  author_association: string;
  _links: Record<string, any>;
}

export interface GithubDraftReviewComment {
  path: string;
  position?: number;
  line?: number;
  side?: string;
  start_line?: number;
  start_side?: string;
  body: string;
}

export interface GithubCreatePullRequestReviewInput {
  owner: string;
  repo: string;
  pullNumber: number;
  body?: string;
  event: string;
  commitId?: string;
  comments?: GithubDraftReviewComment[];
}

export interface GithubCreatePullRequestInput {
  owner: string;
  repo: string;
  title: string;
  body?: string;
  head: string;
  base: string;
  draft?: boolean;
  maintainer_can_modify?: boolean;
}

import type { Nango } from '@nangohq/node';
import type {
  AxiosInstance,
  AxiosInterceptorManager,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import type {
  ApiEndUser,
  DBSyncConfig,
  DBTeam,
  GetPublicIntegration,
  HTTP_METHOD,
  RunnerFlags,
  PostPublicTrigger,
} from '@nangohq/types';
import type { ZodSchema, SafeParseSuccess } from 'zod';

export declare const oldLevelToNewLevel: {
  readonly debug: 'debug';
  readonly info: 'info';
  readonly warn: 'warn';
  readonly error: 'error';
  readonly verbose: 'debug';
  readonly silly: 'debug';
  readonly http: 'info';
};
type LogLevel = 'info' | 'debug' | 'error' | 'warn' | 'http' | 'verbose' | 'silly';
interface Pagination {
  type: string;
  limit?: number;
  response_path?: string;
  limit_name_in_request: string;
  in_body?: boolean;
  on_page?: (paginationState: {
    nextPageParam?: string | number | undefined;
    response: AxiosResponse;
  }) => Promise<void>;
}
interface CursorPagination extends Pagination {
  cursor_path_in_response: string;
  cursor_name_in_request: string;
}
interface LinkPagination extends Pagination {
  link_rel_in_response_header?: string;
  link_path_in_response_body?: string;
}
interface OffsetPagination extends Pagination {
  offset_name_in_request: string;
  offset_start_value?: number;
  offset_calculation_method?: 'per-page' | 'by-response-size';
}
interface RetryHeaderConfig {
  at?: string;
  after?: string;
}
export interface ProxyConfiguration {
  endpoint: string;
  providerConfigKey?: string;
  connectionId?: string;
  method?:
    | 'GET'
    | 'POST'
    | 'PATCH'
    | 'PUT'
    | 'DELETE'
    | 'get'
    | 'post'
    | 'patch'
    | 'put'
    | 'delete';
  headers?: Record<string, string>;
  params?: string | Record<string, string | number>;
  data?: unknown;
  retries?: number;
  baseUrlOverride?: string;
  paginate?: Partial<CursorPagination> | Partial<LinkPagination> | Partial<OffsetPagination>;
  retryHeader?: RetryHeaderConfig;
  responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream' | undefined;
  retryOn?: number[] | null;
}
export interface AuthModes {
  OAuth1: 'OAUTH1';
  OAuth2: 'OAUTH2';
  OAuth2CC: 'OAUTH2_CC';
  Basic: 'BASIC';
  ApiKey: 'API_KEY';
  AppStore: 'APP_STORE';
  Custom: 'CUSTOM';
  App: 'APP';
  None: 'NONE';
  TBA: 'TBA';
  Tableau: 'TABLEAU';
  Jwt: 'JWT';
  Bill: 'BILL';
  TwoStep: 'TWO_STEP';
  Signature: 'SIGNATURE';
}
export type AuthModeType = AuthModes[keyof AuthModes];
interface OAuth1Token {
  oAuthToken: string;
  oAuthTokenSecret: string;
}
interface AppCredentials {
  type: AuthModes['App'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
}
interface AppStoreCredentials {
  type?: AuthModes['AppStore'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
  private_key: string;
}
interface BasicApiCredentials {
  type: AuthModes['Basic'];
  username: string;
  password: string;
}
interface ApiKeyCredentials {
  type: AuthModes['ApiKey'];
  apiKey: string;
}
interface CredentialsCommon<T = Record<string, any>> {
  type: AuthModeType;
  raw: T;
}
interface OAuth2Credentials extends CredentialsCommon {
  type: AuthModes['OAuth2'];
  access_token: string;
  refresh_token?: string;
  expires_at?: Date | undefined;
}
interface OAuth2ClientCredentials extends CredentialsCommon {
  type: AuthModes['OAuth2CC'];
  token: string;
  expires_at?: Date | undefined;
  client_id: string;
  client_secret: string;
}
interface OAuth1Credentials extends CredentialsCommon {
  type: AuthModes['OAuth1'];
  oauth_token: string;
  oauth_token_secret: string;
}
interface TbaCredentials {
  type: AuthModes['TBA'];
  token_id: string;
  token_secret: string;
  config_override: {
    client_id?: string;
    client_secret?: string;
  };
}
interface TableauCredentials extends CredentialsCommon {
  type: AuthModes['Tableau'];
  pat_name: string;
  pat_secret: string;
  content_url?: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface JwtCredentials {
  type: AuthModes['Jwt'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface BillCredentials extends CredentialsCommon {
  type: AuthModes['Bill'];
  username: string;
  password: string;
  organization_id: string;
  dev_key: string;
  session_id?: string;
  user_id?: string;
  expires_at?: Date | undefined;
}
interface TwoStepCredentials extends CredentialsCommon {
  type: AuthModes['TwoStep'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface SignatureCredentials {
  type: AuthModes['Signature'];
  username: string;
  password: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface CustomCredentials extends CredentialsCommon {
  type: AuthModes['Custom'];
}
type UnauthCredentials = Record<string, never>;
type AuthCredentials =
  | OAuth2Credentials
  | OAuth2ClientCredentials
  | OAuth1Credentials
  | BasicApiCredentials
  | ApiKeyCredentials
  | AppCredentials
  | AppStoreCredentials
  | UnauthCredentials
  | TbaCredentials
  | TableauCredentials
  | JwtCredentials
  | BillCredentials
  | TwoStepCredentials
  | SignatureCredentials
  | CustomCredentials;
type Metadata = Record<string, unknown>;
interface MetadataChangeResponse {
  metadata: Metadata;
  provider_config_key: string;
  connection_id: string | string[];
}
interface Connection {
  id: number;
  provider_config_key: string;
  connection_id: string;
  connection_config: Record<string, string>;
  created_at: string;
  updated_at: string;
  last_fetched_at: string;
  metadata: Record<string, unknown> | null;
  provider: string;
  errors: {
    type: string;
    log_id: string;
  }[];
  end_user: ApiEndUser | null;
  credentials: AuthCredentials;
}
export declare class ActionError<T = Record<string, unknown>> extends Error {
  type: string;
  payload?: Record<string, unknown>;
  constructor(payload?: T);
}
export interface NangoProps {
  scriptType: 'sync' | 'action' | 'webhook' | 'on-event';
  host?: string;
  secretKey: string;
  team?: Pick<DBTeam, 'id' | 'name'>;
  connectionId: string;
  environmentId: number;
  environmentName?: string;
  activityLogId?: string | undefined;
  providerConfigKey: string;
  provider: string;
  lastSyncDate?: Date;
  syncId?: string | undefined;
  nangoConnectionId?: number;
  syncJobId?: number | undefined;
  dryRun?: boolean;
  track_deletes?: boolean;
  attributes?: object | undefined;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]> | undefined;
  rawDeleteOutput?: Map<string, unknown[]> | undefined;
  stubbedMetadata?: Metadata | undefined;
  abortSignal?: AbortSignal;
  syncConfig: DBSyncConfig;
  runnerFlags: RunnerFlags;
  debug: boolean;
  startedAt: Date;
  endUser: {
    id: number;
    endUserId: string | null;
    orgId: string | null;
  } | null;
  axios?: {
    request?: AxiosInterceptorManager<AxiosRequestConfig>;
    response?: {
      onFulfilled: (value: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;
      onRejected: (value: unknown) => AxiosError | Promise<AxiosError>;
    };
  };
}
export interface EnvironmentVariable {
  name: string;
  value: string;
}
export declare const defaultPersistApi: AxiosInstance;
export declare class NangoAction {
  protected nango: Nango;
  private attributes;
  protected persistApi: AxiosInstance;
  activityLogId?: string | undefined;
  syncId?: string;
  nangoConnectionId?: number;
  environmentId: number;
  environmentName?: string;
  syncJobId?: number;
  dryRun?: boolean;
  abortSignal?: AbortSignal;
  syncConfig?: DBSyncConfig;
  runnerFlags: RunnerFlags;
  connectionId: string;
  providerConfigKey: string;
  provider?: string;
  ActionError: typeof ActionError;
  private memoizedConnections;
  private memoizedIntegration;
  constructor(
    config: NangoProps,
    {
      persistApi,
    }?: {
      persistApi: AxiosInstance;
    }
  );
  protected stringify(): string;
  private proxyConfig;
  protected throwIfAborted(): void;
  proxy<T = any>(config: ProxyConfiguration): Promise<AxiosResponse<T>>;
  get<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  post<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  put<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  patch<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  delete<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  getToken(): Promise<
    | string
    | OAuth1Token
    | OAuth2ClientCredentials
    | BasicApiCredentials
    | ApiKeyCredentials
    | AppCredentials
    | AppStoreCredentials
    | UnauthCredentials
    | CustomCredentials
    | TbaCredentials
    | TableauCredentials
    | JwtCredentials
    | BillCredentials
    | TwoStepCredentials
    | SignatureCredentials
  >;
  /**
   * Get current integration
   */
  getIntegration(
    queries?: GetPublicIntegration['Querystring']
  ): Promise<GetPublicIntegration['Success']['data']>;
  getConnection(
    providerConfigKeyOverride?: string,
    connectionIdOverride?: string
  ): Promise<Connection>;
  setMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  updateMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  /**
   * @deprecated please use setMetadata instead.
   */
  setFieldMapping(fieldMapping: Record<string, string>): Promise<AxiosResponse<object>>;
  getMetadata<T = Metadata>(): Promise<T>;
  getWebhookURL(): Promise<string | null | undefined>;
  /**
   * @deprecated please use getMetadata instead.
   */
  getFieldMapping(): Promise<Metadata>;
  /**
   * Log
   * @desc Log a message to the activity log which shows up in the Nango Dashboard
   * note that the last argument can be an object with a level property to specify the log level
   * @example
   * ```ts
   * await nango.log('This is a log message', { level: 'error' })
   * ```
   */
  log(
    message: any,
    options?:
      | {
          level?: LogLevel;
        }
      | {
          [key: string]: any;
          level?: never;
        }
  ): Promise<void>;
  log(
    message: string,
    ...args: [
      any,
      {
        level?: LogLevel;
      },
    ]
  ): Promise<void>;
  getEnvironmentVariables(): Promise<EnvironmentVariable[] | null>;
  getFlowAttributes<A = object>(): A | null;
  paginate<T = any>(config: ProxyConfiguration): AsyncGenerator<T[], undefined, void>;
  triggerAction<In = unknown, Out = object>(
    providerConfigKey: string,
    connectionId: string,
    actionName: string,
    input?: In
  ): Promise<Out>;
  zodValidateInput<T = any, Z = any>({
    zodSchema,
    input,
  }: {
    zodSchema: ZodSchema<Z>;
    input: T;
  }): Promise<SafeParseSuccess<Z>>;
  triggerSync(
    providerConfigKey: string,
    connectionId: string,
    sync: string | { name: string; variant: string },
    syncMode?: PostPublicTrigger['Body']['sync_mode'] | boolean
  ): Promise<void | string>;
  startSync(
    providerConfigKey: string,
    syncs: (string | { name: string; variant: string })[],
    connectionId?: string
  ): Promise<void>;
  /**
   * Uncontrolled fetch is a regular fetch without retry or credentials injection.
   * Only use that method when you want to access resources that are unrelated to the current connection/provider.
   */
  uncontrolledFetch(options: {
    url: URL;
    method?: HTTP_METHOD;
    headers?: Record<string, string> | undefined;
    body?: string | null;
  }): Promise<Response>;
  tryAcquireLock(props: { key: string; ttlMs: number }): Promise<boolean>;
  releaseLock(props: { key: string }): Promise<boolean>;
  private sendLogToPersist;
  private logAPICall;
}
export declare class NangoSync extends NangoAction {
  variant: string;
  lastSyncDate?: Date;
  track_deletes: boolean;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]>;
  rawDeleteOutput?: Map<string, unknown[]>;
  stubbedMetadata?: Metadata | undefined;
  private batchSize;
  constructor(config: NangoProps);
  /**
   * @deprecated please use batchSave
   */
  batchSend<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchSave<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchDelete<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchUpdate<T extends object>(results: T[], model: string): Promise<boolean | null>;
  getMetadata<T = Metadata>(): Promise<T>;
  setMergingStrategy(
    merging: { strategy: 'ignore_if_modified_after' | 'override' },
    model: string
  ): Promise<void>;
  getRecordsByIds<K = string | number, T = any>(ids: K[], model: string): Promise<Map<K, T>>;
}
/**
 * @internal
 *
 * This function will enable tracing on the SDK
 * It has been split from the actual code to avoid making the code too dirty and to easily enable/disable tracing if there is an issue with it
 */
export declare function instrumentSDK(rawNango: NangoAction | NangoSync): NangoAction | NangoSync;
export {};

export const NangoFlows = [
  {
    providerConfigKey: 'github',
    syncs: [],
    actions: [
      {
        name: 'list-repositories',
        type: 'action',
        description: 'Lists repositories for the authenticated user.',
        version: '',
        scopes: ['repo'],
        input: null,
        output: ['GithubRepositoryList'],
        usedModels: ['GithubRepositoryList', 'GithubRepository', 'GithubIssueCreator'],
        endpoint: {
          method: 'GET',
          path: '/list-repositories',
        },
      },
      {
        name: 'get-repository',
        type: 'action',
        description: 'Gets a specific repository by owner and name.',
        version: '',
        scopes: ['repo'],
        input: 'GithubRepositoryInput',
        output: ['GithubRepository'],
        usedModels: ['GithubRepository', 'GithubIssueCreator', 'GithubRepositoryInput'],
        endpoint: {
          method: 'GET',
          path: '/get-repository',
        },
      },
      {
        name: 'create-repository',
        type: 'action',
        description:
          'Creates a new repository for the authenticated user. After successful creation, describe to the user how they can push to it inluding ssh url.',
        version: '',
        scopes: ['repo'],
        input: 'GithubCreateRepositoryInput',
        output: ['GithubRepository'],
        usedModels: ['GithubRepository', 'GithubIssueCreator', 'GithubCreateRepositoryInput'],
        endpoint: {
          method: 'POST',
          path: '/create-repository',
        },
      },
      {
        name: 'update-repository',
        type: 'action',
        description: 'Updates an existing repository.',
        version: '',
        scopes: ['repo'],
        input: 'GithubUpdateRepositoryInput',
        output: ['GithubRepository'],
        usedModels: ['GithubRepository', 'GithubIssueCreator', 'GithubUpdateRepositoryInput'],
        endpoint: {
          method: 'PATCH',
          path: '/update-repository',
        },
      },
      {
        name: 'delete-repository',
        type: 'action',
        description: 'Deletes a repository.',
        version: '',
        scopes: ['delete_repo'],
        input: 'GithubRepositoryInput',
        output: ['GithubDeleteRepositoryOutput'],
        usedModels: ['GithubDeleteRepositoryOutput', 'GithubRepositoryInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-repository',
        },
      },
      {
        name: 'create-organization-repository',
        type: 'action',
        description: 'Creates a new repository within a specified organization.',
        version: '',
        scopes: ['repo', 'admin:org'],
        input: 'GithubCreateOrganizationRepositoryInput',
        output: ['GithubRepository'],
        usedModels: [
          'GithubRepository',
          'GithubIssueCreator',
          'GithubCreateOrganizationRepositoryInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-organization-repository',
        },
      },
      {
        name: 'list-issues',
        type: 'action',
        description: 'Lists issues for a repository.',
        version: '',
        scopes: ['repo'],
        input: 'GithubIssuesInput',
        output: ['GithubIssueList'],
        usedModels: [
          'GithubIssueList',
          'GithubIssue',
          'GithubIssueCreatorLite',
          'GithubIssueCreator',
          'GithubIssueLabel',
          'GithubSubIssuesSummary',
          'GithubReactions',
          'GithubIssuesInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-issues',
        },
      },
      {
        name: 'get-issue',
        type: 'action',
        description: 'Gets a specific issue by number.',
        version: '',
        scopes: ['repo'],
        input: 'GithubIssueInput',
        output: ['GithubIssue'],
        usedModels: [
          'GithubIssue',
          'GithubIssueCreatorLite',
          'GithubIssueCreator',
          'GithubIssueLabel',
          'GithubSubIssuesSummary',
          'GithubReactions',
          'GithubIssueInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-issue',
        },
      },
      {
        name: 'create-issue',
        type: 'action',
        description: 'Creates a new issue in a repository.',
        version: '',
        scopes: ['repo'],
        input: 'GithubCreateIssueInput',
        output: ['GithubIssue'],
        usedModels: [
          'GithubIssue',
          'GithubIssueCreatorLite',
          'GithubIssueCreator',
          'GithubIssueLabel',
          'GithubSubIssuesSummary',
          'GithubReactions',
          'GithubCreateIssueInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-issue',
        },
      },
      {
        name: 'update-issue',
        type: 'action',
        description: 'Updates an existing issue.',
        version: '',
        scopes: ['repo'],
        input: 'GithubUpdateIssueInput',
        output: ['GithubIssue'],
        usedModels: [
          'GithubIssue',
          'GithubIssueCreatorLite',
          'GithubIssueCreator',
          'GithubIssueLabel',
          'GithubSubIssuesSummary',
          'GithubReactions',
          'GithubUpdateIssueInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-issue',
        },
      },
      {
        name: 'get-pull-request',
        type: 'action',
        description: 'Get details of a specific pull request in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubPullRequestInput',
        output: ['GithubPullRequest'],
        usedModels: [
          'GithubPullRequest',
          'GithubIssueCreator',
          'GithubIssueAssignee',
          'GithubTeamRef',
          'GithubIssueLabel',
          'GithubPullRequestInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-pull-request',
        },
      },
      {
        name: 'update-pull-request',
        type: 'action',
        description: 'Update an existing pull request in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubUpdatePullRequestInput',
        output: ['GithubPullRequest'],
        usedModels: [
          'GithubPullRequest',
          'GithubIssueCreator',
          'GithubIssueAssignee',
          'GithubTeamRef',
          'GithubIssueLabel',
          'GithubUpdatePullRequestInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-pull-request',
        },
      },
      {
        name: 'list-pull-requests',
        type: 'action',
        description: 'List pull requests in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubListPullRequestsInput',
        output: ['GithubPullRequestList'],
        usedModels: [
          'GithubPullRequestList',
          'GithubPullRequest',
          'GithubIssueCreator',
          'GithubIssueAssignee',
          'GithubTeamRef',
          'GithubIssueLabel',
          'GithubListPullRequestsInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-pull-requests',
        },
      },
      {
        name: 'merge-pull-request',
        type: 'action',
        description: 'Merge a pull request in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubMergePullRequestInput',
        output: ['GithubMergeResult'],
        usedModels: ['GithubMergeResult', 'GithubMergePullRequestInput'],
        endpoint: {
          method: 'PUT',
          path: '/merge-pull-request',
        },
      },
      {
        name: 'get-pull-request-files',
        type: 'action',
        description: 'Get the files changed in a specific pull request.',
        version: '',
        scopes: ['pull'],
        input: 'GithubPullRequestInput',
        output: ['GithubPullRequestFileList'],
        usedModels: [
          'GithubPullRequestFileList',
          'GithubPullRequestFile',
          'GithubPullRequestInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-pull-request-files',
        },
      },
      {
        name: 'get-pull-request-status',
        type: 'action',
        description: 'Get the combined status of all status checks for a pull request.',
        version: '',
        scopes: ['status'],
        input: 'GithubPullRequestInput',
        output: ['GithubCombinedStatus'],
        usedModels: [
          'GithubCombinedStatus',
          'GithubStatus',
          'GithubRepositoryForGithubCombinedStatus',
          'GithubIssueCreator',
          'GithubPullRequestInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-pull-request-status',
        },
      },
      {
        name: 'update-pull-request-branch',
        type: 'action',
        description:
          'Update the branch of a pull request with the latest changes from the base branch.',
        version: '',
        scopes: ['pull'],
        input: 'GithubUpdatePullRequestBranchInput',
        output: ['GithubBranchUpdateResult'],
        usedModels: ['GithubBranchUpdateResult', 'GithubUpdatePullRequestBranchInput'],
        endpoint: {
          method: 'PUT',
          path: '/update-pull-request-branch',
        },
      },
      {
        name: 'get-pull-request-comments',
        type: 'action',
        description: 'Get the review comments on a pull request.',
        version: '',
        scopes: ['pull'],
        input: 'GithubPullRequestInput',
        output: ['GithubPullRequestCommentList'],
        usedModels: [
          'GithubPullRequestCommentList',
          'GithubPullRequestComment',
          'GithubIssueCreator',
          'GithubReactions',
          'GithubPullRequestInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-pull-request-comments',
        },
      },
      {
        name: 'add-pull-request-review-comment',
        type: 'action',
        description: 'Add a review comment to a pull request.',
        version: '',
        scopes: ['pull'],
        input: 'GithubAddPullRequestReviewCommentInput',
        output: ['GithubPullRequestComment'],
        usedModels: [
          'GithubPullRequestComment',
          'GithubIssueCreator',
          'GithubReactions',
          'GithubAddPullRequestReviewCommentInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/add-pull-request-review-comment',
        },
      },
      {
        name: 'create-pull-request-review',
        type: 'action',
        description: 'Submit a review on a pull request.',
        version: '',
        scopes: ['pull'],
        input: 'GithubCreatePullRequestReviewInput',
        output: ['GithubPullRequestReview'],
        usedModels: [
          'GithubPullRequestReview',
          'GithubIssueCreator',
          'GithubCreatePullRequestReviewInput',
          'GithubDraftReviewComment',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-pull-request-review',
        },
      },
      {
        name: 'create-pull-request',
        type: 'action',
        description: 'Create a new pull request in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubCreatePullRequestInput',
        output: ['GithubPullRequest'],
        usedModels: [
          'GithubPullRequest',
          'GithubIssueCreator',
          'GithubIssueAssignee',
          'GithubTeamRef',
          'GithubIssueLabel',
          'GithubCreatePullRequestInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-pull-request',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
] as const;
