import { createTool } from '@mastra/core/tools';
import { z } from 'zod';

const askUserToSelectResource = createTool({
  id: 'askUserToSelectResource',
  description: 'Pass a set of options to the user for the user to select from',
  inputSchema: z.object({
    title: z.string().describe('A title: Please select a(n) <>'),
    options: z
      .array(
        z.object({
          value: z.string().describe('The underlying identifier for the resource'),
          label: z
            .string()
            .describe('The plain text user facing description for identifying the resource'),
        })
      )
      .min(2),
  }),
  outputSchema: z.string().describe('The selected resource id'),
});

export { askUserToSelectResource };
