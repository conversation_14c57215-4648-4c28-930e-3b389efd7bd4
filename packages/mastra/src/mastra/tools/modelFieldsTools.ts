import { createTool } from '@mastra/core/tools';

import { z } from 'zod';
import { getActionInputModelFieldsForLLM, getSyncOutputModelFieldsForLLM } from '../nangoConstants';

const getSyncOutputModelFieldsTool = createTool({
  id: 'get-sync-output-model-fields',
  description: 'Get fields for a sync output model',
  inputSchema: z.object({
    model: z.string().describe('Model name'),
  }),
  outputSchema: z.record(
    z.array(
      z.object({
        name: z.string(),
        type: z.string(),
        optional: z.boolean(),
      })
    )
  ),
  execute: async ({ context }) => {
    return getSyncOutputModelFieldsForLLM(context.model);
  },
});

const getActionInputModelFieldsTool = createTool({
  id: 'get-action-input-model-fields',
  description: 'Get fields for an action input model',
  inputSchema: z.object({
    model: z.string().describe('Model name'),
  }),
  outputSchema: z.record(
    z.array(
      z.object({
        name: z.string(),
        type: z.string(),
        optional: z.boolean(),
      })
    )
  ),
  execute: async ({ context }) => {
    return getActionInputModelFieldsForLLM(context.model);
  },
});

export { getActionInputModelFieldsTool, getSyncOutputModelFieldsTool };
