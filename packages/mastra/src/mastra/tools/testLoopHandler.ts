import { createTool } from '@mastra/core/tools';
import { z } from 'zod';

/**
 * This tool exists solely to demonstrate the loop capability of the
 * performToolCall function in submit-prompt/index.ts
 *
 * When called, it will trigger a tool call with the name "test_loop_handler"
 * which our backend handler recognizes and processes without breaking the loop.
 */
const testLoopHandler = createTool({
  id: 'test_loop_handler',
  description: 'A test tool to demonstrate the backend tool handling loop capability',
  inputSchema: z.object({
    message: z.string().optional().describe('Optional message to include with the test'),
  }),
  outputSchema: z.string(),
});

const clientHandler = createTool({
  id: 'test_client_handler',
  description: 'A test tool to demonstrate the client tool handling capability',
  inputSchema: z.object({
    message: z.string().optional().describe('Optional message to include with the test'),
  }),
  outputSchema: z.string(),
});

export { testLoopHandler, clientHandler };
