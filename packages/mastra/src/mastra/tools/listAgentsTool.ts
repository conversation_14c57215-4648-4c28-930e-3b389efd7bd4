import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role credentials
const initSupabaseClient = () => {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase credentials in environment variables');
  }

  return createClient(supabaseUrl, supabaseServiceKey);
};

interface Agent {
  active: boolean;
}

interface Conversation {
  title: string;
  agent: Agent; // Assert single object, not Agent[]
}

const listAgentsTool = createTool({
  id: 'get-user-agents',
  description: 'Get all agents for a specific user',
  inputSchema: z.object({
    userId: z.string().describe('User ID to fetch agents for'),
  }),
  outputSchema: z.union([
    z.array(
      z.object({
        title: z.string(),
        active: z.boolean(),
      })
    ),
    z.string().describe('Error message'),
  ]),
  execute: async ({ context }) => {
    const { userId } = context;

    if (!userId) {
      return 'userId is required to fetch agents';
    }

    console.log(`Fetching agents for user: ${userId}`);

    try {
      const supabase = initSupabaseClient();

      const { data: conversations, error } = await supabase
        .from('conversations')
        .select(
          `
          title,
          agent:agents (
            active
          )
        `
        )
        .order('updatedAt', { ascending: false })
        .eq('userId', userId);

      if (error) {
        console.error('Error fetching agents:', error);
        throw new Error(`Failed to fetch agents: ${error.message}`);
      }

      // Filter out conversations without agents and format the response
      const agents = (conversations as unknown as Conversation[])
        .filter(conv => conv.agent)
        .sort((a, b) => (b.agent.active ? 1 : 0) - (a.agent.active ? 1 : 0))
        .map(conv => ({
          title: conv.title,
          active: conv.agent.active,
        }));

      return agents;
    } catch (_) {
      return "Agents couldn't be resolved due to an error, <NAME_EMAIL>";
    }
  },
});

export { listAgentsTool };
