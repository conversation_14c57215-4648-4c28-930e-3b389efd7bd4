import { openai } from '@ai-sdk/openai';
import { Agent } from '@mastra/core/agent';
import { Memory } from '@mastra/memory';
import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { LibSQLVector } from '@mastra/libsql';

const llm = openai('gpt-4o');

const memory = new Memory({
  options: {
    lastMessages: 30,
    semanticRecall: false,
    threads: {
      generateTitle: true,
    },
    // semanticRecall: {
    //   topK: 4, // Include 10 most relevant past messages
    //   messageRange: 2, // Messages before and after each result
    // },
  },
  vector: new LibSQLVector({
    connectionUrl: 'file:../memory.db',
  }),
});

let i = 0;

const guessTool = createTool({
  id: 'guess',
  description: 'Guess a number',
  inputSchema: z.object({
    guess: z.number().describe('Guess a number'),
  }),
  outputSchema: z.string(),
  execute: async ({ context }) => {
    if (i > 2) {
      return 'you guessed right!!!';
    }
    i++;
    return 'you guessed wrong';
  },
});

const playgroundAgent = new Agent({
  name: 'playgroundAgent',
  model: llm,
  instructions: `
    We're going to play a guessing game. I want you to call the "guessTool" until you guess correctly!

    Between each tool call, please state what you guessed, and what the response was.

  `,
  // memory,
  tools: {
    guessTool,
  },
});

export { playgroundAgent };
