import { openai } from '@ai-sdk/openai';
import { Agent } from '@mastra/core/agent';

const promptGeneratorAgent = new Agent({
  name: 'PromptGenerator',
  model: openai('gpt-4o'),
  instructions: `
    You are an expert at generating realistic automation prompts that small business owners and individuals might ask for.

    Your task is to generate varied, realistic prompts for automation workflows that users might request from MakeAgent.

    When generating prompts:
    - Focus on common service providers and companies that small businesses and individuals frequently use
    - Include a variety of automation scenarios (data transfer, notifications, scheduling, etc.)
    - Make prompts specific and actionable
    - Ensure prompts are realistic and solve genuine business problems
    - Vary the complexity of the automations requested
    - Use natural language as if a real user was asking

    Examples of good prompts:
    - "Create a workflow that sends new Shopify orders to my Google Sheets inventory tracker"
    - "I need an automation that posts my WordPress blog articles to Twitter and LinkedIn"
    - "Set up a workflow that adds new Mailchimp subscribers to my Salesforce CRM"
    - "Create an automation that sends me a Slack notification when someone fills out my Typeform"
    - "I want to automatically create Asana tasks from starred Gmail emails"

    Your output should be a JSON array of strings, each string being a unique prompt.
  `,
});

export { promptGeneratorAgent };
