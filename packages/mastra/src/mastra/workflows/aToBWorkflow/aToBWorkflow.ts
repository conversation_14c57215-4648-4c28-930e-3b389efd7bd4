import { openai } from '@ai-sdk/openai';
import { Agent } from '@mastra/core/agent';
import { Step, Workflow } from '@mastra/core/workflows';
import { z } from 'zod';
import { ACTION_MODEL_SCHEMAS } from './aToBWorkflowConstants';

const llm = openai('gpt-4o');

const WORKFLOW_INPUT_SCHEMA = z.object({
  record: z.record(z.any()),
  syncDetails: z.object({
    providerKey: z.string(),
    syncKey: z.string(),
    model: z.string(),
  }),
  actionDetails: z.object({
    connectionId: z.string(),
    providerKey: z.string(),
    actionKey: z.string(),
    model: z.string(),
  }),
  userParameters: z
    .object({
      _llmSystemPrompt: z.string(),
    })
    .and(z.record(z.any())),
});

// Step ids
const STEP_ONE_ID = 'prepare-agent';
const STEP_TWO_ID = 'process-record';
const STEP_THREE_ID = 'send-to-target';

/**
 * Step 1: Prepare an agent with the appropriate system prompt
 */
const prepareAgent = new Step({
  id: STEP_ONE_ID,
  description: 'Prepares an agent with the appropriate system prompt',
  inputSchema: WORKFLOW_INPUT_SCHEMA,
  execute: async ({ context }) => {
    const triggerData = context?.getStepResult<z.infer<typeof WORKFLOW_INPUT_SCHEMA>>('trigger');

    if (!triggerData) {
      throw new Error('Trigger data not found');
    }

    const { record, syncDetails, actionDetails, userParameters } = triggerData;

    const actionModelSchema = ACTION_MODEL_SCHEMAS[actionDetails.model];
    if (!actionModelSchema) {
      throw new Error(`Schema not found for action model: ${actionDetails.model}`);
    }

    const systemPromptPrefix = `You are participating in the execution of dynamic workflows on behalf of customers. You will be receiving a record from ${syncDetails.providerKey} and must, according to the instructions of the customers guidelines listed below, output the data to feed into the action based on the provided record. The action schema is ${JSON.stringify(
      actionModelSchema.shape,
      null,
      2
    )}.`;

    const fullSystemPrompt = `${systemPromptPrefix}\n\n${userParameters._llmSystemPrompt}`;

    const agent = new Agent({
      name: 'DynamicWorkflowAgent',
      model: llm,
      instructions: fullSystemPrompt,
    });

    return {
      agent,
      record,
      actionModelSchema,
      actionDetails,
      userParameters,
    };
  },
});

/**
 * Step 2: Process the record with the prepared agent
 */
const STEP_TWO_INPUT_SCHEMA = z.object({
  agent: z.instanceof(Agent),
  record: z.record(z.any()),
  actionModelSchema: z.any(),
  actionDetails: z.object({
    connectionId: z.string(),
    providerKey: z.string(),
    actionKey: z.string(),
    model: z.string(),
  }),
  userParameters: z.record(z.any()),
});

const processRecord = new Step({
  id: STEP_TWO_ID,
  description: 'Processes the record with the prepared agent',
  inputSchema: STEP_TWO_INPUT_SCHEMA,
  execute: async ({ context }) => {
    const data = context?.getStepResult(STEP_ONE_ID);

    if (!data) {
      throw new Error('Agent preparation data not found');
    }

    const {
      agent,
      record,
      actionModelSchema,
      userParameters: { _llmSystemPrompt, ...userParameters },
    } = data as z.infer<typeof STEP_TWO_INPUT_SCHEMA>;

    const prompt = `Process the following record and generate a structured output according to the required schema:\n\n${JSON.stringify(
      record,
      null,
      2
    )}

      The user also provided the following parameters:\n\n${JSON.stringify(
        userParameters,
        null,
        2
      )}`;

    const response = await agent.generate([{ role: 'user', content: prompt }], {
      output: actionModelSchema,
    });

    const parsedOutput = response.object;

    try {
      actionModelSchema.parse(parsedOutput);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(
        `Failed to parse agent output as valid JSON matching the schema: ${errorMessage}`
      );
    }

    return {
      ...data,
      processedOutput: parsedOutput,
    };
  },
});

/**
 * Step 3: Send the processed output to the target system
 */
const STEP_THREE_INPUT_SCHEMA = z.object({
  agent: z.instanceof(Agent),
  record: z.record(z.any()),
  actionModelSchema: z.any(),
  actionDetails: z.object({
    connectionId: z.string(),
    providerKey: z.string(),
    actionKey: z.string(),
    model: z.string(),
  }),
  userParameters: z.record(z.any()),
  processedOutput: z.record(z.any()),
});

const sendToTarget = new Step({
  id: STEP_THREE_ID,
  description: 'Sends the processed output to the target system',
  inputSchema: STEP_THREE_INPUT_SCHEMA,
  execute: async ({ context }) => {
    const data = context?.getStepResult(STEP_TWO_ID);

    if (!data) {
      throw new Error('Processed record data not found');
    }

    const { actionDetails, processedOutput } = data as z.infer<typeof STEP_THREE_INPUT_SCHEMA>;

    const payload = {
      connectionId: actionDetails.connectionId,
      providerKey: actionDetails.providerKey,
      actionKey: actionDetails.actionKey,
      payload: processedOutput,
    };

    const endpoint = process.env.ACTION_WEBHOOK_URL;

    if (!endpoint) {
      throw new Error('Missing ACTION_WEBHOOK_URL');
    }

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      return {
        success: true,
        endpoint,
        payload,
        response: result,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to send data to target system: ${errorMessage}`);
    }
  },
});

/**
 * Register the workflow
 */
const aToBWorkflow = new Workflow({
  name: 'a-to-b-workflow',
  triggerSchema: WORKFLOW_INPUT_SCHEMA,
})
  .step(prepareAgent)
  .then(processRecord)
  .then(sendToTarget);

aToBWorkflow.commit();

export { aToBWorkflow };
