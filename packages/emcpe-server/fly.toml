# fly.toml app configuration file generated for easymcpeasy-mcp-server on 2025-04-03T02:49:41Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'easymcpeasy-mcp-server'
primary_region = 'syd'

[build]

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'suspend'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
