import { supabase } from './supabase.js';
import { activeConnections, ToolCallRecord } from './dashboard.js';
import { nango } from './nango.js';

const handleConnectionClose = (sessionId: string) => {
  const connectionData = activeConnections[sessionId];

  if (connectionData) {
    console.log(`Client disconnected: ${sessionId}, User: ${connectionData.userId}`);
    delete activeConnections[sessionId];

    const disconnectionTime = new Date();
    supabase
      .from('emcpe_connection_log')
      .update({ disconnectionTime: disconnectionTime.toISOString() })
      .eq('sessionId', sessionId)
      .is('disconnectionTime', null)
      .then(({ error: updateError }) => {
        if (updateError) {
          console.error(`Failed to log disconnection for session ${sessionId}:`, updateError);
        } else {
          console.log(`Disconnection logged for session ${sessionId}`);
        }
      });
  } else {
    console.log(`Attempted to close already removed/unknown session: ${sessionId}`);

    const disconnectionTime = new Date();
    supabase
      .from('emcpe_connection_log')
      .update({ disconnectionTime: disconnectionTime.toISOString() })
      .eq('sessionId', sessionId)
      .is('disconnectionTime', null)
      .then(({ error: updateError }) => {
        if (!updateError) {
          console.log(`Disconnection logged for potentially orphaned session: ${sessionId}`);
        }
      });
  }
};

const handleToolCall = async (
  providerKey: string,
  nangoConnectionId: string,
  action: string,
  toolName: string,
  inputs: any,
  sessionId: string
) => {
  let statusCode = 9000;
  let toolResultPayload;

  try {
    const response = await rateLimit({
      providerKey,
      nangoConnectionId,
      action,
      toolName,
      inputs,
      sessionId,
    });

    // If response is not null, rate limit was exceeded. Return the error payload.
    if (response) {
      return response;
    }

    // Rate limit not exceeded, proceed with the action
    toolResultPayload = await nango.triggerAction(providerKey, nangoConnectionId, action, inputs);
    statusCode = 200;
  } catch (error) {
    console.error(`Error during nango.triggerAction for ${toolName}:`, error);
    toolResultPayload = {
      error: (error as Error).message || 'Unknown error during tool execution',
    };

    if ((error as { status: number })?.status) {
      statusCode = (error as { status: number }).status;
    } else {
      statusCode = 500;
    }
  }

  const timestamp = new Date();
  const toolCallRecord: ToolCallRecord = { toolName, timestamp, statusCode };

  if (sessionId && activeConnections[sessionId]) {
    activeConnections[sessionId].toolCalls.push(toolCallRecord);
    console.log(
      `Tool call logged for active session ${sessionId}: ${toolName} - Status: ${statusCode}`
    );

    supabase
      .from('emcpe_tool_call')
      .insert({
        sessionId: sessionId,
        toolName: toolCallRecord.toolName,
        timestamp: toolCallRecord.timestamp.toISOString(),
        statusCode: toolCallRecord.statusCode,
      })
      .then(({ error: insertError }) => {
        if (insertError) {
          console.error(`Failed to insert tool call log for session ${sessionId}:`, insertError);
        }
      });
  } else {
    console.warn(
      `Could not log tool call to memory: Session ID ${sessionId} not found in active connections.`
    );
  }

  return {
    content: [
      {
        type: 'text' as const,
        text: JSON.stringify(toolResultPayload),
      },
    ],
    isError: statusCode !== 200,
  };
};

// Define rate limits per toolName: { count: number, windowMs: number }
const TOOL_RATE_LIMITS: Record<string, { count: number; windowMs: number }> = {
  'x-social_send-post': {
    count: 5,
    windowMs: 30 * 24 * 60 * 60 * 1000, // 5 calls per 30 days
  },
  // Add other tool names and their specific limits here
};

async function rateLimit({
  toolName,
  sessionId,
}: {
  providerKey: string; // Keep for context if needed later
  nangoConnectionId: string; // Keep for potential future use
  action: string; // Keep for potential future use
  toolName: string;
  inputs: any; // Keep for potential future use
  sessionId: string;
}): Promise<{ content: { type: 'text'; text: string }[]; isError: boolean } | null> {
  const limitConfig = TOOL_RATE_LIMITS[toolName];

  // If no limit is defined for this specific tool, allow the call
  if (!limitConfig) {
    return null;
  }

  const { count: limitCount, windowMs } = limitConfig;

  const connectionData = activeConnections[sessionId];
  if (!connectionData || !connectionData.userId) {
    console.warn(
      `Rate limiting check failed for tool ${toolName}: Could not find user ID for session ${sessionId}. Allowing call.`
    );
    return null; // Fail open if user context is missing
  }
  const userId = connectionData.userId;

  const windowStartTime = new Date(Date.now() - windowMs).toISOString();

  try {
    // Query to count relevant tool calls for the user in the defined window
    const { count, error: countError } = await supabase
      .from('emcpe_tool_call')
      .select(
        `
        id,
        connection:emcpe_connection_log!inner (
          userId
        )
      `,
        { count: 'exact', head: true }
      )
      .eq('toolName', toolName) // Filter by the specific tool being called
      .gte('timestamp', windowStartTime) // Filter by the time window for this tool
      .eq('connection.userId', userId); // Filter by userId via the join

    if (countError) {
      console.error(`Rate limiting query failed for user ${userId}, tool ${toolName}:`, countError);
      return null; // Fail open on database error
    }

    const windowDays = Math.round(windowMs / (1000 * 60 * 60 * 24)); // For logging
    console.log(
      `Rate limit check for user ${userId}, tool ${toolName}: ${
        count ?? 0
      } calls in the last ${windowDays} days (limit: ${limitCount})`
    );

    if (count !== null && count >= limitCount) {
      console.warn(
        `Rate limit exceeded for user ${userId}, tool ${toolName}. Count: ${count}, Limit: ${limitCount} per ${windowDays} days.`
      );
      const errorMessage = `Rate limit exceeded for ${toolName}. Limit is ${limitCount} calls per ${windowDays} days.`;
      return {
        content: [
          {
            type: 'text' as const,
            text: JSON.stringify({ error: errorMessage, status: 429 }),
          },
        ],
        isError: true,
      };
    }

    // Limit not exceeded
    return null;
  } catch (error) {
    console.error(
      `Unexpected error during rate limiting check for user ${userId}, tool ${toolName}:`,
      error
    );
    return null; // Fail open on unexpected errors
  }
}

export { handleConnectionClose, handleToolCall };
