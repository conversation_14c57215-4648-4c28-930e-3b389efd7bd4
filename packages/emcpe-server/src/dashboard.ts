import { Request, Response, NextFunction, Express } from 'express';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { supabase } from './supabase.js';

export interface ToolCallRecord {
  toolName: string;
  timestamp: Date;
  statusCode: number;
}

export interface ActiveConnection {
  transport: SSEServerTransport;
  userId: string;
  connectionTime: Date;
  toolCalls: ToolCallRecord[];
}

const activeConnections: { [sessionId: string]: ActiveConnection } = {};

const requireAdminToken = (req: Request, res: Response, next: NextFunction) => {
  const adminToken = process.env.ADMIN_TOKEN;
  if (!adminToken) {
    res.status(404);
    return;
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.status(401).json({ error: 'Unauthorized: Missing or invalid Authorization header' });
    return;
  }

  const token = authHeader.split(' ')[1];
  if (token !== adminToken) {
    res.status(403).json({ error: 'Forbidden: Invalid token' });
    return;
  }

  next();
};

const registerDashboardRoutes = (app: Express) => {
  app.get('/status', requireAdminToken, async (_: Request, res: Response) => {
    try {
      const { data: connections, error: connError } = await supabase
        .from('emcpe_connection_log')
        .select(
          `
          id,
          sessionId,
          userId,
          connectionTime,
          emcpe_tool_call(
            id,
            toolName,
            timestamp,
            statusCode
          )
        `
        )
        .is('disconnectionTime', null);

      if (connError) {
        console.error('Error fetching active connections from Supabase:', connError);
        res.status(500).json({ error: 'Failed to fetch status' });
        return;
      }

      if (!connections || connections.length === 0) {
        console.log(`GET /status - No active connections found (Authenticated)`);
        res.json({ activeConnectionCount: 0, activeConnections: [] });
        return;
      }

      console.log(
        `GET /status - Active connections from DB: ${connections.length} (Authenticated)`
      );
      res.json({
        activeConnectionCount: connections.length,
        activeConnections: connections,
      });
    } catch (err) {
      console.error('Unexpected error in /status endpoint:', err);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  app.get('/history', requireAdminToken, async (req: Request, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = (page - 1) * limit;

    try {
      const {
        data: connections,
        error: connError,
        count,
      } = await supabase
        .from('emcpe_connection_log')
        .select(
          `
          sessionId,
          userId,
          connectionTime,
          disconnectionTime,
          emcpe_tool_call(
            toolName,
            timestamp,
            statusCode
          )
        `,
          { count: 'exact' }
        )
        .order('connectionTime', { ascending: false })
        .range(offset, offset + limit - 1);

      if (connError) {
        console.error('Error fetching connection history from Supabase:', connError);
        res.status(500).json({ error: 'Failed to fetch history' });
        return;
      }

      if (!connections || connections.length === 0) {
        console.log(`GET /history - No history found for page ${page} (Authenticated)`);
        res.json({ totalEntries: count ?? 0, page: page, limit: limit, history: [] });
        return;
      }

      console.log(`GET /history - Returning ${connections.length} history entries (Authenticated)`);
      res.json({
        totalEntries: count ?? 0,
        page: page,
        limit: limit,
        history: connections,
      });
    } catch (err) {
      console.error('Unexpected error in /history endpoint:', err);
      res.status(500).json({ error: 'Internal server error' });
    }
  });
};

export { activeConnections, requireAdminToken, registerDashboardRoutes };
