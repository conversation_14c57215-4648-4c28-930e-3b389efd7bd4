import dotenv from "dotenv";

if (process.env.NODE_ENV !== "production") {
  dotenv.config();
}

import express, { Request, Response } from "express";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { SSEServerTransport } from "@modelcontextprotocol/sdk/server/sse.js";
import { hashData } from "./hashData.js";
import { supabase } from "./supabase.js";
import { ACTION_INPUT_MODELS_ZOD, ACTION_INPUTS } from "./constants.js";
import { activeConnections, registerDashboardRoutes } from "./dashboard.js";
import { handleConnectionClose, handleToolCall } from "./funtions.js";

const app = express();

app.get("/:token/sse", async (req: Request, res: Response) => {
  let sessionId: string | null = null;

  try {
    console.log(`GET /:token/sse - Request received`);
    const token = req.params.token;
    const hashedToken = await hashData(token);

    const { data: dbServer, error: serverError } = await supabase
      .from("emcpe_servers")
      .select("tokenHash, userId, preferences") // Fetch preferences
      .eq("tokenHash", hashedToken)
      .single();

    if (serverError || !dbServer) {
      console.error("Server validation error:", serverError);
      res.status(401).json({ error: "Invalid server token" });
      return;
    }

    const userId = dbServer.userId;
    // Extract enablements from preferences, default to empty object if null/undefined
    const preferences = dbServer.preferences as { enablements?: Record<string, boolean> } | null;
    const enablements = preferences?.enablements ?? {};
    console.log(`User ${userId} preferences loaded:`, enablements);

    const { data: userConnections, error: connectionError } = await supabase
      .from("connections")
      .select("id, providerKey")
      .eq("userId", userId);

    if (connectionError) {
      console.error("Error fetching user connections:", connectionError);
      res.status(500).send({ error: "Internal server error fetching connections" });
      return;
    }

    const server = new McpServer({
      name: "EasyMCPeasy",
      version: "1.0.0",
    });

    const transport = new SSEServerTransport("/messages", res);
    sessionId = transport.sessionId;

    (userConnections || []).forEach(
      ({ id: nangoConnectionId, providerKey }: { id: string; providerKey: string }) => {
        ACTION_INPUTS.forEach(
          ({
            provider,
            action,
            model: modelKey,
            description,
          }: {
            provider: string;
            action: string;
            model: string | null;
            description: string;
          }) => {
            if (provider !== providerKey) return;

            const model = modelKey === null
              ? null
              : ACTION_INPUT_MODELS_ZOD[
                modelKey as keyof typeof ACTION_INPUT_MODELS_ZOD
              ];
            if (modelKey && !model) {
              console.error(`Model ${modelKey} not found for action ${action}`);
              return;
            }

            const toolKey = `${provider}_${action}`;
            // Default to enabled if not specified or explicitly set to true
            const isEnabled = enablements[toolKey] !== false;

            if (!isEnabled) {
              console.log(`Tool ${toolKey} disabled by user preference for user ${userId}`);
              return; // Skip registering this tool
            }

            console.log(`Registering enabled tool: ${toolKey} for user ${userId}`);
            server.tool(
              toolKey,
              description,
              model ? model.shape : undefined,
              (inputs: unknown) => {
                return handleToolCall(
                  providerKey,
                  nangoConnectionId,
                  action,
                  toolKey, // Use toolKey here as well
                  inputs,
                  sessionId!,
                );
              },
            );
          },
        );
      },
    );

    const connectionTime = new Date();

    activeConnections[sessionId] = {
      transport,
      userId,
      connectionTime,
      toolCalls: [],
    };

    supabase
      .from("emcpe_connection_log")
      .insert({
        sessionId: sessionId,
        userId: userId,
        connectionTime: connectionTime.toISOString(),
      })
      .then(({ error: insertError }) => {
        if (insertError) {
          console.error(`Failed to log connection start for session ${sessionId}:`, insertError);
        } else {
          console.log(`Connection start logged for session ${sessionId}`);
        }
      });

    console.log(`Client connected: ${sessionId}, User: ${userId}`);

    req.on("close", () => handleConnectionClose(transport.sessionId));

    await server.connect(transport);
  } catch (error) {
    console.error("Error setting up SSE connection:", error);

    if (sessionId && activeConnections[sessionId]) {
      delete activeConnections[sessionId];
    }

    if (!res.headersSent) {
      res.status(500).json({ error: "Internal server error during SSE setup" });
    }
  }
});

app.post("/messages", async (req: Request, res: Response) => {
  const sessionId = req.query.sessionId as string;
  const connection = activeConnections[sessionId];
  if (connection) {
    await connection.transport.handlePostMessage(req, res);
  } else {
    res.status(404).send("Session not found or inactive");
  }
});

registerDashboardRoutes(app);

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

export default app;
