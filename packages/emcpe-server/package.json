{"private": true, "name": "@makeagent/emcpe-server", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "npm run build && npm run start"}, "type": "module", "dependencies": {"@modelcontextprotocol/sdk": "^1.8.0", "@nangohq/node": "^0.58.1", "@supabase/supabase-js": "^2.49.4", "dotenv": "^16.4.5", "express": "^5.1.0", "typescript": "^5.8.2", "zod": "^3.24.2"}, "devDependencies": {"@types/express": "^5.0.1", "@types/resolve": "^1.20.6", "tsx": "^4.19.3"}}