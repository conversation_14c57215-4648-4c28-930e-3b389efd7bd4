import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      components: resolve(__dirname, './src/components'),
      stores: resolve(__dirname, './src/stores'),
      hooks: resolve(__dirname, './src/hooks'),
      lib: resolve(__dirname, './src/lib'),
      constants: resolve(__dirname, './src/constants'),
      types: resolve(__dirname, './src/types'),
      utils: resolve(__dirname, './src/utils'),
    },
  },
  server: {
    port: 5174, // Different from MakeAgent's port
  },
});
