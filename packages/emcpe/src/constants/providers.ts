import {
  DropboxIcon,
  GithubIcon,
  GmailIcon,
  GoogleCalendarIcon,
  GoogleDocsIcon,
  GoogleDriveIcon,
  GoogleSheetsIcon,
  HarvestIcon,
  LinkedInIcon,
  LinearIcon,
  NotionIcon,
  SlackIcon,
  TwitterIcon,
  ZoomIcon,
} from 'components/icons/providers';
import { ACTION_INPUTS } from './nangoConstants';

// Static UI configuration for providers
const PROVIDER_UI_CONFIG = {
  'google-mail': {
    title: 'Send email with G<PERSON>',
    name: 'Gmail',
    icon: GmailIcon,
    headerColor: 'bg-red-600',
    description: 'Connect to your Gmail to manage emails and drafts in Gmail.',
    descriptionShort: 'Manage emails and drafts.',
  },
  'google-calendar': {
    title: 'Manage events with Google Calendar',
    name: 'Google Calendar',
    icon: GoogleCalendarIcon,
    headerColor: 'bg-blue-600',
    description: 'Connect to Google Calendar to manage your calendar events and meetings.',
    descriptionShort: 'Manage events and meetings.',
  },
  'google-sheet': {
    title: 'Work with Google Sheets',
    name: 'Google Sheets',
    icon: GoogleSheetsIcon,
    headerColor: 'bg-green-600',
    description: 'Connect to Google Sheets to read and write spreadsheet data.',
    descriptionShort: 'Read and write spreadsheet data.',
  },
  notion: {
    title: 'Organize with Notion',
    name: 'Notion',
    icon: NotionIcon,
    headerColor: 'bg-gray-900',
    description: 'Connect to Notion to manage your workspace content.',
    descriptionShort: 'Manage your workspace content.',
  },
  linkedin: {
    title: 'Network on LinkedIn',
    name: 'LinkedIn',
    icon: LinkedInIcon,
    headerColor: 'bg-blue-700',
    description: 'Connect to LinkedIn to manage your professional network.',
    descriptionShort: 'Manage your professional network.',
  },
  'x-social': {
    title: 'Engage on X',
    name: 'X',
    icon: TwitterIcon,
    headerColor: 'bg-black',
    description: 'Connect to X (formerly Twitter) to manage your social presence.',
    descriptionShort: 'Manage your social presence.',
  },
  slack: {
    title: 'Communicate with Slack',
    name: 'Slack',
    icon: SlackIcon,
    headerColor: 'bg-purple-600',
    description: 'Connect to Slack to manage your team communications.',
    descriptionShort: 'Manage your team communications.',
  },
  // "zoom": {
  //   title: "Connect with Zoom",
  //   name: "Zoom",
  //   icon: ZoomIcon,
  //   headerColor: "bg-blue-500",
  //   description:
  //     "Connect to Zoom to manage meetings and recordings.",
  //   descriptionShort: "Manage meetings & recordings.",
  // },
  'google-drive': {
    title: 'Access Google Drive',
    name: 'Google Drive',
    icon: GoogleDriveIcon,
    headerColor: 'bg-yellow-500',
    description: 'Connect to Google Drive to manage files and folders.',
    descriptionShort: 'Manage files & folders.',
  },
  github: {
    title: 'Collaborate on GitHub',
    name: 'GitHub',
    icon: GithubIcon,
    headerColor: 'bg-black',
    invertInModal: true,
    description: 'Connect to GitHub to manage repositories and issues.',
    descriptionShort: 'Manage repositories & issues.',
  },
  'google-docs': {
    title: 'Edit Google Docs',
    name: 'Google Docs',
    icon: GoogleDocsIcon,
    headerColor: 'bg-blue-600', // Same as Calendar for consistency? Or a different blue?
    description: 'Connect to Google Docs to read and edit documents.',
    descriptionShort: 'Read and edit documents.',
  },
  dropbox: {
    title: 'Manage files with Dropbox',
    name: 'Dropbox',
    icon: DropboxIcon,
    headerColor: 'bg-blue-600', // Using a standard blue, adjust if needed
    description: 'Connect to Dropbox to manage your files and folders.',
    descriptionShort: 'Manage files and folders.',
  },
  linear: {
    title: 'Manage projects with Linear',
    name: 'Linear',
    icon: LinearIcon,
    headerColor: 'bg-indigo-600', // Linear's brand color is indigo
    description: 'Connect to Linear to manage issues, projects, and teams.',
    descriptionShort: 'Manage issues and projects.',
  },
  harvest: {
    title: 'Track time with Harvest',
    name: 'Harvest',
    icon: HarvestIcon,
    headerColor: 'bg-orange-600', // Harvest's brand color is orange
    description: 'Connect to Harvest to track time, manage timesheets, and view reports.',
    descriptionShort: 'Track time and manage timesheets.',
  },
} as const;

// Function to generate the dynamic PROVIDER_CONFIGS
function generateProviderConfigs() {
  const providerMap = new Map();

  // Helper to ensure provider entry exists
  const ensureProvider = (key: string) => {
    if (!providerMap.has(key)) {
      providerMap.set(key, {
        id: key,
        available: true, // Assume available if it has tools/resources
        tools: [],
        // Resources and resourceTemplates removed
        ...(PROVIDER_UI_CONFIG[key as keyof typeof PROVIDER_UI_CONFIG] || {
          // Default UI config if not found
          title: `Connect to ${key}`,
          name: key.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          icon: () => null, // Default empty icon
          headerColor: 'bg-gray-600',
          description: `Manage your ${key} connection.`,
          descriptionShort: `Manage ${key}.`,
        }),
      });
    }
    return providerMap.get(key);
  };

  // First, initialize all providers from PROVIDER_UI_CONFIG
  Object.keys(PROVIDER_UI_CONFIG).forEach(key => {
    ensureProvider(key);
  });

  // Process actions as tools
  ACTION_INPUTS.forEach(action => {
    const providerKey = action.provider;
    const provider = ensureProvider(providerKey);
    provider.tools.push({
      name: action.action,
      description: action.description,
    });
  });

  // Resources and resource templates sections removed as per requirement

  // Convert map to the desired object structure
  const finalConfigs: { [key: string]: any } = {};
  providerMap.forEach((value, key) => {
    finalConfigs[key] = value;
  });

  return finalConfigs;
}

// Export the dynamically generated PROVIDER_CONFIGS
const PROVIDER_CONFIGS = generateProviderConfigs();

// Helper function to get a provider by ID
function getProviderConfig(id: string) {
  return PROVIDER_CONFIGS[id];
}

export { PROVIDER_CONFIGS, getProviderConfig };
