---
import AppLayout from '../layouts/AppLayout.astro';
const lastUpdatedDate = new Date().toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
});
---

<AppLayout title="Privacy Policy - EasyMCPeasy">
  <div class="max-w-4xl mx-auto px-4 py-16">
    <div class="rounded-2xl shadow-sm p-8 md:p-12 bg-white dark:bg-gray-800">
      <h1
        class="text-4xl font-bold text-center text-gray-900 dark:text-white mb-4"
      >
        Privacy Policy
      </h1>

      <div class="text-sm text-gray-600 text-center dark:text-gray-400 mb-12">
        Last Updated: {lastUpdatedDate}
      </div>

      <div class="prose dark:prose-invert max-w-none mt-10">
        <h2
          class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8"
        >
          Introduction
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          Welcome to EasyMCPeasy (“the Service”), powered by MakeAgent. This Privacy Policy explains
          how we collect, use, and protect your information when you use EasyMCPeasy. By accessing
          or using the Service, you agree to the practices described here and in our <a
            href="/terms"
            class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500">Terms of Service</a
          >. We are committed to safeguarding your privacy and keeping our practices transparent.
        </p>

        <h2
          class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8"
        >
          1. Information We Collect
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          EasyMCPeasy collects only the minimum information necessary to authenticate your access to
          the Service. This includes:
        </p>
        <ul class="list-disc pl-10 text-gray-600 dark:text-gray-400 space-y-2 mb-8">
          <li>
            <strong class="text-gray-900 dark:text-white">GitHub Authentication Data:</strong> When you
            log in with your GitHub account, we collect basic identifiers (e.g., username) required to
            verify your identity via Supabase.
          </li>
          <li>
            <strong class="text-gray-900 dark:text-white">Anonymized Usage Metrics:</strong> We may collect anonymized usage data to understand how the Service is used and to improve its functionality. This data does not include personally identifiable information and is used solely for analytical purposes.
          </li>
        </ul>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          <strong class="text-gray-900 dark:text-white">Data Flow:</strong> Data sent from your Model
          Context Protocol (MCP) client to third-party services (e.g., Google, X) is forwarded to Nango
          for processing. EasyMCPeasy does not retain, process, or access this data beyond login for
          site authentication.
        </p>

        <h2
          class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8"
        >
          2. How We Use Your Information
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          We use the information we collect solely to:
        </p>
        <ul class="list-disc pl-10 text-gray-600 dark:text-gray-400 space-y-2 mb-8">
          <li>Authenticate your access to EasyMCPeasy.</li>
          <li>
            Facilitate connections between your MCP client and third-party services via Nango.
          </li>
          <li>Maintain and secure the Service.</li>
          <li>Troubleshoot issues and improve functionality.</li>
        </ul>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8">
          3. Data Sharing
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          We share your information only as necessary and in the following limited cases:
        </p>
        <ul class="list-disc pl-10 text-gray-600 dark:text-gray-400 space-y-2 mb-8">
          <li>
            <strong class="text-gray-900 dark:text-white">With Nango:</strong> We forward MCP query-related
            data to Nango (<a
              href="https://nango.dev"
              target="_blank"
              rel="noopener noreferrer"
              class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500">nango.dev</a
            >) to manage tool calls to third-party services. Nango’s handling of data is governed by
            their <a
              href="https://www.nango.dev/privacy-policy-policy"
              target="_blank"
              rel="noopener noreferrer"
              class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500">Privacy Policy</a
            >.
          </li>
          <li>
            <strong class="text-gray-900 dark:text-white">Legal Requirements:</strong> We may disclose
            information if required by law, regulation, or legal process, or to protect our rights, safety,
            or property.
          </li>
          <li>
            <strong class="text-gray-900 dark:text-white">With Your Consent:</strong> We will share information
            if you explicitly request us to do so.
          </li>
        </ul>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8">
          4. Data Security
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          We implement reasonable measures to protect the limited information we collect against
          unauthorized access, disclosure, or loss. However, no online service can guarantee
          absolute security.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8">
          5. Pricing and Future Services
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          EasyMCPeasy is currently provided free of charge. We reserve the right to introduce paid features or services in the future. Any changes to pricing or the introduction of paid services will be communicated in advance, potentially requiring updates to this Privacy Policy or our Terms of Service.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8">
          6. Changes to This Policy
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          We may update this Privacy Policy from time to time. Significant changes will be posted
          here with an updated “Last Updated” date, and we may notify you via email if warranted.
          Your continued use of the Service after updates indicates your acceptance.
        </p>
      </div>

      <div class="mt-12 text-center">
        <a
          href="/"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 no-underline"
        >
          Back to EasyMCPeasy
        </a>
      </div>
    </div>
  </div>
</AppLayout>
