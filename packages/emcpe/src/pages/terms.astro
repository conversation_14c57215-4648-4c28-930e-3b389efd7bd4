---
import AppLayout from '../layouts/AppLayout.astro';
const lastUpdatedDate = new Date().toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
});
---

<AppLayout title="Terms of Service - EasyMCPeasy">
  <div class="max-w-4xl mx-auto px-4 py-16">
    <div class="rounded-2xl shadow-sm p-8 md:p-12 bg-white dark:bg-gray-800">
      <h1 class="text-4xl font-bold text-center text-gray-900 dark:text-white mb-4" style="font-size: 2.25rem;">
        Terms of Service
      </h1>

      <div class="text-sm text-gray-600 text-center dark:text-gray-400 mb-12">
        Last Updated: {lastUpdatedDate}
      </div>

      <div class="prose dark:prose-invert max-w-none">
        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          Introduction
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          Welcome to EasyMCPeasy (“the Service”), powered by MakeAgent. These Terms of Service
          (“Terms”) govern your use of the Service. By accessing or using EasyMCPeasy, you agree to
          be bound by these Terms. If you do not agree, please do not use the Service.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          1. Service Description
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          EasyMCPeasy enables your Model Context Protocol (MCP) client to connect to third-party
          services such as Google or X through a server hosted on Supabase. The Service utilizes
          Nango, an established integration platform, to facilitate these connections. Data from
          your MCP client is forwarded to Nango for processing; EasyMCPeasy does not retain or
          process this data.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          2. Authentication
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          Access to EasyMCPeasy requires authentication via your GitHub account. You are responsible
          for maintaining the security of your GitHub credentials. All activities conducted through
          your account are your responsibility.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          3. Acceptable Use
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          You agree to use the Service in compliance with applicable laws and for its intended
          purpose. Prohibited actions include engaging in illegal activities, attempting to disrupt
          or compromise the Service, transmitting malicious code, or attempting to reverse-engineer
          the Service.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          4. Data and Privacy
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          EasyMCPeasy collects only the minimum data required to authenticate your GitHub login via
          Supabase. We do not retain or process data sent from your MCP client beyond forwarding it
          to Nango. Our practices are detailed in the <a
            href="/privacy"
            class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500"
            >EasyMCPeasy Privacy Policy</a
          >. Nango’s <a
            href="https://www.nango.dev/privacy-policy"
            target="_blank"
            rel="noopener noreferrer"
            class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500">Privacy Policy</a
          > governs their handling of your data.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          5. Third-Party Services
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          EasyMCPeasy relies on Nango (<a
            href="https://nango.dev"
            target="_blank"
            rel="noopener noreferrer"
            class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500">nango.dev</a
          >) for integration functionality, subject to their <a
            href="https://www.nango.dev/terms"
            target="_blank"
            rel="noopener noreferrer"
            class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500">Terms of Service</a
          >. Third-party services you connect to have their own terms and policies, which you must
          adhere to. We are not responsible for the performance or practices of these external
          services.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          6. Intellectual Property
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          The Service, including its server implementation, is the intellectual property of
          MakeAgent. You are granted permission to use EasyMCPeasy as provided, but we do not claim
          ownership of any data you transmit through the Service.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          7. Disclaimer and Limitation of Liability
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          EasyMCPeasy is provided “as is” without warranties of any kind. We strive to maintain
          reliable service but cannot guarantee uninterrupted operation, particularly due to
          reliance on Nango and third-party services. In the event of issues, our liability is
          limited to a nominal amount (e.g., $5) if directly attributable to the Service.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          8. Termination
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          You may discontinue use of the Service at any time. We reserve the right to suspend or
          terminate your access without prior notice if you breach these Terms or for other
          operational reasons.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          9. Changes to Terms
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          We may update these Terms as needed. Changes will be posted here or communicated via email
          if significant. Continued use of the Service constitutes acceptance of the updated Terms.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          10. Governing Law
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          These Terms are governed by the laws of the jurisdiction where MakeAgent operates. Any
          disputes will be resolved in the courts of that jurisdiction.
        </p>

        <h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-white mt-12 mb-8" style="font-size: 1.5rem;">
          11. Severability
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          If any provision of these Terms is found unenforceable, the remaining provisions will
          remain in effect to the fullest extent permitted by law.
        </p>
        <div class="mt-12 text-center">
          <a
            href="/"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 no-underline"
          >
            Back to EasyMCPeasy
          </a>
        </div>
      </div>
    </div>
  </div>
</AppLayout>
