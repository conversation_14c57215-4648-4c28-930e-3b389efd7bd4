---
export interface Props {
  title: string;
}

const { title } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>{title} | EasyMCPeasy</title>
    <meta name="title" content="EasyMCPeasy" />
    <meta name="description" content="MCP made easy" />

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EasyMCPeasy" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://easymcpeasy.com" />
    <meta property="og:title" content="EasyMCPeasy" />
    <meta property="og:description" content="MCP made easy" />
    <meta property="og:image" content="/logo.svg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://easymcpeasy.com" />
    <meta property="twitter:title" content="EasyMCPeasy" />
    <meta property="twitter:description" content="MCP made easy" />
    <meta property="twitter:image" content="/logo.svg" />

    <!-- Mobile Web App -->
    <meta name="theme-color" content="#ffffff" />
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    {/* apple-mobile-web-app-title already set */}

    <!-- Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />

    <!-- Security -->
    {/* <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;" /> */}

    <!-- Canonical -->
    <link rel="canonical" href="https://easymcpeasy.com" />

    <meta name="generator" content={Astro.generator} />
    {import.meta.env.PROD && <script defer src="https://cloud.umami.is/script.js" data-website-id="dc4b0252-7a93-43e3-9a71-e14bc135ff6e"></script>}
  </head>
  <body
    class="min-h-screen flex flex-col bg-slate-50 dark:bg-gray-900 text-gray-800 dark:text-gray-100"
  >
    <header
      class="py-4 px-6 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
    >
      <div class="container mx-auto flex justify-between items-center">
        <a
          href="/"
          class="text-xl font-bold bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent"
          >EasyMCPeasy</a
        >
        <nav>
          <a
            href="/"
            class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400"
            >Home</a
          >
        </nav>
      </div>
    </header>

    <main class="flex-1 container mx-auto px-4 py-8 max-w-4xl">
      <slot />
    </main>

    <footer
      class="py-4 px-6 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center mt-auto"
    >
      <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
        Powered by
        <a
          href="https://makeagent.com"
          target="_blank"
          rel="noopener noreferrer"
          class="font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300"
        >
          MakeAgent
        </a>
        . Integrations are
        <a
          href="https://github.com/makeagent/emcpe-nango"
          target="_blank"
          rel="noopener noreferrer"
          class="font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300"
        >
          open source
        </a>
        .
      </p>
      <div class="text-xs text-gray-500 dark:text-gray-500 flex justify-center space-x-4">
        <a href="/privacy" class="hover:text-indigo-500 dark:hover:text-indigo-400">
          Privacy Policy
        </a>
        <a href="/terms" class="hover:text-indigo-500 dark:hover:text-indigo-400">
          Terms of Service
        </a>
      </div>
    </footer>
  </body>
</html>
