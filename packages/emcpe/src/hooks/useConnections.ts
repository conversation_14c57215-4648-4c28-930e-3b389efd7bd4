import { useEffect } from 'react';
import { connectionsStore } from '../stores/connections';
import { useAuth } from './useAuth';

function useConnections() {
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      console.log('Loading connections...');
      connectionsStore.loadConnections();
    }
  }, [isAuthenticated]);

  return connectionsStore.useState();
}

export { useConnections };
