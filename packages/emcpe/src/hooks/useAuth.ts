import { authStore } from '../stores/auth';

function useAuth() {
  const state = authStore.useAuthState();

  return {
    ...state,
    verifyOtp: (otp: string) => authStore.verifyOtp(otp),
    login: (email: string, password: string) => authStore.login(email, password),
    loginWithGitHub: () => authStore.loginWithGitHub(),
    register: (email: string, password: string) => authStore.register(email, password),
    logout: () => authStore.logout(),
    openLoginModal: () => authStore.openLoginModal(),
    closeAuthModal: () => authStore.closeAuthModal(),
    resetPassword: (email: string) => authStore.resetPassword(email),
    resetLoginForm: () => authStore.resetLoginForm(),
    setNewPassword: (password: string, doLogin: boolean) =>
      authStore.setNewPassword(password, doLogin),
    clearError: () => authStore.clearError(),
  };
}

export { useAuth };
