import { useAuth } from '../hooks/useAuth';
import { X, Github } from 'lucide-react';

function AuthModal() {
  const { error, isModalOpen, closeAuthModal, loginWithGitHub } = useAuth();

  if (!isModalOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl mb-[30vh] p-8 w-full max-w-md mx-4 relative">
        <h2 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-white">
          Login to EasyMCPeasy
        </h2>

        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-sm text-red-600 dark:text-red-400">
            {error}
          </div>
        )}

        <div className="space-y-6">
          <p className="text-gray-600 dark:text-gray-300 text-center mb-4">
            Sign in with your GitHub account to continue
          </p>

          <button
            onClick={loginWithGitHub}
            className="w-full flex items-center justify-center gap-3 px-4 py-3 text-white bg-gray-800 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg font-medium transition-colors"
          >
            <Github className="w-5 h-5" />
            Continue with GitHub
          </button>

          <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-4">
            By signing in, you agree to our{' '}
            <a href="/terms" target="_blank" className="underline">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" target="_blank" className="underline">
              Privacy Policy
            </a>
            .
          </p>
        </div>

        <button
          onClick={closeAuthModal}
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
        >
          <X className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
}

export { AuthModal };
