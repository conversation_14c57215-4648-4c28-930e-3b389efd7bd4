interface DropboxIconProps {
  className?: string;
}

function DropboxIcon({ className = 'w-12 h-12' }: DropboxIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 62 62" fill="none" className={className}>
      <g clipPath="url(#A)">
        <path
          d="M31 19.017l-11 7.017 11 7.017-11 7.017-11-7.055 11-7.017-11-6.979L20 12l11 7.017zM19.943 42.326l11-7.017 11 7.017-11 7.017-11-7.017zM31 33.014l11-7.017-11-6.979L41.943 12l11 7.017-11 7.017 11 7.017-11 7.017L31 33.014z"
          fill="#0061ff"
        />
      </g>
      <defs>
        <clipPath id="A">
          <path fill="#fff" transform="translate(9 9)" d="M0 0h44v44H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export { DropboxIcon };
