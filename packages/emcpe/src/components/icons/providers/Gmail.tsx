function GmailIcon({ className = 'w-8 h-8' }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 62 62" className={className}>
      <g clipPath="url(#gmail-clip)">
        <path d="M12 47.999h7v-17l-10-7.5v21.5a3 3 0 0 0 3 3z" fill="#4285f4" />
        <path d="M43 47.999h7a3 3 0 0 0 3-3v-21.5l-10 7.5" fill="#34a853" />
        <path d="M43 17.999v13l10-7.5v-4c0-3.71-4.235-5.825-7.2-3.6" fill="#fbbc04" />
        <path d="M19 31V18l12 9 12-9v13l-12 9" fill="#ea4335" />
        <path d="M9 19.499v4l10 7.5v-13l-2.8-2.1c-2.97-2.225-7.2-.11-7.2 3.6z" fill="#c5221f" />
      </g>
      <defs>
        <clipPath id="gmail-clip">
          <path fill="#fff" transform="translate(9 15)" d="M0 0h44v33H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export { GmailIcon };
