interface LinearIconProps {
  className?: string;
}

function LinearIcon({ className = 'w-12 h-12' }: LinearIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 62 62" fill="none" className={className}>
      <g clipPath="url(#linear-clip)" fill="#5e6ad2">
        <path d="M45.344 47.719c.44-.352.792-.704 1.144-1.144a21.99 21.99 0 0 0 0-31.152 21.99 21.99 0 0 0-31.152 0l-1.056 1.232 31.064 31.064zm-2.64 1.935l-30.36-30.36c-.616.968-1.144 1.936-1.584 2.992l28.952 28.952c1.056-.44 2.024-.968 2.992-1.584zm-6.336 2.729L9.616 25.631c-.352 1.32-.528 2.64-.616 3.96l23.408 23.408c1.32-.088 2.64-.264 3.96-.616zm-8.8.441L9.176 34.432c.704 4.488 2.728 8.712 6.16 12.232 3.52 3.344 7.832 5.456 12.232 6.16z" />
      </g>
      <defs>
        <clipPath id="linear-clip">
          <path fill="#fff" transform="translate(9 9)" d="M0 0h44v44H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export { LinearIcon };
