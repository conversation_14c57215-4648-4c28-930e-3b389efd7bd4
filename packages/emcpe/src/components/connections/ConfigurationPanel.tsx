import { useState, useEffect, useRef } from 'react';
import clsx from 'clsx';
import { Loader2, AlertCircle } from 'lucide-react';
import { getProviderConfig } from 'constants/providers';
import { mcpeasyServerStore } from 'stores/mcpeasyServer';

type ProviderConfigType = ReturnType<typeof getProviderConfig>;

interface ConfigurationPanelProps {
  provider: string;
  config: ProviderConfigType;
  hasConnection: boolean;
  isConnecting: boolean;
  handleConnect: () => void;
  // Add props for disconnect state and handler
  isDisconnecting: boolean;
  handleDisconnect: () => void;
}

function ConfigurationPanel({
  provider,
  config,
  hasConnection,
  handleConnect,
  // Receive new props
  isDisconnecting,
  handleDisconnect,
}: ConfigurationPanelProps) {
  // Remove internal state for disconnecting
  // const [isDisconnecting, setIsDisconnecting] = useState(false);
  const preferences = mcpeasyServerStore.useServerPreferences();
  const { isLoading: isUpdatingPrefs, error: updateError } = mcpeasyServerStore.useServerState();

  const [localEnablements, setLocalEnablements] = useState<Record<string, boolean>>({});

  const prevIsUpdatingPrefs = useRef(false);

  useEffect(() => {
    setLocalEnablements(preferences?.enablements ?? {});
  }, [preferences]);

  useEffect(() => {
    if (prevIsUpdatingPrefs.current && !isUpdatingPrefs) {
      if (updateError) {
        setLocalEnablements(preferences?.enablements ?? {});
      }
    }
    prevIsUpdatingPrefs.current = isUpdatingPrefs;
  }, [isUpdatingPrefs, updateError, preferences]);

  // REMOVE internal handleDisconnect function definition
  // const handleDisconnect = async () => { ... };

  const handleEnableAll = () => {
    if (!config?.tools) return; // No tools to enable

    const allEnabled: Record<string, boolean> = {};
    config.tools.forEach((tool: any) => {
      const toolKey = `${provider}_${tool.name}`;
      allEnabled[toolKey] = true; // Explicitly set all to true
    });
    setLocalEnablements(allEnabled); // Optimistic update
    mcpeasyServerStore.updatePreferences(allEnabled);
  };

  const handleDisableAll = () => {
    if (!config?.tools) return; // No tools to disable

    const allDisabled: Record<string, boolean> = {};
    config.tools.forEach((tool: any) => {
      const toolKey = `${provider}_${tool.name}`;
      allDisabled[toolKey] = false; // Explicitly set all to false
    });
    setLocalEnablements(allDisabled); // Optimistic update
    mcpeasyServerStore.updatePreferences(allDisabled);
  };

  return (
    <div className="py-6">
      <div className="space-y-6">
        <p className="text-sm text-gray-600 dark:text-gray-300">
          {!hasConnection
            ? `Connect to utilize ${config.name} tools` // Updated wording
            : 'Disconnecting will remove the tools the next time the client boots.'}
        </p>

        {!hasConnection && (
          <div>
            <button
              onClick={handleConnect}
              className="px-8 py-3 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-500 rounded-sm"
            >
              Connect
            </button>
          </div>
        )}

        {hasConnection && (
          <div>
            <button
              onClick={handleDisconnect}
              disabled={isDisconnecting}
              className={clsx(
                'px-6 py-2.5 text-sm font-medium border rounded-sm inline-flex items-center gap-2',
                isDisconnecting
                  ? 'border-gray-300 text-gray-400 cursor-not-allowed dark:border-gray-600 dark:text-gray-500'
                  : 'border-red-700 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300'
              )}
            >
              {isDisconnecting && <Loader2 className="w-4 h-4 animate-spin" />}
              {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
            </button>
          </div>
        )}

        {/* Tool Preferences Section - Always visible */}
        <div
          className={clsx(
            'border-t pr-2 border-gray-200 dark:border-gray-700 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent overflow-y-auto max-h-[calc(100vh-500px)] md:max-h-[calc(85vh-450px)]',
            {
              'pt-12': !hasConnection,
              'pt-6': hasConnection,
            }
          )}
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              Tool Preferences
            </h3>
            {/* Add Select/Deselect All Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={handleEnableAll}
                disabled={isUpdatingPrefs} // Disable while updating
                className="px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                Enable All
              </button>
              <button
                onClick={handleDisableAll}
                disabled={isUpdatingPrefs} // Disable while updating
                className="px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                Disable All
              </button>
            </div>
          </div>
          <div className="flex items-center justify-between mb-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {hasConnection
                ? 'Select which tools can be used for this connection. Tools are enabled by default.'
                : 'Select which tools will be available when connecting. Tools are enabled by default.'}
            </p>
          </div>
          {updateError && !isUpdatingPrefs && (
            <div className="flex items-center text-sm text-red-600 dark:text-red-400 mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 rounded-md">
              <AlertCircle className="w-4 h-4 mr-2 flex-shrink-0" />
              <span>Error updating preferences: {updateError}</span>
            </div>
          )}
          <div className="space-y-3">
            {config?.tools?.map((tool: any) => {
              const toolKey = `${provider}_${tool.name}`;

              const isChecked = localEnablements[toolKey] !== false;

              const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
                const newCheckedState = e.target.checked;

                const updatedEnablements = { ...localEnablements };

                if (newCheckedState) {
                  delete updatedEnablements[toolKey];
                } else {
                  // If unchecked, set the key to false locally
                  updatedEnablements[toolKey] = false; // Corrected variable name
                }
                setLocalEnablements(updatedEnablements); // Corrected variable name

                // Create the update object with only the changed tool's state
                const singleUpdate = { [toolKey]: newCheckedState }; // Pass true or false directly

                // Call the store update with only the single change
                mcpeasyServerStore.updatePreferences(singleUpdate);
              };

              return (
                <div key={toolKey} className="flex items-center">
                  <input
                    id={toolKey}
                    name={toolKey}
                    type="checkbox"
                    checked={isChecked}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-indigo-600 dark:ring-offset-gray-800"
                  />
                  <label
                    htmlFor={toolKey}
                    className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    <code className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded">
                      {tool.name}
                    </code>
                    {/* Add truncation classes - move parentheses */}
                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 align-bottom">
                      (
                      <span className="inline-block overflow-hidden whitespace-nowrap text-ellipsis max-w-[180px] sm:max-w-[280px] md:max-w-[380px] align-bottom">
                        {' '}
                        {/* Adjusted max-width slightly */}
                        {tool.description}
                      </span>
                      ) {/* Closing parenthesis outside truncated span */}
                    </span>
                  </label>
                </div>
              );
            })}
            {!config?.tools?.length && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No tools available for this provider.
              </p>
            )}
          </div>
        </div>
        {/* Removed closing parenthesis and brace for hasConnection */}
      </div>
    </div>
  );
}

export { ConfigurationPanel };
