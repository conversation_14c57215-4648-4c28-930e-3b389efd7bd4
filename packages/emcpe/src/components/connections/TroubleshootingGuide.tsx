function TroubleshootingGuide() {
  return (
    <div className="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg">
      <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
        Setup and troubleshooting
      </h3>
      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2 list-disc pl-4">
        <li>
          This setup requires Node (specifically npx) to be installed globally on your local
          machine.
        </li>
        <li>
          Make sure the reference to npx in the server config is using the correct path, especially
          if you're using a package manager like asdf.
        </li>
        <li>
          If npx is installed in a different path than the system default, paste the absolute path
          in place of npx in the server config
        </li>
        <li>If using asdf, run asdf which npx to get the absolute path</li>
        <li>To install node on your machine, visit nodejs.org</li>
      </ul>
    </div>
  );
}

export { TroubleshootingGuide };
