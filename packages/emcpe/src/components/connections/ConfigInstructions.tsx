interface ConfigInstructionsProps {
  instructions: string[];
}

function ConfigInstructions({ instructions }: ConfigInstructionsProps) {
  return (
    <div className="space-y-2">
      {instructions.map((instruction, index) => (
        <p key={index} className="text-sm text-gray-600 dark:text-gray-400">
          {index + 1}. {instruction}
        </p>
      ))}
    </div>
  );
}

export { ConfigInstructions };
