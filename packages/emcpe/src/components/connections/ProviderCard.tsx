import { useAuth } from '../../hooks/useAuth';
import { connectionsStore } from '../../stores/connections';
import { useModal } from 'hooks/useModal';
import clsx from 'clsx';

interface ProviderCardProps {
  providerKey: string;
  config: any;
}

function ProviderCard({ providerKey, config }: ProviderCardProps) {
  const { isAuthenticated, openLoginModal } = useAuth();
  const { openModal } = useModal();
  const { connections } = connectionsStore.useState();
  const Icon = config.icon;

  const connection = connections.find(c => c.providerKey === providerKey);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isAuthenticated) {
      openLoginModal();
      return;
    }
    openModal(`connections-${providerKey}`);
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <div
        className={clsx(
          'flex items-center gap-4 flex-1 min-w-0 mb-4 sm:mb-0',
          config.available && 'cursor-pointer'
        )}
        onClick={config.available ? handleClick : undefined}
      >
        <Icon className="w-10 h-10 flex-shrink-0" />
        <div className="min-w-0">
          <h3 className="font-medium text-gray-900 dark:text-white">{config.name}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 truncate pr-4">
            {config.descriptionShort}
          </p>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
        {config.available ? (
          <button
            onClick={handleClick}
            className={`px-4 py-2.5 text-sm font-medium rounded-lg shadow-sm text-center flex-1 sm:flex-initial ${
              connection
                ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                : 'bg-indigo-600 hover:bg-indigo-500 text-white dark:bg-indigo-500 dark:hover:bg-indigo-400'
            }`}
          >
            {connection ? 'Manage' : 'Connect'}
          </button>
        ) : (
          <span className="px-4 py-2 text-sm font-medium text-gray-400 dark:text-gray-500 text-center">
            Coming soon
          </span>
        )}
      </div>
    </div>
  );
}

export { ProviderCard };
