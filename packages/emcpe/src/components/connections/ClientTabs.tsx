import clsx from 'clsx';

// Add 'cline' to types
interface ClientTabsProps {
  activeTab: 'cline' | 'cursor' | 'claude' | 'windsurf';
  onTabChange: (tab: 'cline' | 'cursor' | 'claude' | 'windsurf') => void;
}

function ClientTabs({ activeTab, onTabChange }: ClientTabsProps) {
  return (
    <div className="flex space-x-1 mb-4 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
      {/* Add Cline button first */}
      <button
        onClick={() => onTabChange('cline')}
        className={clsx(
          'flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors',
          activeTab === 'cline'
            ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
            : 'text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
        )}
      >
        Cline
      </button>
      {/* Windsurf button second */}
      <button
        onClick={() => onTabChange('windsurf')}
        className={clsx(
          'flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors',
          activeTab === 'windsurf'
            ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
            : 'text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
        )}
      >
        Windsurf
      </button>
      {/* Cursor button third */}
      <button
        onClick={() => onTabChange('cursor')}
        className={clsx(
          'flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors',
          activeTab === 'cursor'
            ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
            : 'text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
        )}
      >
        Cursor
      </button>
      {/* Claude button fourth */}
      <button
        onClick={() => onTabChange('claude')}
        className={clsx(
          'flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors',
          activeTab === 'claude'
            ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
            : 'text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
        )}
      >
        Claude
      </button>
    </div>
  );
}

export { ClientTabs };
