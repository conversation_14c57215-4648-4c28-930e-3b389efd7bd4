import { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, EyeOff, Loader2, Setting<PERSON>, RefreshCw } from 'lucide-react';
import { authStore } from '../../stores/auth';
import { mcpeasyServerStore } from '../../stores/mcpeasyServer';
import { SERVER_URL_PREFIX } from '../../constants/constants';

function ServerConfiguration() {
  const { isAuthenticated } = authStore.useAuthState();
  const { serverToken, isLoading, error } = mcpeasyServerStore.useServerState();
  const [isUrlVisible, setIsUrlVisible] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [copied, setCopied] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleCopyUrl = () => {
    if (!isAuthenticated) {
      authStore.openLoginModal();
      return;
    }
    if (serverToken) {
      navigator.clipboard.writeText(SERVER_URL_PREFIX + serverToken);
      setCopied(true);
      setTimeout(() => setCopied(false), 1000);
      console.log('Server URL copied!');
    }
  };

  const handleRegenerateToken = async () => {
    if (!isAuthenticated) {
      authStore.openLoginModal();
      return;
    }

    setIsRegenerating(true);
    try {
      await mcpeasyServerStore.regenerateServerToken();
      setIsMenuOpen(false);
    } catch (error) {
      console.error('Error regenerating token:', error);
    } finally {
      setIsRegenerating(false);
    }
  };

  const displayValue = serverToken ? SERVER_URL_PREFIX + serverToken : '';

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">Server Configuration</h2>
        {isAuthenticated && serverToken && !isLoading && (
          <div className="relative" ref={menuRef}>
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Settings className="w-5 h-5" />
            </button>
            {isMenuOpen && (
              <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                <button
                  onClick={handleRegenerateToken}
                  disabled={isRegenerating}
                  className="flex items-center w-full px-6 py-3 text-sm text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 whitespace-nowrap"
                >
                  {isRegenerating ? (
                    <Loader2 className="w-4 h-4 mr-2 flex-shrink-0" />
                  ) : (
                    <RefreshCw className="w-4 h-4 mr-2 flex-shrink-0" />
                  )}
                  Revoke & regenerate
                </button>
              </div>
            )}
          </div>
        )}
      </div>
      {!isAuthenticated ? (
        <div className="text-center py-8">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Connect to everything
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Log in to view and manage your server configuration
          </p>
          <div className="space-y-3 space-x-3">
            <button
              onClick={() => authStore.openLoginModal()}
              className="w-full px-6 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="inline-block"
              >
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
              </svg>
              Sign in with GitHub
            </button>
          </div>
        </div>
      ) : isLoading || isRegenerating ? (
        <div className="flex items-center justify-center p-3 text-gray-500 dark:text-gray-400">
          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
          <span>
            {isRegenerating ? 'Regenerating token...' : 'Loading server configuration...'}
          </span>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center p-3 text-red-600 dark:text-red-400">
          <span>Error: {error}</span>
        </div>
      ) : !serverToken ? (
        <div className="flex items-center justify-center p-3 text-gray-500 dark:text-gray-400">
          <span>Server token not found. It should be generated automatically.</span>
        </div>
      ) : (
        <div className="flex items-center space-x-2 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div className="flex-1 font-mono text-sm break-all">
            {isUrlVisible
              ? displayValue
              : SERVER_URL_PREFIX + '•'.repeat(Math.min(20, serverToken.length))}
          </div>
          <button
            onClick={() => setIsUrlVisible(!isUrlVisible)}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 flex-shrink-0"
          >
            {isUrlVisible ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
          </button>
          <button
            onClick={handleCopyUrl}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            {copied ? <Check className="w-5 h-5 text-green-500" /> : <Copy className="w-5 h-5" />}
          </button>
        </div>
      )}
    </div>
  );
}

export { ServerConfiguration };
