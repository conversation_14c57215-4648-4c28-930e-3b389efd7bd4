import { useState } from 'react';
import { TabNavigation } from './TabNavigation';
import { ConnectionsTab } from './ConnectionsTab';
import { ServerConfigTab } from './ServerConfigTab';
import { ConnectionModal } from 'components/connections/ConnectionModal';
import { useModal } from 'hooks/useModal';

type Tab = 'connections' | 'server';

function ConnectionsList() {
  const [activeTab, setActiveTab] = useState<Tab>('connections');
  const { closeModal, activeModal } = useModal();

  return (
    <div className="space-y-6">
      <div className="text-center py-6">
        <h2 className="text-2xl font-medium text-gray-700 dark:text-gray-300 my-12 sm:my-4">
          One MCP server connecting you to anywhere
          <a
            href="https://github.com/makeagent/emcpe-nango"
            target="_blank"
            rel="noopener noreferrer"
            className="text-[15px] align-super text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 ml-0.5"
          >
            *
          </a>
        </h2>
      </div>

      <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />

      <ConnectionModal
        isOpen={activeModal.startsWith('connections-')}
        onClose={() => {
          closeModal();
        }}
        provider={activeModal?.replace('connections-', '')}
        onConnected={() => {
          closeModal();
        }}
        onDisconnected={() => {
          closeModal();
        }}
      />

      {activeTab === 'connections' ? <ConnectionsTab /> : <ServerConfigTab />}
    </div>
  );
}

export { ConnectionsList };
