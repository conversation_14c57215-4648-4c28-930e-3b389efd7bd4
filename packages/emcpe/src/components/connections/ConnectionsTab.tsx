import { useState } from 'react';
import { PROVIDER_CONFIGS } from '../../constants/providers';
import { ProviderCard } from './ProviderCard';
import { SearchInput } from './SearchInput';

function ConnectionsTab() {
  const [searchTerm, setSearchTerm] = useState('');
  const [displayLimit, setDisplayLimit] = useState(30);

  const providers = Object.entries(PROVIDER_CONFIGS)
    .sort(([, configA], [, configB]) => configA.name.localeCompare(configB.name))
    .filter(
      ([, config]) =>
        config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        config.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .slice(0, displayLimit);

  const hasMore = Object.keys(PROVIDER_CONFIGS).length > displayLimit;

  return (
    <>
      <SearchInput value={searchTerm} onChange={setSearchTerm} />

      <div className="grid gap-4">
        {providers.map(([key, config]) => (
          <ProviderCard key={key} providerKey={key} config={config} />
        ))}
      </div>

      {hasMore && (
        <div className="text-center mt-8">
          <button
            onClick={() => setDisplayLimit(prev => prev + 30)}
            className="px-6 py-2 text-sm font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
          >
            Load more
          </button>
        </div>
      )}
    </>
  );
}

export { ConnectionsTab };
