import { useState } from 'react';
import { ClientTabs } from './ClientTabs';
import { ClientTabContent } from './ClientTabContent';

// Add 'cline' to the type
type ClientTab = 'cline' | 'cursor' | 'claude' | 'windsurf';

function ClientIntegration() {
  // Set default active tab to 'cline'
  const [activeClientTab, setActiveClientTab] = useState<ClientTab>('cline');

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Client Integration</h2>
      <ClientTabs activeTab={activeClientTab} onTabChange={setActiveClientTab} />
      <ClientTabContent activeTab={activeClientTab} />
    </div>
  );
}

export { ClientIntegration };
