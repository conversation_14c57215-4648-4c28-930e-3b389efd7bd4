import React from 'react'; // Ensure React is imported for Fragment
import { getProviderConfig } from 'constants/providers';

// Infer the type from the return value of getProviderConfig
type ProviderConfigType = ReturnType<typeof getProviderConfig>;

interface OverviewPanelProps {
  config: ProviderConfigType;
}

function OverviewPanel({ config }: OverviewPanelProps) {
  return (
    // Remove flex/height from main div
    <div className="pt-6">
      <div className="mb-8 space-y-6">
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{config.description}</p>
      </div>
      {/* Make this container grow */}
      <div className="space-y-8 flex-1 flex flex-col">
        {/* Make section a flex container that grows AND scrolls if needed */}
        <section className="border border-gray-200 dark:border-gray-700 rounded-lg p-6  bg-gray-50/50 dark:bg-gray-800/30 flex-1 flex flex-col overflow-y-auto">
          {' '}
          {/* Added overflow-y-auto, removed overflow-hidden */}
          {/* Prevent header shrinking */}
          <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-white flex-shrink-0">
            Tools
          </h3>
          {/* This div contains the grid and scrolls, takes remaining space */}
          <div className="grid grid-cols-[auto_1fr] items-baseline gap-x-2 gap-y-4 pr-2 overflow-y-auto flex-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent max-h-[calc(100vh-500px)] md:max-h-[calc(85vh-450px)]">
            {config?.tools?.length ? (
              config.tools.map((tool: any) => (
                // Render code and p tags directly as grid children
                <React.Fragment key={tool.name}>
                  {/* Tool name tag (Column 1) - Align self to start */}
                  <code className="justify-self-start text-sm font-medium font-mono text-indigo-700 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/40 px-2 py-1 rounded">
                    {tool.name}
                  </code>
                  {/* Description (Column 2) - will wrap and align */}
                  <p className="text-sm text-gray-600 dark:text-gray-400">{tool.description}</p>
                </React.Fragment>
              ))
            ) : (
              // Span across both columns if no tools
              <p className="col-span-2 text-sm text-gray-500 dark:text-gray-400">
                No tools available
              </p>
            )}
          </div>
        </section>
      </div>
    </div>
  );
}

export { OverviewPanel };
