import { X } from 'lucide-react';

interface GoogleOAuthNoticeProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
}

function GoogleOAuthNotice({ isOpen, onClose, onAccept }: GoogleOAuthNoticeProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 flex items-start justify-center z-[60] pt-[33vh]">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg mx-4 p-8 -translate-y-1/4">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Unvalidated Google OAuth
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="text-base text-gray-700 dark:text-gray-300 space-y-5">
          <p>
            EasyMCPeasy is waiting on OAuth Client Validation from Google. You will see a warning
            when you proceed.
          </p>
          <p className="font-medium">
            ➜ For now, select "advanced" followed by "continue" to complete OAuth.
          </p>
          <p>
            Validation by Google determines the appropriateness of granted scopes. EasyMCPeasy
            overall requires many scopes to offer the full range of Google services, but what you
            use is up to you, and we request scopes separately by service.
          </p>
          <p>
            Your credentials are handled securely by{' '}
            <a
              href="https://www.nango.dev/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Nango
            </a>
            .
          </p>
          <p className="font-medium">
            ➜ Select "advanced" followed by "continue" to complete OAuth.
          </p>
        </div>

        <div className="mt-8 flex justify-end">
          <button
            onClick={onAccept}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white text-base font-medium rounded-md"
          >
            Ship baby ship!
          </button>
        </div>
      </div>
    </div>
  );
}

export { GoogleOAuthNotice };
