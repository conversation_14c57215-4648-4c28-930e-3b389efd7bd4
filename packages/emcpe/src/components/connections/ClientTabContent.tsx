import { ConfigInstructions } from './ConfigInstructions';
import { ConfigCode } from './ConfigCode';
import { mcpeasyServerStore } from 'stores/mcpeasyServer';
import { Copy, Check } from 'lucide-react';
import { useState } from 'react';

const MCP_WORKER_BASE_URL = 'https://mcp.easymcpeasy.com';

// Add 'cline' to the possible active tabs
interface ClientTabContentProps {
  activeTab: 'cline' | 'cursor' | 'claude' | 'windsurf';
}

const PLACEHOLDER_TOKEN = '***************';

// Add 'cline' to the function signature
const getConfigContent = (
  activeTab: 'cline' | 'cursor' | 'claude' | 'windsurf',
  serverToken?: string
) => {
  // Generate config based on the active tab
  switch (activeTab) {
    // Add case for 'cline'
    case 'cline':
      return {
        instructions: [
          'Click on MCP servers next to the [+] New Task button',
          'Click on remote servers',
          "EITHER: Specify 'EasyMCPeasy', and paste in your url above postfixed with /sse",
          'OR: Click Edit Configuration, and paste in the following configuration:',
        ],
        code: {
          mcpServers: {
            EasyMCPeasy: {
              url: `${MCP_WORKER_BASE_URL}/${serverToken || PLACEHOLDER_TOKEN}/sse`,
              disabled: false,
              autoApprove: [],
            },
          },
        },
      };
    case 'cursor':
      return {
        instructions: [
          'Navigate to Settings, then Cursor Settings',
          'Select MCP on the left',
          'Click Add new global MCP server at the top right',
          'Copy and paste the server config below, then save',
        ],
        code: {
          mcpServers: {
            EasyMCPeasy: {
              url: `${MCP_WORKER_BASE_URL}/${serverToken || PLACEHOLDER_TOKEN}`,
            },
          },
        },
      };
    case 'claude':
      return {
        instructions: [
          'Open the Claude Desktop app',
          'Go to Settings. It will open on the General tab on the left. Go to the Developer tab.',
          'Click Edit Config',
          'Open the claud_desktop_config.json file',
          'Copy and paste the server config to your existing file, then save',
        ],
        code: {
          mcpServers: {
            EasyMCPeasy: {
              command: 'npx',
              args: [
                '-y',
                'supergateway',
                '--sse',
                `${MCP_WORKER_BASE_URL}/${serverToken || PLACEHOLDER_TOKEN}`,
              ],
            },
          },
        },
      };
    case 'windsurf':
      return {
        instructions: [
          'Note: Last we checked there was no MCP available in non-paid Windsurf plans',
          'Open the Windsurf app',
          'Go to Windsurf Settings, then click Cascade',
          'Click view raw config to open the json file',
          'Copy and paste the server config below, then save',
        ],
        code: {
          mcpServers: {
            EasyMCPeasy: {
              command: 'npx',
              args: [
                '-y',
                'supergateway',
                '--sse',
                `${MCP_WORKER_BASE_URL}/${serverToken || PLACEHOLDER_TOKEN}`,
              ],
            },
          },
        },
      };
  }
};

function ClientTabContent({ activeTab }: ClientTabContentProps) {
  const { serverToken } = mcpeasyServerStore.useServerState();
  const [copied, setCopied] = useState(false);
  const [copiedEntry, setCopiedEntry] = useState(false);

  const content = getConfigContent(activeTab);

  return (
    <div className="space-y-4">
      <ConfigInstructions
        instructions={serverToken ? content.instructions : ['Login for setup instructions']}
      />
      <div className="relative">
        {serverToken && (
          <button
            onClick={() => {
              navigator.clipboard.writeText(
                JSON.stringify(getConfigContent(activeTab, serverToken).code, null, 2)
              );
              setCopied(true);
              setTimeout(() => setCopied(false), 1000);
            }}
            className="absolute align-center space-x- flex w-32 top-2 right-2 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            title="Copy configuration"
          >
            {copied ? <Check className="w-5 h-5 text-green-500" /> : <Copy className="w-5 h-5" />}
            &nbsp;[new file]
          </button>
        )}
        {serverToken && (
          <button
            onClick={() => {
              navigator.clipboard.writeText(
                JSON.stringify(getConfigContent(activeTab, serverToken).code.mcpServers, null, 2)
                  .slice(1, -2)
                  .concat(',')
              );
              setCopiedEntry(true);
              setTimeout(() => setCopiedEntry(false), 1000);
            }}
            className="absolute align-center flex w-32 top-12 right-2 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            title="Copy configuration"
          >
            {copiedEntry ? (
              <Check className="w-5 h-5 text-green-500" />
            ) : (
              <Copy className="w-5 h-5" />
            )}
            &nbsp;[entry only]
          </button>
        )}
        <ConfigCode code={serverToken ? content.code : ''} />
      </div>
    </div>
  );
}

export { ClientTabContent };
