import { useEffect, useState } from 'react';
import { Tab } from '@headlessui/react';
import clsx from 'clsx';
import { connectionsStore } from 'stores/connections';
import { connectProvider, deleteConnection, primeNango } from 'lib/nango'; // Added deleteConnection
import { getProviderConfig } from 'constants/providers';
import { CheckSquare, Loader2, Plus } from 'lucide-react';
import { GoogleOAuthNotice } from './GoogleOAuthNotice';
import { OverviewPanel } from './OverviewPanel';
import { ConfigurationPanel } from './ConfigurationPanel';

interface ConnectionProps {
  provider: string;
  showBackButton?: boolean;
  onBack?: () => void;
  onConnected?: () => void;
  onDisconnected?: () => void;
}

function Connection({ provider, showBackButton, onBack, onConnected }: ConnectionProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false); // Add state back
  const [showGoogleNotice, setShowGoogleNotice] = useState(false);
  const hasConnection = connectionsStore.useHasConnection(provider);
  const { connections } = connectionsStore.useState(); // Get connections for ID
  const connection = connections.find(c => c.providerKey === provider); // Find connection ID
  const config = getProviderConfig(provider);

  useEffect(() => {
    primeNango();
  }, []);

  // Monitor connection status changes
  useEffect(() => {
    if (hasConnection && isConnecting) {
      setIsConnecting(false);
      onConnected?.();
    }
  }, [hasConnection, isConnecting, onConnected]);

  if (!config) return null;

  const Icon = config.icon;
  const headerColor = config.headerColor || 'bg-indigo-600';
  const isGoogleProvider = provider.startsWith('google-');

  const handleConnect = async () => {
    if (isGoogleProvider) {
      setShowGoogleNotice(true);
    } else {
      proceedWithConnection();
    }
  };

  const proceedWithConnection = async () => {
    setIsConnecting(true);
    const [error] = await connectProvider(provider);
    if (error) {
      console.error('Failed to connect provider:', error);
      setIsConnecting(false);
    }
    onConnected?.();
  };

  // Add handler back
  const handleDisconnect = async () => {
    if (!connection) return;
    setIsDisconnecting(true);
    try {
      const [error] = await deleteConnection(connection.id, provider);
      if (error) {
        console.error('Failed to disconnect:', error);
      }
    } finally {
      setTimeout(() => {
        setIsDisconnecting(false);
        // onDisconnected?.(); // Removed to prevent auto-closing modal
      }, 600);
    }
  };

  return (
    // Ensure this main container is a flex column and fills height allowed by modal
    <div className="flex flex-col bg-white dark:bg-gray-800 pb-12 h-full">
      <GoogleOAuthNotice
        isOpen={showGoogleNotice}
        onClose={() => setShowGoogleNotice(false)}
        onAccept={() => {
          setShowGoogleNotice(false);
          proceedWithConnection();
        }}
      />
      {/* Header Banner */}
      <div className={clsx('relative h-24 rounded-t-lg', headerColor)}>
        <div className="absolute -bottom-3 left-6 flex items-center gap-4">
          <div
            className={clsx('p-4 bg-white rounded-lg shadow-md', {
              'dark:text-white dark:bg-black': config.invertInModal,
            })}
          >
            <Icon className="w-14 h-14" />
          </div>
          <div className="text-white -mt-6">
            <h2 className="text-2xl font-semibold">{config.name}</h2>
            <p className="text-sm text-white/80 mt-1">{config.title}</p>
          </div>
        </div>
        <ConnectButton
          hasConnection={hasConnection}
          isConnecting={isConnecting}
          handleConnect={handleConnect}
        />
      </div>
      {/* Content - Make this a flex column that takes remaining space */}
      <div className="mt-10 px-6 flex-1 flex flex-col">
        {' '}
        {/* Removed overflow-hidden */}
        {/* Make Tab.Group fill height */}
        <Tab.Group as="div" className="flex-1 flex flex-col">
          <Tab.List className="flex gap-6 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
            <Tab
              className={({ selected }) =>
                clsx(
                  'px-1 py-3 text-sm font-medium border-b-2 focus:outline-none',
                  selected
                    ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                )
              }
            >
              Overview
            </Tab>
            <Tab
              className={({ selected }) =>
                clsx(
                  'px-1 py-3 text-sm font-medium border-b-2 focus:outline-none',
                  selected
                    ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                )
              }
            >
              Configuration
            </Tab>
          </Tab.List>

          {/* Panels container should grow but NOT scroll itself */}
          <Tab.Panels className="flex-1 overflow-hidden">
            {' '}
            {/* Re-add overflow-hidden here to contain children */}
            {/* Tab.Panel should be a flex container, remove h-full */}
            <Tab.Panel className="flex flex-col">
              {' '}
              {/* Removed h-full */}
              <OverviewPanel config={config} />
            </Tab.Panel>
            <Tab.Panel>
              <ConfigurationPanel
                provider={provider}
                config={config}
                hasConnection={hasConnection}
                isConnecting={isConnecting}
                handleConnect={handleConnect}
                // Pass down disconnect state and handler
                isDisconnecting={isDisconnecting}
                handleDisconnect={handleDisconnect}
              />
            </Tab.Panel>{' '}
            {/* Removed h-full */}
          </Tab.Panels>
        </Tab.Group>
      </div>
      {showBackButton && (
        <div className="absolute bottom-1 -left-0.5 mt-auto px-6 py-4 border-gray-200 dark:border-gray-700">
          <button
            onClick={onBack}
            className="text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ← Back to connections
          </button>
        </div>
      )}
    </div>
  );
}

function ConnectButton({
  hasConnection,
  isConnecting,
  handleConnect,
}: {
  hasConnection: boolean;
  isConnecting: boolean;
  handleConnect: () => void;
  mobile?: boolean;
}) {
  return (
    <button
      onClick={hasConnection ? undefined : handleConnect}
      disabled={hasConnection || isConnecting}
      className={clsx(
        'absolute right-6 top-1/2 -translate-y-1/2 text-sm font-medium transition-colors sm:px-8 sm:py-3.5 px-3 py-3 sm:rounded-sm rounded-lg sm: ',
        {
          'bg-white/20 text-white cursor-default': hasConnection,
          'bg-white/80 text-gray-500 cursor-wait': isConnecting,
          'bg-indigo-600 hover:bg-indigo-500 text-gray-900 sm:bg-white sm:hover:bg-gray-50':
            !hasConnection && !isConnecting,
          'bg-white text-gray-900 hover:bg-gray-50': !hasConnection && !isConnecting,
        }
      )}
    >
      <span className="hidden sm:inline">
        {hasConnection ? 'Connected' : isConnecting ? 'Connecting...' : 'Connect'}
      </span>
      <span className="sm:hidden">
        {hasConnection ? (
          <CheckSquare className="w-5 h-5" />
        ) : isConnecting ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          <Plus className="w-5 h-5" />
        )}
      </span>
    </button>
  );
}

export { Connection };
