import clsx from 'clsx';

interface TabNavigationProps {
  activeTab: 'connections' | 'server';
  onTabChange: (tab: 'connections' | 'server') => void;
}

function TabNavigation({ activeTab, onTabChange }: TabNavigationProps) {
  return (
    <div className="flex w-full md:justify-end mb-6">
      <div className="flex w-full md:w-auto rounded-lg border border-gray-200 dark:border-gray-700">
        <button
          onClick={() => onTabChange('connections')}
          className={clsx(
            'w-1/2 md:w-auto text-center md:text-left px-4 py-2 text-sm font-medium rounded-l-lg transition-colors',
            activeTab === 'connections'
              ? 'bg-indigo-50 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400'
              : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
          )}
        >
          Connections
        </button>
        <button
          onClick={() => onTabChange('server')}
          className={clsx(
            'w-1/2 md:w-auto text-center md:text-left px-4 py-2 text-sm font-medium rounded-r-lg transition-colors border-l border-gray-200 dark:border-gray-700',
            activeTab === 'server'
              ? 'bg-indigo-50 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400'
              : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
          )}
        >
          Configure Server
        </button>
      </div>
    </div>
  );
}

export { TabNavigation };
