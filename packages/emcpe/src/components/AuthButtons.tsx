import { useAuth } from '../hooks/useAuth';
import { Github } from 'lucide-react';

function AuthButtons() {
  const { openLoginModal } = useAuth();

  return (
    <button
      onClick={openLoginModal}
      className="px-4 py-2 rounded-full text-sm font-medium bg-gray-800 hover:bg-gray-700 text-white flex items-center gap-2"
    >
      <Github size={16} />
      Sign in with GitHub
    </button>
  );
}

export { AuthButtons };
