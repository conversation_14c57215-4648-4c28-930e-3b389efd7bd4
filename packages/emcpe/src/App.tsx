import { IntlProvider } from 'react-intl';
import { Navbar } from './components/Navbar';
import { ConnectionsList } from './components/connections/ConnectionsList';
import { AuthModal } from './components/AuthModal';
import { Footer } from './components/Footer';
import './shimPopup';

function App() {
  return (
    <IntlProvider messages={{}} locale="en">
      <div className="min-h-screen flex flex-col bg-slate-50 dark:bg-gray-900 text-gray-800 dark:text-gray-100">
        <Navbar />
        <main className="flex-1 container mx-auto px-4 py-6 pb-12 mb-[200px]">
          <ConnectionsList />
        </main>
        <Footer />
      </div>
      <AuthModal />
    </IntlProvider>
  );
}

export default App;
