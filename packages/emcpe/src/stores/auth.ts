import { createStore } from './createStore';
import { supabase } from '../lib/supabase';
import { User } from '@supabase/supabase-js';

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  error: string | null;
  isModalOpen: boolean;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  error: null,
  isModalOpen: false,
};

class AuthStore {
  private static instance: AuthStore;
  private store;

  private constructor() {
    this.store = createStore<AuthState>(initialState);
    this.initializeAuth();
  }

  static getInstance(): AuthStore {
    if (!AuthStore.instance) {
      AuthStore.instance = new AuthStore();
    }
    return AuthStore.instance;
  }

  private async initializeAuth() {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (session?.user) {
      this.store.setState({
        isAuthenticated: true,
        user: session.user,
        error: null,
      });
    }

    supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        this.store.setState({
          isAuthenticated: true,
          user: session.user,
          error: null,
          isModalOpen: false,
        });
      } else if (event === 'SIGNED_OUT') {
        this.store.setState(initialState);
      }
    });
  }

  async login(email: string, password: string) {
    try {
      const { error } = await supabase.auth.signInWithPassword({ email, password });
      if (error) throw error;
    } catch (error) {
      this.store.setState({ error: 'Failed to log in' });
    }
  }

  async loginWithGitHub() {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'github',
        options: {
          redirectTo: window.location.origin,
        },
      });
      if (error) throw error;
    } catch (error) {
      this.store.setState({ error: 'Failed to log in with GitHub' });
    }
  }

  async register(email: string, password: string) {
    try {
      const { error } = await supabase.auth.signUp({ email, password });
      if (error) throw error;
    } catch (error) {
      this.store.setState({ error: 'Failed to sign up' });
    }
  }

  async logout() {
    await supabase.auth.signOut();
    this.store.setState(initialState);
  }

  openLoginModal() {
    this.store.setState({ isModalOpen: true, error: null });
  }

  closeAuthModal() {
    this.store.setState({ isModalOpen: false, error: null });
  }

  resetLoginForm() {
    this.store.setState({ error: null });
  }

  clearError() {
    this.store.setState({ error: null });
  }

  // These methods are kept for API compatibility but are not used with GitHub auth
  async verifyOtp(_otp: string) {
    // Not used with GitHub auth
  }

  async resetPassword(_email: string) {
    // Not used with GitHub auth
  }

  async setNewPassword(_password: string, _doLogin: boolean) {
    // Not used with GitHub auth
  }

  useAuthState() {
    return this.store.useStoreState();
  }
}

const authStore = AuthStore.getInstance();
export { authStore };
