import { createStore } from './createStore';
import { supabase } from '../lib/supabase';
// No longer need uuidv4 here, generation happens in the backend function

interface McpeasyServerPreferences {
  enablements?: Record<string, boolean>;
  // Add other preference fields here in the future
}

interface McpeasyServerState {
  serverToken: string | null; // This will hold the UNENCRYPTED token
  preferences: McpeasyServerPreferences | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: McpeasyServerState = {
  serverToken: null,
  preferences: null,
  isLoading: false,
  error: null,
};

const FUNCTION_NAME = 'emcpe-token';

class McpeasyServerStore {
  private static instance: McpeasyServerStore;
  private store;
  private subscription: any; // Keep track of auth subscription

  private constructor() {
    this.store = createStore<McpeasyServerState>(initialState);
    this.init();
  }

  static getInstance(): McpeasyServerStore {
    if (!McpeasyServerStore.instance) {
      McpeasyServerStore.instance = new McpeasyServerStore();
    }
    return McpeasyServerStore.instance;
  }

  private async init() {
    // Initialize when auth state changes
    this.subscription = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        // Delay slightly to ensure session is fully established?
        setTimeout(() => {
          // Fetch token only if not already loading and token isn't set
          if (!this.store.getState().serverToken && !this.store.getState().isLoading) {
            this.fetchOrCreateServerToken();
          }
        }, 100);
      } else if (event === 'SIGNED_OUT') {
        this.cleanup(); // Clear state on sign out
      }
    });

    // Check if user is already signed in on initial load
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (session?.user) {
      this.fetchOrCreateServerToken();
    }
  }

  // Calls the backend function to get or create the token
  async fetchOrCreateServerToken() {
    // No userId needed as input, function gets it from auth context
    this.store.setState({ isLoading: true, error: null });

    try {
      // Still call the function to get/create the token itself
      console.log(`Invoking ${FUNCTION_NAME} via GET to fetch/create token...`);
      const { data: functionData, error: functionError } = await supabase.functions.invoke<{
        serverToken: string;
      }>(FUNCTION_NAME, {
        method: 'GET',
      });

      if (functionError) {
        console.error(`Error invoking ${FUNCTION_NAME} (GET):`, functionError);
        throw new Error(functionError.message || 'Failed to fetch server token.');
      }

      if (!functionData?.serverToken) {
        console.error('Function returned success but no serverToken found in data:', functionData);
        throw new Error('Received invalid response from server token function.');
      }

      const serverToken = functionData.serverToken;
      console.log('Successfully fetched/created server token.');

      // Now, fetch preferences directly from the table
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error('User session not found, cannot fetch preferences.');
      }
      const userId = session.user.id;

      console.log(`Fetching preferences directly for user ${userId}...`);
      const { data: serverData, error: dbError } = await supabase
        .from('emcpe_servers')
        .select('preferences')
        .eq('userId', userId)
        .single();

      if (dbError) {
        // It might be okay if the record doesn't exist yet, but log other errors
        if (dbError.code !== 'PGRST116') {
          // PGRST116 = "The result contains 0 rows"
          console.error(`Error fetching preferences from DB for user ${userId}:`, dbError);
          // Decide if this should be a hard error or just log and continue with default prefs
          // For now, let's treat it as an error that prevents loading prefs
          throw new Error(dbError.message || 'Failed to fetch server preferences from database.');
        } else {
          console.log(`No existing preferences found for user ${userId}, using defaults.`);
        }
      }

      const preferences = serverData?.preferences as McpeasyServerPreferences | null;

      // Update store state with token and fetched/default preferences
      this.store.setState({
        serverToken: serverToken,
        preferences: preferences || { enablements: {} }, // Use fetched or default
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      console.error('Error in fetchOrCreateServerToken:', error);
      this.store.setState({
        serverToken: null,
        preferences: null, // Clear preferences on error too
        isLoading: false,
        error: error.message || 'An unknown error occurred while fetching the server token.',
      });
    }
  }

  // Updates the user's server preferences
  async updatePreferences(enablements: Record<string, boolean>) {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session?.user) {
      this.store.setState({
        ...this.store.getState(),
        error: 'You must be logged in to update preferences.',
      });
      return;
    }

    // Optimistically update UI state could be added here if preferences were stored
    // For now, just show loading/error for the update operation itself
    this.store.setState({ isLoading: true, error: null }); // Use isLoading for the update operation

    const userId = session.user.id;

    try {
      const nextPreferences = {
        ...(this.store.getState().preferences || {}),
        enablements: Object.entries({
          ...(this.store.getState().preferences?.enablements || {}),
          ...enablements,
        }).reduce(
          (acc, [key, value]) => {
            if (value !== true) {
              acc[key] = value;
            }
            return acc;
          },
          {} as Record<string, boolean>
        ),
      };

      const { error } = await supabase
        .from('emcpe_servers')
        .update({ preferences: nextPreferences }) // Update only the preferences field
        .eq('userId', userId); // Ensure we only update the correct user's record

      if (error) {
        console.error(`Error updating preferences in DB for user ${userId}:`, error);
        throw new Error(error.message || 'Failed to update server preferences in database.');
      }
      // Remove diagnostic delay
      // await new Promise(resolve => setTimeout(resolve, 10));
      this.store.setState({
        preferences: nextPreferences,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      console.error('Error in updatePreferences:', error);
      // Remove diagnostic delay
      // await new Promise(resolve => setTimeout(resolve, 10));
      this.store.setState({
        isLoading: false, // Reset loading state
        error: error.message || 'An error occurred while updating preferences.',
        // Note: No need to revert state if we didn't update it optimistically
      });
    }
  }

  // Calls the backend function to regenerate the token
  async regenerateServerToken() {
    // Check auth state locally first
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session?.user) {
      this.store.setState({
        ...this.store.getState(), // Keep existing state but add error
        error: 'You must be logged in to regenerate your server token.',
      });
      return; // Don't proceed if not logged in
    }

    this.store.setState({ isLoading: true, error: null });

    try {
      // Call the Supabase function via POST
      console.log(`Invoking ${FUNCTION_NAME} via POST to regenerate...`);
      const { data, error } = await supabase.functions.invoke(FUNCTION_NAME, {
        method: 'POST',
        // Body not needed for regenerate in this design
      });

      if (error) {
        console.error(`Error invoking ${FUNCTION_NAME} (POST):`, error);
        throw new Error(error.message || 'Failed to regenerate server token.');
      }

      if (data?.serverToken) {
        console.log('Successfully regenerated server token.');
        this.store.setState({
          serverToken: data.serverToken, // Store the new UNENCRYPTED token
          isLoading: false,
          error: null,
        });
        return data.serverToken; // Return the new unencrypted token
      } else {
        console.error(
          'Regenerate function returned success but no serverToken found in data:',
          data
        );
        throw new Error('Received invalid response from server token regeneration.');
      }
    } catch (error: any) {
      console.error('Error in regenerateServerToken:', error);
      this.store.setState({
        // Keep potentially existing token? Or clear it? Let's keep it but show error.
        isLoading: false,
        error: error.message || 'An error occurred while regenerating the server token.',
      });
      // Don't return anything on error
    }
  }

  // Cleans up state and subscriptions
  private cleanup() {
    // No supabase subscription to clean up here, but good practice
    // if (this.subscription) {
    //   this.subscription.unsubscribe();
    //   this.subscription = null;
    // }
    console.log('Cleaning up mcpeasyServerStore state.');
    this.store.setState(initialState); // Reset state to initial
  }

  // Selector specifically for preferences
  useServerPreferences() {
    return this.store.useStoreState(state => state.preferences);
  }

  // Hook to use the store's state
  useServerState() {
    return this.store.useStoreState();
  }
}

const mcpeasyServerStore = McpeasyServerStore.getInstance();
export { mcpeasyServerStore };
