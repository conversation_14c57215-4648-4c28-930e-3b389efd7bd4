import { createStore } from './createStore';
import { supabase } from '../lib/supabase';

export interface Connection {
  id: string;
  providerKey: string;
  userId: string;
}

export interface ConnectionsState {
  connections: Connection[];
  isLoading: boolean;
}

const initialState: ConnectionsState = {
  connections: [],
  isLoading: false,
};

class ConnectionsStore {
  private static instance: ConnectionsStore;
  private store;
  private subscription: any;

  private constructor() {
    this.store = createStore<ConnectionsState>(initialState);
    this.init();
  }

  static getInstance(): ConnectionsStore {
    if (!ConnectionsStore.instance) {
      ConnectionsStore.instance = new ConnectionsStore();
    }
    return ConnectionsStore.instance;
  }

  private async init() {
    // Initialize real-time subscription when auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        setTimeout(() => {
          if (!this.store.getState().connections.length) {
            this.loadConnections();
            this.setupSubscription(session.user.id);
          }
        }, 100);
      } else if (event === 'SIGNED_OUT') {
        this.cleanup();
      }
    });
  }

  public async loadConnections() {
    try {
      this.store.setState({ isLoading: true });
      const { data: connections, error } = await supabase.from('connections').select('*');

      if (error) throw error;

      this.store.setState({
        connections: connections || [],
        isLoading: false,
      });
    } catch (error) {
      console.error('Failed to load connections:', error);
      this.store.setState({ isLoading: false });
    }
  }

  private setupSubscription(userId: string) {
    this.cleanup();

    this.subscription = supabase
      .channel('connections-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'connections',
          filter: `userId=eq.${userId}`,
        },
        () => {
          this.loadConnections();
        }
      )
      .subscribe();
  }

  private cleanup() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.store.setState(initialState);
  }

  hasConnection(providerKey: string): boolean {
    const { connections } = this.store.getState();
    return connections.some(conn => conn.providerKey === providerKey);
  }

  useState() {
    return this.store.useStoreState();
  }

  useHasConnection(providerKey: string) {
    return this.store.useStoreState(state =>
      state.connections.some(conn => conn.providerKey === providerKey)
    );
  }
}

const connectionsStore = ConnectionsStore.getInstance();
export { connectionsStore };
