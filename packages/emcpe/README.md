# EasyMCPeasy (emcpe)

A React-based package for internal MakeAgent use that provides easy integration with Multi-provider Capability Providers (MCP), allowing seamless connection to various third-party services like Google Workspace, Twitter/X, LinkedIn, Slack, and more.

## Overview

EasyMCPeasy provides a streamlined interface for connecting to and utilizing various third-party services through a unified API. It simplifies the process of integrating with multiple providers and managing their capabilities within your application.

## Features

- **Multi-provider Support**: Integrate with Google Mail, Google Calendar, Google Drive, Google Docs, Google Sheets, Twitter/X, LinkedIn, Slack, GitHub, and more
- **Unified API**: Access all provider capabilities through a consistent interface
- **React Components**: Ready-to-use UI components for provider connections and interactions
- **TypeScript Support**: Fully typed API for better developer experience

## Installation

This package is for internal use only and is not published to npm.

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## Supported Providers

- **Google Workspace**
  - Gmail
  - Google Calendar
  - Google Drive
  - Google Docs
  - Google Sheets
- **Social Media**
  - Twitter/X
  - LinkedIn
- **Productivity**
  - Slack
  - Notion
- **Development**
  - GitHub

## Internal Use Only

This package is proprietary and for internal MakeAgent use only. Not for distribution.
