import { defineConfig } from 'astro/config';
import react from '@astrojs/react';
import tailwind from '@astrojs/tailwind';

export default defineConfig({
  integrations: [
    react(),
    tailwind({
      // Use the existing Tailwind config
      config: { path: './tailwind.config.js' }
    })
  ],
  // Set the output directory to not conflict with Vite
  outDir: './dist',
  // Configure the base path for Astro pages
  base: '/',
  // Configure the build to work alongside the existing React app
  build: {
    format: 'file'
  }
});
