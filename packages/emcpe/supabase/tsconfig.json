{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "lib": ["ESNext", "DOM", "deno.ns"], // Added deno.ns for Deno APIs
    "moduleResolution": "node", // Or "bundler" if using import maps extensively
    "esModuleInterop": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "allowImportingTsExtensions": true, // Deno uses TS extensions
    "noEmit": true, // Supabase CLI handles bundling/emitting
    "isolatedModules": true, // Good practice for Deno
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"] // Adjust if you have specific paths for functions
    }
    // Removed explicit "types" array to avoid potential conflicts
  },
  "include": ["functions/**/*", "../_shared/**/*"], // Include shared directory as well
  "exclude": ["node_modules"]
}
