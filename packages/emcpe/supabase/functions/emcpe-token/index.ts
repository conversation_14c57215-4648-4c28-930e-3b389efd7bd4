import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { corsHeaders } from '../_shared/cors.ts';
import { errorResponse } from '../_shared/error.ts';
import { initAuthenticate } from '../_shared/initAuth.ts';
import { hashData } from '../_shared/hashData.ts';
import { decryptData, encryptData } from '../_shared/crypto.ts';

const MCP_ENCRYPTION_KEY = Deno.env.get('MCP_ENCRYPTION_KEY');

if (!MCP_ENCRYPTION_KEY) {
  console.error('Missing environment variable: MCP_ENCRYPTION_KEY');
}

try {
  serve(async req => {
    console.log(`emcpe-token: Received request - Method: ${req.method}, URL: ${req.url}`);

    if (req.method === 'OPTIONS') {
      console.log('emcpe-token: Handling OPTIONS request.');
      return new Response('ok', { headers: corsHeaders });
    }

    if (!MCP_ENCRYPTION_KEY) {
      console.error('emcpe-token: MCP_ENCRYPTION_KEY missing at runtime.');
      return errorResponse(
        new Error('Server configuration error: Missing MCP_ENCRYPTION_KEY.'),
        500
      );
    }

    try {
      console.log('emcpe-token: Entering main try block.');
      const [authErrorResponse, user, supabaseAdmin] = await initAuthenticate(req);

      if (authErrorResponse) {
        console.log('emcpe-token: Authentication failed.');
        return authErrorResponse;
      }
      const userId = user.id;
      console.log(`emcpe-token: Authenticated user ${userId}`);

      if (req.method === 'GET' || req.method === 'POST') {
        console.log(`emcpe-token: Handling ${req.method} request.`);

        /*
          Look for existing token
        */
        const { data: existingServer, error: selectError } = await supabaseAdmin
          .from('emcpe_servers')
          .select('userId, tokenEncrypted')
          .eq('userId', userId)
          .single();

        if (selectError && selectError.code !== 'P0002') {
          console.error(`Failed to look for server token for user ${userId}:`, selectError);
        }

        /*
          Just return the existing token if it exists
        */
        if (req.method === 'GET' && existingServer && existingServer.tokenEncrypted) {
          const decryptedToken = await decryptData(
            existingServer.tokenEncrypted,
            MCP_ENCRYPTION_KEY
          );
          return new Response(JSON.stringify({ serverToken: decryptedToken }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200,
          });
        }

        console.log(`emcpe-token: Generating new token and hash for user ${userId}`);
        const newUnencryptedToken = crypto.randomUUID();
        const tokenHash = await hashData(newUnencryptedToken);
        const tokenEncrypted = await encryptData(newUnencryptedToken, MCP_ENCRYPTION_KEY);

        console.log(`emcpe-token: Inserting new record for user ${userId}`);

        if (existingServer && existingServer.tokenEncrypted) {
          console.log(`emcpe-token: Updating existing record for user ${userId}`);
          const { error: updateError } = await supabaseAdmin
            .from('emcpe_servers')
            .update({ tokenEncrypted: tokenEncrypted, tokenHash: tokenHash })
            .eq('userId', userId);

          if (updateError) {
            console.error(`Failed to update token hash for user ${userId}:`, updateError);
            throw updateError;
          }
          console.log(`emcpe-token: Successfully updated record for user ${userId}`);
        } else {
          const { error: insertError } = await supabaseAdmin.from('emcpe_servers').insert({
            userId: userId,
            tokenEncrypted: tokenEncrypted,
            tokenHash: tokenHash,
          });

          if (insertError) {
            console.error(`Failed to insert token hash for user ${userId}:`, insertError);
            throw insertError;
          }
          console.log(`emcpe-token: Successfully inserted record for user ${userId}`);
        }

        console.log(`emcpe-token: Returning new token for user ${userId}`);
        return new Response(JSON.stringify({ serverToken: newUnencryptedToken }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        });
      } else {
        console.log(`emcpe-token: Method ${req.method} not allowed.`);
        return errorResponse(new Error(`Method ${req.method} not allowed. Use GET or POST.`), 405);
      }
    } catch (error) {
      console.error('emcpe-token: Error within request handler:', error);
      const message = error instanceof Error ? error.message : 'Internal Server Error';
      return errorResponse(new Error(message), 500);
    }
  });
  console.log('emcpe-token: serve() called successfully.');
} catch (serveCallError) {
  console.error('FATAL: Error occurred when calling serve():', serveCallError);
}
