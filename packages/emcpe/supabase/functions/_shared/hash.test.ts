import { assertEquals, assertNotEquals } from 'https://deno.land/std@0.177.0/testing/asserts.ts';
import { hashData } from './hashData.ts'; // Import hashData from its new file

// --- Hashing Tests ---

Deno.test('hashData should produce known SHA-256 hash', async () => {
  const input = 'hello world';
  const expectedHash = 'b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9';
  const actualHash = await hashData(input);
  assertEquals(actualHash, expectedHash, 'SHA-256 hash does not match known value');
});

Deno.test('hashData should produce different hashes for different inputs', async () => {
  const input1 = 'input string 1';
  const input2 = 'input string 2';
  const hash1 = await hashData(input1);
  const hash2 = await hashData(input2);
  assertNotEquals(hash1, hash2, 'Different inputs should produce different hashes');
});

Deno.test('hashData should produce the same hash for the same input', async () => {
  const input = 'consistent input';
  const hash1 = await hashData(input);
  const hash2 = await hashData(input);
  assertEquals(hash1, hash2, 'Same input should produce the same hash');
});

Deno.test('hashData should produce expected hash for specific UUID', async () => {
  const inputUuid = 'f5d2cdb4-3e04-4dcd-9056-e5d7903df201';
  const expectedHash = 'ca099e6e7dea18825c90c2e7139bf9489a7b44723ca9a33b2c7b6ba0f797e809';
  const actualHash = await hashData(inputUuid);
  assertEquals(actualHash, expectedHash, 'Hash for specific UUID does not match expected value');
});
