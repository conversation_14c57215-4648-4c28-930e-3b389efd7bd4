import {
  assert,
  assertEquals,
  assertRejects,
} from 'https://deno.land/std@0.177.0/testing/asserts.ts';
import { decryptData, encryptData } from './crypto.ts'; // Import from new file

Deno.test('[Cryptography] encryptData and decryptData should round-trip correctly', async () => {
  const originalData = 'This is my secret data! 🤫';
  const testSecret = 'test-secret-key-needs-to-be-long-enough-for-pbkdf2'; // Ensure 32+ chars

  const encryptedBase64Url = await encryptData(originalData, testSecret);
  assert(typeof encryptedBase64Url === 'string', 'Encrypted data should be a string');
  assert(encryptedBase64Url.length > 0, 'Encrypted string should not be empty');
  assert(
    !encryptedBase64Url.includes('+') && !encryptedBase64Url.includes('/'),
    'Encrypted string should be base64url safe'
  );

  const decryptedData = await decryptData(encryptedBase64Url, testSecret);
  assertEquals(decryptedData, originalData, 'Decrypted data should match original data');
});

Deno.test('[Cryptography] decryptData should throw with wrong secret', async () => {
  const originalData = 'Another piece of data';
  const correctSecret = 'correct-secret-key-must-be-at-least-32-characters';
  const wrongSecret = 'incorrect-secret-key-must-be-at-least-32-character'; // Different

  const encryptedBase64Url = await encryptData(originalData, correctSecret);

  await assertRejects(
    async () => {
      await decryptData(encryptedBase64Url, wrongSecret);
    },
    Error,
    'Failed to decrypt data.'
  );
});

Deno.test(
  '[Cryptography] decryptData should decrypt known ciphertext with known secret',
  async () => {
    // Using the previously provided known values
    const knownEncrypted =
      'sy_DbK4QOoHIb9c8CNQizkxKfbCFJ5JKkfDVjKNsSm1BVGilr4SYXcyZNLFo7PSQEW3PA3DxFX36Vifdz2ExOg';
    const knownSecret = 'pEpB<PS8MC({n_5<e!kq,cHmQbnP%u;T)0a,;&js\\Q{0a_'; // Escaped backslash
    const expectedDecrypted = '2774e6b8-c6bf-474a-8b21-170ac6ff237a';

    const decryptedData = await decryptData(knownEncrypted, knownSecret);
    assertEquals(decryptedData, expectedDecrypted, 'Decryption of known ciphertext failed');
  }
);

Deno.test('[Cryptography] decryptData should throw with corrupted data', async () => {
  const originalData = 'Data to be corrupted';
  const secret = 'a-valid-secret-key-that-is-long-enough-for-sure'; // Ensure 32+ chars

  const encryptedBase64Url = await encryptData(originalData, secret);

  // Corrupt the data slightly (e.g., change a character)
  const corruptedData = encryptedBase64Url.slice(0, -5) + 'XXXXX';

  await assertRejects(
    async () => {
      await decryptData(corruptedData, secret);
    },
    Error,
    'Failed to decrypt data.'
  );
});
