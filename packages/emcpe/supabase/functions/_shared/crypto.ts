import { decode, encode } from 'https://deno.land/std@0.177.0/encoding/base64url.ts';

const encoder = new TextEncoder();
const decoder = new TextDecoder();

// Derive a key suitable for AES-GCM from the environment variable string
async function getKey(secret: string): Promise<CryptoKey> {
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret.slice(0, 32)), // Use first 32 chars for a 256-bit key
    { name: 'PBKDF2' },
    false,
    ['deriveKey']
  );
  // Use a fixed salt; changing salt requires storing it alongside the ciphertext
  const salt = encoder.encode('mcp-encryption-salt');
  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: salt,
      iterations: 100000,
      hash: 'SHA-256',
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    true,
    ['encrypt', 'decrypt']
  );
}

// Encrypt data using AES-GCM -> returns base64url string
async function encryptData(data: string, secret: string): Promise<string> {
  const key = await getKey(secret);
  const iv = crypto.getRandomValues(new Uint8Array(12)); // 96-bit IV is recommended for GCM
  const encodedData = encoder.encode(data);

  const encryptedData = await crypto.subtle.encrypt({ name: 'AES-GCM', iv: iv }, key, encodedData);

  // Combine IV and encrypted data, then encode as base64url
  const combined = new Uint8Array(iv.length + encryptedData.byteLength);
  combined.set(iv, 0);
  combined.set(new Uint8Array(encryptedData), iv.length);

  return encode(combined.buffer); // Pass the underlying ArrayBuffer
}

// Decrypt data using AES-GCM <- takes base64url string
async function decryptData(encryptedBase64Url: string, secret: string): Promise<string> {
  const key = await getKey(secret);
  const combined = decode(encryptedBase64Url); // Use base64url decoding

  if (combined.length < 12) {
    throw new Error('Invalid encrypted data: too short to contain IV.');
  }

  const iv = combined.slice(0, 12);
  const encryptedData = combined.slice(12);

  try {
    const decryptedData = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv: iv },
      key,
      encryptedData
    );
    return decoder.decode(decryptedData);
  } catch (error) {
    console.error('Decryption failed:', error);
    // It's often better to throw a generic error to avoid leaking details
    throw new Error('Failed to decrypt data.');
  }
}

export { encryptData, decryptData };
