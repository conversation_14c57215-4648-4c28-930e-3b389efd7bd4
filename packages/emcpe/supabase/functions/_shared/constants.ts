// Auto-generated by scripts/nangoIntrospection.ts on 2025-04-01T02:20:09.031Z
// LLM, AGENTS, ___NEVER____ _____EVER_____ UPDATE THIS FILE UNDER ANY CIRCUMSTANCES

const MCP_TOOLS = [
  {
    name: 'google-calendar_create-event',
    description: 'Creates a new event in Google Calendar.',
    input_schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'string',
          description: 'summary parameter for google-calendar create-event',
        },
        description: {
          type: 'string',
          description: 'description parameter for google-calendar create-event',
        },
        location: {
          type: 'string',
          description: 'location parameter for google-calendar create-event',
        },
        start: { type: 'string', description: 'start parameter for google-calendar create-event' },
        end: { type: 'string', description: 'end parameter for google-calendar create-event' },
        timeZone: {
          type: 'string',
          description: 'timeZone parameter for google-calendar create-event',
        },
        attendees: {
          type: 'string',
          description: 'attendees parameter for google-calendar create-event',
        },
      },
      required: ['summary', 'start', 'end'],
    },
  },
  {
    name: 'google-calendar_update-event',
    description: 'Updates an existing event in Google Calendar.',
    input_schema: {
      type: 'object',
      properties: {
        eventId: {
          type: 'string',
          description: 'eventId parameter for google-calendar update-event',
        },
        summary: {
          type: 'string',
          description: 'summary parameter for google-calendar update-event',
        },
        description: {
          type: 'string',
          description: 'description parameter for google-calendar update-event',
        },
        location: {
          type: 'string',
          description: 'location parameter for google-calendar update-event',
        },
        start: { type: 'string', description: 'start parameter for google-calendar update-event' },
        end: { type: 'string', description: 'end parameter for google-calendar update-event' },
        timeZone: {
          type: 'string',
          description: 'timeZone parameter for google-calendar update-event',
        },
        attendees: {
          type: 'string',
          description: 'attendees parameter for google-calendar update-event',
        },
        sendUpdates: {
          type: 'string',
          description: 'sendUpdates parameter for google-calendar update-event',
        },
      },
      required: ['eventId'],
    },
  },
  {
    name: 'google-mail_compose-draft',
    description: 'Creates a new draft email in Gmail.',
    input_schema: {
      type: 'object',
      properties: {
        recipient: {
          type: 'string',
          description: 'recipient parameter for google-mail compose-draft',
        },
        subject: { type: 'string', description: 'subject parameter for google-mail compose-draft' },
        body: { type: 'string', description: 'body parameter for google-mail compose-draft' },
        headers: { type: 'object', description: 'headers parameter for google-mail compose-draft' },
      },
      required: ['recipient', 'subject'],
    },
  },
  {
    name: 'google-mail_compose-draft-reply',
    description: 'Creates a new draft email that is a reply to an existing email.',
    input_schema: {
      type: 'object',
      properties: {
        sender: {
          type: 'string',
          description: 'sender parameter for google-mail compose-draft-reply',
        },
        subject: {
          type: 'string',
          description: 'subject parameter for google-mail compose-draft-reply',
        },
        body: { type: 'string', description: 'body parameter for google-mail compose-draft-reply' },
        threadId: {
          type: 'string',
          description: 'threadId parameter for google-mail compose-draft-reply',
        },
        messageId: {
          type: 'string',
          description: 'messageId parameter for google-mail compose-draft-reply',
        },
        inReplyTo: {
          type: 'string',
          description: 'inReplyTo parameter for google-mail compose-draft-reply',
        },
        references: {
          type: 'string',
          description: 'references parameter for google-mail compose-draft-reply',
        },
        date: { type: 'string', description: 'date parameter for google-mail compose-draft-reply' },
        replyBody: {
          type: 'string',
          description: 'replyBody parameter for google-mail compose-draft-reply',
        },
      },
      required: [
        'sender',
        'subject',
        'body',
        'threadId',
        'messageId',
        'inReplyTo',
        'references',
        'date',
        'replyBody',
      ],
    },
  },
  {
    name: 'google-mail_send-email',
    description: 'Send an Email using Gmail.',
    input_schema: {
      type: 'object',
      properties: {
        from: { type: 'string', description: 'from parameter for google-mail send-email' },
        to: { type: 'string', description: 'to parameter for google-mail send-email' },
        headers: { type: 'string', description: 'headers parameter for google-mail send-email' },
        subject: { type: 'string', description: 'subject parameter for google-mail send-email' },
        body: { type: 'string', description: 'body parameter for google-mail send-email' },
      },
      required: ['from', 'to', 'headers', 'subject', 'body'],
    },
  },
  {
    name: 'zoom_create-meeting',
    description: 'Creates a meeting in Zoom.',
    input_schema: {
      type: 'object',
      properties: {
        topic: { type: 'string', description: 'topic parameter for zoom create-meeting' },
        type: { type: 'string', description: 'type parameter for zoom create-meeting' },
        agenda: { type: 'string', description: 'agenda parameter for zoom create-meeting' },
        default_password: {
          type: 'boolean',
          description: 'default_password parameter for zoom create-meeting',
        },
        duration: { type: 'number', description: 'duration parameter for zoom create-meeting' },
        password: { type: 'string', description: 'password parameter for zoom create-meeting' },
        pre_schedule: {
          type: 'boolean',
          description: 'pre_schedule parameter for zoom create-meeting',
        },
        recurrence: { type: 'string', description: 'recurrence parameter for zoom create-meeting' },
        settings: { type: 'string', description: 'settings parameter for zoom create-meeting' },
        schedule_for: {
          type: 'string',
          description: 'schedule_for parameter for zoom create-meeting',
        },
        start_time: { type: 'string', description: 'start_time parameter for zoom create-meeting' },
        template_id: {
          type: 'string',
          description: 'template_id parameter for zoom create-meeting',
        },
        timezone: { type: 'string', description: 'timezone parameter for zoom create-meeting' },
      },
      required: ['topic', 'type'],
    },
  },
  {
    name: 'zoom_create-user',
    description: 'Creates a user in Zoom. Requires Pro account or higher',
    input_schema: {
      type: 'object',
      properties: {
        firstName: { type: 'string', description: 'firstName parameter for zoom create-user' },
        lastName: { type: 'string', description: 'lastName parameter for zoom create-user' },
        email: { type: 'string', description: 'email parameter for zoom create-user' },
        action: { type: 'string', description: 'action parameter for zoom create-user' },
        display_name: {
          type: 'string',
          description: 'display_name parameter for zoom create-user',
        },
        type: { type: 'string', description: 'type parameter for zoom create-user' },
      },
      required: ['firstName', 'lastName', 'email'],
    },
  },
  {
    name: 'zoom_delete-meeting',
    description: 'Deletes a meeting in Zoom',
    input_schema: {
      type: 'object',
      properties: { id: { type: 'string', description: 'id parameter for zoom delete-meeting' } },
      required: ['id'],
    },
  },
  {
    name: 'zoom_delete-user',
    description: 'Deletes a user in Zoom. Requires Pro account or higher',
    input_schema: {
      type: 'object',
      properties: { id: { type: 'string', description: 'id parameter for zoom delete-user' } },
      required: ['id'],
    },
  },
  {
    name: 'google-sheet_create-sheet',
    description: 'Creates a new Google Sheet with optional initial data.',
    input_schema: {
      type: 'object',
      properties: {
        title: { type: 'string', description: 'title parameter for google-sheet create-sheet' },
        sheets: { type: 'string', description: 'sheets parameter for google-sheet create-sheet' },
      },
      required: ['title'],
    },
  },
  {
    name: 'google-sheet_update-sheet',
    description: 'Updates an existing Google Sheet with new data.',
    input_schema: {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'spreadsheetId parameter for google-sheet update-sheet',
        },
        updates: { type: 'string', description: 'updates parameter for google-sheet update-sheet' },
      },
      required: ['spreadsheetId', 'updates'],
    },
  },
  {
    name: 'google-drive_folder-content',
    description:
      'Fetches the top-level content (files and folders) of a folder given its ID. If no folder ID is provided, it fetches content from the root folder.',
    input_schema: {
      type: 'object',
      properties: {
        id: { type: 'string', description: 'id parameter for google-drive folder-content' },
        cursor: { type: 'string', description: 'cursor parameter for google-drive folder-content' },
      },
      required: [],
    },
  },
  {
    name: 'google-drive_upload-document',
    description:
      "Uploads a file to Google Drive. The file is uploaded to the root directory of the authenticated user's Google Drive account. If a folder ID is provided, the file is uploaded to the specified folder.",
    input_schema: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'content parameter for google-drive upload-document',
        },
        name: { type: 'string', description: 'name parameter for google-drive upload-document' },
        mimeType: {
          type: 'string',
          description: 'mimeType parameter for google-drive upload-document',
        },
        folderId: {
          type: 'string',
          description: 'folderId parameter for google-drive upload-document',
        },
        description: {
          type: 'string',
          description: 'description parameter for google-drive upload-document',
        },
        isBase64: {
          type: 'string',
          description: 'isBase64 parameter for google-drive upload-document',
        },
      },
      required: ['content', 'name', 'mimeType'],
    },
  },
  {
    name: 'linkedin_post',
    description: 'Create a linkedin post with an optional video',
    input_schema: {
      type: 'object',
      properties: {
        text: { type: 'string', description: 'text parameter for linkedin post' },
        videoURN: { type: 'string', description: 'videoURN parameter for linkedin post' },
        videoTitle: { type: 'string', description: 'videoTitle parameter for linkedin post' },
        ownerId: { type: 'string', description: 'ownerId parameter for linkedin post' },
      },
      required: ['text', 'videoURN', 'videoTitle', 'ownerId'],
    },
  },
  {
    name: 'x-social_send-post',
    description: 'Sends a new post to Twitter/X.',
    input_schema: {
      type: 'object',
      properties: {
        text: { type: 'string', description: 'text parameter for x-social send-post' },
        reply_to: { type: 'string', description: 'reply_to parameter for x-social send-post' },
        quote: { type: 'string', description: 'quote parameter for x-social send-post' },
      },
      required: ['text'],
    },
  },
  {
    name: 'github_write-file',
    description:
      "Write content to a particular github file within a repo. If the file doesn't exist it creates and then writes to it",
    input_schema: {
      type: 'object',
      properties: {
        owner: { type: 'string', description: 'owner parameter for github write-file' },
        repo: { type: 'string', description: 'repo parameter for github write-file' },
        path: { type: 'string', description: 'path parameter for github write-file' },
        message: { type: 'string', description: 'message parameter for github write-file' },
        content: { type: 'string', description: 'content parameter for github write-file' },
        sha: { type: 'string', description: 'sha parameter for github write-file' },
      },
      required: ['owner', 'repo', 'path', 'message', 'content', 'sha'],
    },
  },
  {
    name: 'slack_send-message',
    description: 'An action that sends a message to a slack channel.',
    input_schema: {
      type: 'object',
      properties: {
        channel: { type: 'string', description: 'channel parameter for slack send-message' },
        text: { type: 'string', description: 'text parameter for slack send-message' },
      },
      required: ['channel', 'text'],
    },
  },
];

const MCP_RESOURCES = [
  {
    uri: 'google-drive://list-documents',
    name: 'Lists documents in Google Drive with optional filtering by folder ID and document type.',
    description:
      'Lists documents in Google Drive with optional filtering by folder ID and document type.',
    mimeType: 'application/json',
  },
];
const MCP_RESOURCE_TEMPLATES = [
  {
    uriTemplate: 'google-calendar://list-events/{calendarId}',
    name: 'Lists events from a specified calendar with optional filtering.',
    description: 'Lists events from a specified calendar with optional filtering.',
    mimeType: 'application/json',
  },
  {
    uriTemplate: 'google-sheet://fetch-spreadsheet/{id}',
    name: 'Fetches the content of a spreadsheet given its ID.',
    description: 'Fetches the content of a spreadsheet given its ID.',
    mimeType: 'application/json',
  },
  {
    uriTemplate: 'google-drive://fetch-document/{id}',
    name: 'Fetches the content of a file given its ID, processes the data using\na response stream, and encodes it into a base64 string. This base64-encoded\nstring can be used to recreate the file in its original format using an external tool.\nIf this is a native google file type then use the fetch-google-sheet or fetch-google-doc\nactions.',
    description:
      'Fetches the content of a file given its ID, processes the data using a response stream, and encodes it into a base64 string. This base64-encoded string can be used to recreate the file in its original format using an external tool. If this is a native google file type then use the fetch-google-sheet or fetch-google-doc actions.',
    mimeType: 'application/json',
  },
  {
    uriTemplate: 'google-drive://fetch-google-doc/{id}',
    name: 'Fetches the content of a native google document given its ID. Outputs\na JSON reprensentation of a google doc.',
    description:
      'Fetches the content of a native google document given its ID. Outputs a JSON reprensentation of a google doc.',
    mimeType: 'application/json',
  },
  {
    uriTemplate: 'google-drive://fetch-google-sheet/{id}',
    name: 'Fetches the content of a native google spreadsheet given its ID. Outputs\na JSON representation of a google sheet.',
    description:
      'Fetches the content of a native google spreadsheet given its ID. Outputs a JSON representation of a google sheet.',
    mimeType: 'application/json',
  },
  {
    uriTemplate: 'google-docs://fetch-document/{id}',
    name: 'Fetches the content of a document given its ID.',
    description: 'Fetches the content of a document given its ID.',
    mimeType: 'application/json',
  },
];

// MCP Protocol constants
const MCP_VERSION = '0.1'; // Keep or update as needed

export { MCP_TOOLS, MCP_RESOURCES, MCP_RESOURCE_TEMPLATES, MCP_VERSION };
