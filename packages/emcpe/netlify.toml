# Netlify configuration for emcpe package

[build]
ignore = "git diff --quiet $CACHED_COMMIT_REF $COMMIT_REF packages/emcpe-client/"
command = "pnpm --filter @makeagent/emcpe-client run build"
publish = "packages/emcpe/dist"

[context.production.environment]
SITE_NAME = "easymcpeasy"

[dev]
framework = "astro"
targetPort = 4321
port = 8988
publish = "dist"
command = "pnpm --filter @makeagent/emcpe-client run dev"
autoLaunch = true

# Branch deploys configuration
[context.branch-deploy]
command = "pnpm --filter @makeagent/emcpe-client run build"

# Deploy preview configuration
[context.deploy-preview]
command = "pnpm --filter @makeagent/emcpe-client run build"
