{"name": "@makeagent/emcpe-client", "version": "0.1.0", "description": "React-based package for easy MCP integration", "type": "module", "scripts": {"dev": "astro dev", "build": "astro build", "dev:astro": "astro dev", "astro": "astro", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "supabase:serve": "supabase functions serve --no-verify-jwt --project-ref $(cat supabase/.temp/project-ref)", "supabase:deploy": "supabase functions deploy --no-verify-jwt --project-ref $(cat supabase/.temp/project-ref)", "test:mcp": "deno run --allow-net src/test.ts"}, "dependencies": {"@astrojs/react": "^4.2.3", "@astrojs/tailwind": "^6.0.2", "@floating-ui/react": "~0.27.5", "@headlessui/react": "^2.2.0", "@nangohq/frontend": "^0.56.4", "@supabase/supabase-js": "^2.39.7", "astro": "^5.5.6", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-react": "0.482.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intl": "^7.1.6", "react-router-dom": "^7.4.1", "uuid": "^9.0.1", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/deno": "^2.2.0", "@types/lodash": "^4.14.202", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "supabase": "^2.19.7", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5.5.3"}}