{"name": "@makeagent/ma-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "netlify": "netlify dev --no-open", "build": "next build", "start": "next start", "lint": "next lint", "supabase:serve": "cd supabase && supabase functions serve", "test": "jest"}, "dependencies": {"@ai-sdk/openai": "latest", "@ai-sdk/react": "^1.2.8", "@ai-sdk/xai": "^1.2.16", "@floating-ui/react": "^0.27.7", "@headlessui/react": "^2.2.1", "@n8n/tournament": "^1.0.6", "@nangohq/frontend": "^0.58.5", "@nangohq/node": "latest", "@supabase/supabase-js": "latest", "ai": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "json-logic-js": "^2.0.5", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.487.0", "next": "15.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intl": "^7.1.10", "react-markdown": "^10.1.0", "supabase": "^2.20.12", "tailwind-merge": "^3.2.0", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@netlify/functions": "^3.1.8", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/jest": "^29.5.14", "@types/json-logic-js": "^2.0.8", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.7.0", "postcss": "^8.4.35", "tailwindcss": "^4", "ts-jest": "^29.3.4", "typescript": "^5"}}