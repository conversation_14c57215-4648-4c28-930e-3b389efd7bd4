const ERROR_HEADERS = {
  'Content-Type': 'application/json',
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

export { ERROR_HEADERS };

// Standard error response helper
function errorResponse(error: unknown, status = 500): Response {
  console.error('Error:', error);

  return new Response(
    JSON.stringify({
      success: false,
      error: 'An error occurred',
      details: error instanceof Error ? error.message : 'Unknown error',
    }),
    {
      status,
      headers: ERROR_HEADERS,
    }
  );
}

// Validation error helper
function validationError(message: string): Response {
  return new Response(JSON.stringify({ error: message }), {
    status: 400,
    headers: ERROR_HEADERS,
  });
}

function authorizationError(): Response {
  return new Response(JSON.stringify({ error: 'Unauthorized' }), {
    status: 401,
    headers: ERROR_HEADERS,
  });
}

export { errorResponse, validationError, authorizationError };
