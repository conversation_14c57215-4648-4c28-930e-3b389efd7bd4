// AUTOGENERATED: copied here by scripts/shareTypes.ts, update src/types/index.ts and re-run if types need changing

interface WorkflowNode {
  id: string;
  type: NodeType | string;
  providerKey?: string;
  syncKey?: string;
  model?: string;
  actionKey?: string;
}

interface AgentConfig {
  steps: SetupStepType[];
  workflow: { nodes: WorkflowNode[] };
  userParameters?: { _llmSystemPrompt?: string } & Record<string, unknown>;
  condition?: object;
  isSetupOnly?: boolean;
}

type Agent = {
  id: string;
  config: AgentConfig;
  active: boolean;
  triggers?: Trigger[];
  isTesting?: boolean;
  testStatus?: TestStatus;
};

type Trigger = {
  id: string;
};

interface Step {
  id: string;
  text: string;
  completed: boolean;
  skippable?: boolean;
}

interface Example {
  input: string;
  output: string;
}

type TestStatus = 'idle' | 'simulating' | 'working' | 'success' | 'failed';
type TabType = 'brief' | 'examples';
type NodeStatus =
  | 'idle'
  | 'simulating'
  | 'processing'
  | 'success'
  | 'failed'
  | 'active'
  | 'inactive'
  | 'RUNNING'
  | 'PAUSED'
  | 'SUCCESS'
  | 'ERROR';

type NodeType = 'trigger' | 'action' | 'ai' | 'scheduler' | 'weather' | 'email' | 'notification';

// Response types
type ResponseType = 'connectProvider' | 'fineTune' | 'userLocation';

interface SetupStepType {
  type: ResponseType | 'auth';
  completed?: boolean;
  skippable?: boolean;
  data?: StepData;
  provider?: string;
  advanced?: boolean;
}

interface StepData {
  text?: string;
  brief?: string;
  examples?: Example[];
  latitude?: number;
  longitude?: number;
}

interface WorkflowConfig {
  triggerType: 'manual' | 'scheduled' | 'event';
  nodes: WorkflowNode[];
  connections: NodeConnection[];
}

interface NodeConnection {
  source: string;
  target: string;
}

interface Conversation {
  id: string;
  title: string | null;
  createdAt: string;
  updatedAt: string;
  userId: string;
  currentTaskflowId: string | null;
  agent: Agent | null;
}

export type {
  Agent,
  AgentConfig,
  Conversation,
  Example,
  NodeConnection,
  NodeStatus,
  NodeType,
  ResponseType,
  SetupStepType,
  Step,
  StepData,
  TabType,
  TestStatus,
  WorkflowConfig,
  WorkflowNode,
};
