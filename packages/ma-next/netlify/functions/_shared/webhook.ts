async function callWebhook<T>(
  webhookUrl: string | undefined,
  payload: Record<string, number | string | boolean | null>
): Promise<T | null> {
  if (!webhookUrl) {
    console.warn('Webhook URL is not configured');
    return null;
  }

  const fullPayload = {
    ...payload,
    timestamp: new Date().toISOString(),
  };

  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(fullPayload),
    });

    if (!response.ok) {
      console.error(`Webhook call ${webhookUrl} failed: ${response.status} ${response.statusText}`);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Webhook call error:', error);
    return null;
  }
}

export { callWebhook };
