/**
 * Utility function to get the DEBUG mode from environment variables
 * If the DEBUG environment variable is not set, defaults to true
 *
 * @returns boolean indicating if DEBUG mode is enabled
 */
function getDebugMode(): boolean {
  const debugEnv = process.env.DEBUG;

  // If DEBUG env var is not set, default to true
  if (debugEnv === undefined) {
    return true;
  }

  // Convert string to boolean
  return debugEnv.toLowerCase() === 'true';
}

const DEBUG = getDebugMode();

function debug(...args: any[]) {
  if (DEBUG) {
    console.log(...args);
  }
}

export { getDebugMode, debug };
