import { User } from '@supabase/supabase-js';
import { ERROR_HEADERS } from './error';
import { initServiceRoleSupabase, initUserRoleSupabase } from './supabase';

async function initAuthenticate(
  req: Request
): Promise<
  | [Response]
  | [
      null,
      User,
      ReturnType<typeof initServiceRoleSupabase>,
      ReturnType<typeof initUserRoleSupabase>,
    ]
> {
  // Extract the Authorization header
  const authHeader = req.headers.get('Authorization');
  if (!authHeader) {
    return [
      new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: ERROR_HEADERS,
      }),
    ];
  }

  const token = authHeader.replace('Bearer ', '');

  const supabase = initServiceRoleSupabase();
  const supabaseUser = initUserRoleSupabase(token);

  // Use the JWT from the header to get the authenticated user
  const {
    data: { user },
    error,
  } = await supabaseUser.auth.getUser(authHeader.replace('Bearer ', ''));

  if (error || !user) {
    return [
      new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: ERROR_HEADERS,
      }),
    ];
  }

  return [null, user, supabase, supabaseUser] as const;
}

export { initAuthenticate };
