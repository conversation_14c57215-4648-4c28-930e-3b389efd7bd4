const corsHeaders = {
  // FIXME: update to makeagent.com in prod.
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Content-Type': 'application/json',
};

// Handle CORS preflight requests
function handleCors(req: Request, methods = ['POST']): Response | null {
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        ...corsHeaders,
        'Access-Control-Allow-Methods': `${methods.join(', ')}, OPTIONS`,
      },
    });
  }
  return null;
}

export { corsHeaders, handleCors };
