import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { getRunner } from '../_tools/actions';
import { getPseudoNangoAction } from '../_nango/getPseudoNangoAction';

interface PerformActionRequest {
  providerKey: string;
  actionKey: string;
  actionParameters?: Record<string, any>;
}

export default async function handler(req: Request) {
  const cors = handleCors(req);
  if (cors) return cors;

  try {
    if (req.method !== 'POST') {
      return validationError('Method not allowed');
    }

    const [authErrorResponse, user, supabase, _supabaseUser] = await initAuthenticate(req);
    if (authErrorResponse) {
      return authErrorResponse;
    }

    const { providerKey, actionKey, actionParameters } = (await req.json()) as PerformActionRequest;

    if (!providerKey || !actionKey) {
      return validationError('providerKey and actionKey are required');
    }

    const { data: connection } = await supabase
      .from('connections')
      .select('id')
      .eq('userId', user.id)
      .eq('providerKey', providerKey)
      .single();

    if (!connection?.id) {
      return errorResponse(`Prevetted connection not found for provider: ${providerKey}`);
    }

    const nango = getPseudoNangoAction(providerKey, connection.id);

    const runner = getRunner(providerKey, actionKey);
    if (!runner) {
      return errorResponse(`No handler found for: ${providerKey} - ${actionKey}`);
    }

    const result = await runner(nango, actionParameters || {});

    return new Response(JSON.stringify(result), { headers: corsHeaders });
  } catch (error) {
    return errorResponse(error);
  }
}
