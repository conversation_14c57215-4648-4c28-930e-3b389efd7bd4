import { Nango } from '@nangohq/node';
import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { initNango } from '../_shared/nango';

const nango = initNango();

export default async function handler(req: Request) {
  const cors = handleCors(req, ['GET', 'POST']);
  if (cors) return cors;

  try {
    if (req.method === 'GET') {
      return await handleGetSyncs(req);
    }
    if (req.method === 'POST') {
      return await handlePostSyncs(req);
    }
    return validationError('Method not allowed');
  } catch (error) {
    return errorResponse(error);
  }
}

async function handleGetSyncs(req: Request) {
  const url = new URL(req.url);
  const providerKey = url.searchParams.get('providerKey');
  if (!providerKey) {
    return validationError('providerKey is required');
  }

  const [authErrorResponse, user, supabase] = await initAuthenticate(req);
  if (authErrorResponse) return authErrorResponse;

  const { data: connection } = await supabase
    .from('connections')
    .select('id')
    .eq('userId', user.id)
    .eq('providerKey', providerKey)
    .single();

  if (!connection?.id) {
    return errorResponse(`Connection not found for provider: ${providerKey}`);
  }

  const status = await nango.syncStatus(providerKey, '*', connection.id);
  return new Response(JSON.stringify(status), { headers: corsHeaders });
}

async function handlePostSyncs(req: Request) {
  const [authErrorResponse, user, supabase] = await initAuthenticate(req);
  if (authErrorResponse) return authErrorResponse;

  const { providerKey, syncs, action } = (await req.json()) as {
    providerKey?: string;
    syncs?: Record<string, any>;
    action?: 'start' | 'pause' | 'trigger';
  };

  if (!providerKey || !syncs || !Object.keys(syncs).length || !action) {
    return validationError('providerKey, syncs and action are required');
  }

  const { data: connection } = await supabase
    .from('connections')
    .select('id')
    .eq('userId', user.id)
    .eq('providerKey', providerKey)
    .single();

  if (!connection?.id) {
    return errorResponse(`Connection not found for provider: ${providerKey}`);
  }

  if (action === 'start') {
    for (const [name, meta] of Object.entries(syncs)) {
      if (meta && Object.keys(meta).length) {
        await nango.updateMetadata(providerKey, connection.id, { [name]: meta });
      }
      await nango.startSync(providerKey, [name], connection.id);
    }
  } else if (action === 'pause') {
    await nango.pauseSync(providerKey, Object.keys(syncs), connection.id);
  } else if (action === 'trigger') {
    await nango.triggerSync(providerKey, Object.keys(syncs), connection.id);
  } else {
    return validationError('Invalid action');
  }

  const metadata = await nango.getMetadata(providerKey, connection.id);
  return new Response(JSON.stringify(metadata), { headers: corsHeaders });
}
