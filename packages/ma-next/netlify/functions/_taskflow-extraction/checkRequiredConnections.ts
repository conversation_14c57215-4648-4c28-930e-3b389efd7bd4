import { SupabaseClient } from '@supabase/supabase-js';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';
import { debug } from '../_shared/debug';

/**
 * Checks if the user has all required provider connections
 * Returns an array of missing providers or empty array if all connections exist
 */
async function checkRequiredConnections(
  workflowSchema: any,
  userId: string,
  supabase: SupabaseClient
): Promise<[string[], string[]]> {
  const facade = new TaskflowSupabaseFacade(supabase);
  debug('checkRequiredConnections', { workflowSchema, userId });
  const requiredProviders = new Set<string>();
  const missingProviders = new Set<string>();

  // Extract all provider nodes from the workflow schema
  debug('Extracting required providers from workflow schema');
  for (const node of workflowSchema.nodes) {
    if (node.type.startsWith('provider.')) {
      const [_, providerKey] = node.type.split('.');
      if (providerKey) {
        requiredProviders.add(providerKey);
      }
    }
  }
  debug('Required providers:', Array.from(requiredProviders));

  // Check if the user has connections for all required providers
  if (requiredProviders.size > 0) {
    debug('Checking user connections for required providers');
    for (const providerKey of requiredProviders) {
      const { data: connection, error: connError } = await facade.getConnection(
        providerKey,
        userId
      );

      if (connError || !connection) {
        debug(`Missing connection for provider: ${providerKey}`, { connError });
        missingProviders.add(providerKey);
      } else {
        debug(`Connection found for provider: ${providerKey}`);
      }
    }
  } else {
    debug('No required providers found in workflow schema');
  }

  const result = [Array.from(missingProviders), Array.from(requiredProviders)];
  debug('checkRequiredConnections result:', result);
  return result as [string[], string[]];
}

export { checkRequiredConnections };
