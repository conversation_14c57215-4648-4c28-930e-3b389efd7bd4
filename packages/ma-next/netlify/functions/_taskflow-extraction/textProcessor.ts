import { processTaskflowJson } from './processTaskflowJson';
import { TASKFLOW_END_MARKER, TASKFLOW_START_MARKER } from './constants';
import { TransformContext } from '../submit-prompt/transform';

enum StreamState {
  NORMAL,
  IN_TASKFLOW,
}

type TextProcessorContext = TransformContext;

class TextProcessor {
  private ctx: TextProcessorContext;
  private buffer: string = '';
  private streamState: StreamState = StreamState.NORMAL;

  constructor(context: TextProcessorContext) {
    this.ctx = context;
  }

  /**
   * Manages extraction of data from the provided chunks of a text stream.
   *
   * We use a lag-slightly strategy:
   *
   * In normal state, we don't send chunks until we have large enough
   * buffer to confirm whether we've hit a start marker. Then either it does or doesn't contain the marker.
   * If not, it could still partially contain the marker, e.g. "123456789<tas", so we can only safely forward 123,
   * leaving the buffer as 456789<tas which is marker.length chars.
   *
   * When a marker is found we switch to the marker extraction state.
   */
  async processChunk(textChunk: string): Promise<object | void> {
    this.buffer += textChunk;

    switch (this.streamState) {
      case StreamState.NORMAL: {
        return this.processNormalState();
      }
      case StreamState.IN_TASKFLOW: {
        return this.processInTaskflowState();
      }
    }
  }

  /**
   * Collect text, forwarding max (buffer.length - marker.length) chars, until we hit the start marker.
   */
  private processNormalState() {
    // Too short
    // Buffers like: "that. <t" -> wait till we can check for the marker
    if (this.buffer.length < TASKFLOW_START_MARKER.length) {
      return;
    }

    // Example: "I can help you with that. <task" -> send `I can help you with t` and keep `hat. <task` (marker.length chars)
    if (!this.buffer.includes(TASKFLOW_START_MARKER)) {
      const head = this.buffer.slice(0, -TASKFLOW_START_MARKER.length);
      this.ctx.chatProtocolEnqueuer.addTextChunk(head);
      this.buffer = this.buffer.slice(-TASKFLOW_START_MARKER.length);
      return;
    }

    // Includes TASKFLOW_START_MARKER
    const [head, tail] = this.buffer.split(TASKFLOW_START_MARKER);
    if (head) {
      this.ctx.chatProtocolEnqueuer.addTextChunk(head);
    }
    this.buffer = tail; // buffer is now the tag contents.
    this.streamState = StreamState.IN_TASKFLOW;
    this.ctx.chatProtocolEnqueuer.addAuthoringTaskflow(head);

    if (tail.includes(TASKFLOW_END_MARKER)) {
      this.processInTaskflowState();
    }
  }

  /**
   * Process the buffer when we're in the taskflow state: basically we collect JSON until we hit the end marker.
   */
  private async processInTaskflowState() {
    // If the buffer contains the end marker, process the taskflow JSON
    if (this.buffer.includes(TASKFLOW_END_MARKER)) {
      const [jsonString, tail] = this.buffer.split(TASKFLOW_END_MARKER);

      await processTaskflowJson(jsonString, this.ctx);

      this.ctx.chatProtocolEnqueuer.addTextChunk(tail);
      this.buffer = '';
      this.streamState = StreamState.NORMAL;
    }
  }

  /**
   * To be called when there's a risk that there was some leftover text in the buffer.
   */
  flush(): void {
    if (this.buffer) {
      // Process any remaining buffer content as normal text
      this.ctx.chatProtocolEnqueuer.addTextChunk(this.buffer);
      this.buffer = '';
    }
  }
}

export type { TextProcessorContext };
export { TextProcessor };
