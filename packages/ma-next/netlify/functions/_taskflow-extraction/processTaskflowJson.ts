import { createAndTriggerExecution } from './createAndTriggerExecution';
import { getDebugMode } from '../_shared/debug';
import { TextProcessorContext } from './textProcessor';
import { checkRequiredConnections } from './checkRequiredConnections';
import { createTaskflow } from './createTaskflow';
import { RawTaskflowTransformer } from './RawTaskflowTransformer';
import { initNango } from '../_shared/nango';
import { mergeSyncScopes } from './mergeSyncScopes';

const DEBUG = getDebugMode();

/**
 * Process the taskflow JSON: create the taskflow and potentially trigger the execution.
 */
async function processTaskflowJson(
  jsonString: string,
  context: TextProcessorContext
): Promise<void> {
  const { chatProtocolEnqueuer, user, supabase, conversationId } = context;

  try {
    const schema = JSON.parse(jsonString);

    const [missingProviders, requiredProviders] = await checkRequiredConnections(
      schema,
      user.id,
      supabase
    );

    const providerScopes: Record<string, any> = {};
    for (const node of schema.nodes || []) {
      if (node.type === 'trigger.syncTrigger') {
        const provider = node.parameters?.providerKey;
        const syncKey = node.parameters?.syncKey;
        const scope = node.parameters?.syncScopes || null;
        if (provider && syncKey) {
          providerScopes[provider] = providerScopes[provider] || {};
          providerScopes[provider][syncKey] = scope;
        }
      }
    }
    for (const p of requiredProviders) {
      if (!providerScopes[p]) providerScopes[p] = {};
    }

    const transformer = new RawTaskflowTransformer(schema);
    transformer.addRequiredConnections(providerScopes);

    const taskflow = await createTaskflow(
      transformer.getSchema(),
      conversationId,
      supabase
    );

    if (DEBUG) console.log(JSON.stringify(taskflow, null, 2));

    if (!taskflow) return;

    // Agent or task mode taskflow?
    if (taskflow.schema?.triggers?.length > 0) {
      const taskflowInfo = { id: taskflow.id };
      chatProtocolEnqueuer.addTaskflowChunk(taskflowInfo);
    } else {
      await createAndTriggerExecution({
        ...context,
        taskflowId: taskflow.id,
        triggerData: taskflow.schema.triggerData,
        setupSteps: missingProviders.map(provider => ({
          type: 'connectProvider',
          provider,
          completed: false,
        })),
      });
    }

    const nango = initNango();
    for (const [provider, scopes] of Object.entries(providerScopes)) {
      if (!missingProviders.includes(provider)) {
        await mergeSyncScopes(provider, scopes, user.id, supabase, nango);
      }
    }
  } catch (error) {
    console.error('Error parsing or processing taskflow JSON:', error);
    chatProtocolEnqueuer.addError('taskflow extraction failed');
  }
}

export { processTaskflowJson };
