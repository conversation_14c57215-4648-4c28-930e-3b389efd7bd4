export class RawTaskflowTransformer {
  private schema: any;
  private syncTriggerNodes: any[] = [];
  private hasTriggers = false;

  constructor(schema: any) {
    this.schema = schema;
  }

  addRequiredConnections(providers: Record<string, any>) {
    const keys = Object.keys(providers);
    if (keys.length > 0) {
      this.schema.steps = keys.map(provider => ({
        type: 'connectProvider',
        provider,
        syncScopes: providers[provider],
        completed: false,
      }));
    }
    return this;
  }

  extractSyncTriggers() {
    const syncTriggerNodes = this.schema.nodes.filter(
      (node: any) => node.type === 'trigger.syncTrigger'
    );

    if (syncTriggerNodes.length > 0) {
      this.schema.nodes = this.schema.nodes.filter(
        (node: any) => node.type !== 'trigger.syncTrigger'
      );
      this.schema.triggers = syncTriggerNodes;
      this.syncTriggerNodes = syncTriggerNodes;
      this.hasTriggers = true;
    }

    return { syncTriggerNodes: this.syncTriggerNodes, hasTriggers: this.hasTriggers };
  }

  getSchema() {
    return this.schema;
  }
}
