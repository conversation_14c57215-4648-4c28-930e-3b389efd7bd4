import { createExecution, triggerWithExecution } from '../_taskflow';
import { TextProcessorContext } from './textProcessor';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';

type ExecutionContext = TextProcessorContext & {
  taskflowId: string;
  triggerData: Record<string, unknown>;
  setupSteps: Array<{ type: string; provider?: string; completed?: boolean }>;
};

/**
 * Creates and triggers a taskflow execution
 */
async function createAndTriggerExecution(context: ExecutionContext) {
  const {
    chatProtocolEnqueuer,
    taskflowId,
    user,
    triggerData,
    supabase,
    setupSteps = [],
  } = context;
  const facade = new TaskflowSupabaseFacade(supabase);
  try {
    // Create an execution for this taskflow
    const [executionResult, executionError] = await createExecution({
      taskflowId,
      supabaseFacade: facade,
      userId: user.id,
      triggerData,
    });

    // If there are setup steps, we need to update the execution context
    if (setupSteps.length > 0 && executionResult?.taskflowExecution?.id) {
      // Get the current context first
      const { data: executionData } = await facade.getExecutionContext(
        executionResult.taskflowExecution.id
      );

      // Merge the setup node with the existing context
      const updatedContext = {
        ...(executionData?.context || {}),
        setup: {
          type: 'setup',
          status: 'PAUSED',
          steps: setupSteps,
          started: false,
        },
      };

      // Update the execution context to include setup steps
      await facade.updateExecution(executionResult.taskflowExecution.id, {
        context: updatedContext,
      });
    }

    if (!executionResult || executionError || !executionResult.taskflowExecution?.id) {
      console.error('Error creating execution:', executionError);
      throw executionError || new Error('Failed to create execution');
    }

    const executionId = executionResult.taskflowExecution.id;

    // Send the taskflow ID
    const taskflowData = { id: taskflowId };
    chatProtocolEnqueuer.addTaskflowChunk(taskflowData);

    // Send the execution ID
    const executionData = {
      executionId,
    };
    chatProtocolEnqueuer.addExecutionChunk(executionData);

    // Only trigger the execution if there are no setup steps
    if (setupSteps.length === 0) {
      await triggerWithExecution({
        taskflowExecutionId: executionId,
        supabaseFacade: facade,
        userId: user.id,
      });
    }
    return true;
  } catch (error) {
    console.error('Error creating or triggering execution:', error);
    return false;
  }
}

export { createAndTriggerExecution };
export type { ExecutionContext };
