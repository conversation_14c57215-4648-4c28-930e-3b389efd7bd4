import { deepStrictEqual } from 'node:assert';
import { test } from 'node:test';
import { mergeSyncScopes, mergeValues } from './mergeSyncScopes';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Nango } from '@nangohq/node';

const mockSupabase = (id: string | null) =>
  ({
    from: () => ({
      select: () => ({
        eq: () => ({
          eq: () => ({
            maybeSingle: async () => ({ data: id ? { id } : null, error: null }),
          }),
        }),
      }),
    }),
  }) as unknown as SupabaseClient;

test('mergeValues merges arrays and objects', () => {
  const a = { a: [1], b: { c: 1 } };
  const b = { a: [1, 2], b: { d: 2 } };
  deepStrictEqual(mergeValues(a, b), { a: [1, 2], b: { c: 1, d: 2 } });
});

test('mergeSyncScopes updates metadata and starts syncs', async () => {
  const updates: any[] = [];
  const starts: any[] = [];
  const nango = {
    getMetadata: async () => ({ messages: { conversations: ['A'] } }),
    updateMetadata: async (_p: string, _id: string, meta: any) => {
      updates.push(meta);
    },
    startSync: async (_p: string, names: string[], _id: string) => {
      starts.push(...names);
    },
  } as unknown as Nango;

  await mergeSyncScopes(
    'slack',
    { messages: { conversations: ['B'] } },
    'user',
    mockSupabase('123'),
    nango
  );

  deepStrictEqual(updates, [
    { messages: { conversations: ['A', 'B'] } },
  ]);
  deepStrictEqual(starts, ['messages']);
});
