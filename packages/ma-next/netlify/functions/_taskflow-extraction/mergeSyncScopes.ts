import { SupabaseClient } from '@supabase/supabase-js';
import { Nango } from '@nangohq/node';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';


async function mergeSyncScopes(
  providerKey: string,
  scopes: Record<string, any>,
  userId: string,
  supabase: SupabaseClient,
  nango: Nango
) {
  const facade = new TaskflowSupabaseFacade(supabase);
  const { data: connection } = await facade.getConnection(providerKey, userId);
  if (!connection?.id) return;

  let metadata: Record<string, any> = {};
  try {
    metadata = (await nango.getMetadata(providerKey, connection.id)) || {};
  } catch {
    metadata = {};
  }

  for (const [name, scope] of Object.entries(scopes)) {
    const merged = mergeValues(metadata[name], scope);
    metadata[name] = merged;
    await nango.updateMetadata(providerKey, connection.id, { [name]: merged });
    await nango.startSync(providerKey, [name], connection.id);
  }
}

function mergeValues(existing: any, incoming: any): any {
  if (Array.isArray(existing) && Array.isArray(incoming)) {
    return Array.from(new Set([...existing, ...incoming]));
  }
  if (
    existing &&
    incoming &&
    typeof existing === 'object' &&
    typeof incoming === 'object'
  ) {
    const result: Record<string, any> = { ...existing };
    for (const [key, value] of Object.entries(incoming)) {
      result[key] = mergeValues(existing[key], value);
    }
    return result;
  }
  return incoming ?? existing;
}

export { mergeSyncScopes, mergeValues };
