import { getDebugMode } from '../_shared/debug';

const DEBUG = getDebugMode();

/**
 * Generic webhook handler for various services
 * The service name is specified in the path parameter
 * If REQUEST_BIN_URL env var is set, forwards the request to that URL
 * Otherwise, logs the payload (if in debug mode) and does nothing
 */
export default async function handler(req: Request): Promise<Response> {
  try {
    // Extract service name from URL path
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    // The path will be like /services-webhook/[service-name]
    const serviceName = pathParts[pathParts.length - 1];

    if (DEBUG) {
      console.log(`Received webhook from service: ${serviceName}`);
      console.log(`Request method: ${req.method}`);
      console.log(`Request headers: ${JSON.stringify(Object.fromEntries(req.headers))}`);
    }

    // Get request body
    const contentType = req.headers.get('content-type') || '';
    let payload: any;

    if (contentType.includes('application/json')) {
      payload = await req.json();
    } else {
      payload = await req.text();
    }

    // Check if REQUEST_BIN_URL is set
    const requestBinUrl = process.env.REQUEST_BIN_URL;

    if (requestBinUrl) {
      if (DEBUG) {
        console.log(`Forwarding payload to REQUEST_BIN_URL: ${requestBinUrl}`);
      }

      // Forward the request to the REQUEST_BIN_URL
      const forwardResponse = await fetch(requestBinUrl, {
        method: req.method,
        headers: req.headers,
        body: typeof payload === 'string' ? payload : JSON.stringify(payload),
      });

      if (DEBUG) {
        console.log(`Forward response status: ${forwardResponse.status}`);
      }

      // Return the response from the forwarded request
      return new Response(
        JSON.stringify({
          success: true,
          service: serviceName,
          forwarded: true,
          status: forwardResponse.status,
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    } else {
      // No REQUEST_BIN_URL, just log the payload if in debug mode
      if (DEBUG) {
        console.log(
          `Payload from ${serviceName}:`,
          typeof payload === 'string' ? payload : JSON.stringify(payload, null, 2)
        );
      }

      // Return a success response
      return new Response(
        JSON.stringify({
          success: true,
          service: serviceName,
          message: 'Webhook received successfully',
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
  } catch (error) {
    console.error('Webhook processing failed:', error);

    // Type guard for Error objects
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (DEBUG) {
      console.error(
        'Error details:',
        error instanceof Error ? error.stack : 'No stack trace available'
      );
    }

    return new Response(JSON.stringify({ success: false, error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
