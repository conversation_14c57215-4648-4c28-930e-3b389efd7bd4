import { corsHeaders, handleCors } from '../_shared/cors';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { errorResponse, validationError } from '../_shared/error';
import { set } from 'lodash';

interface PatchStepRequest {
  agentId: string;
  path: string;
  value: any;
  meta?: any;
}

interface PatchStepResponse {
  success: boolean;
  error?: string;
  details?: string;
}

export default async function handler(req: Request) {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    // Validate request method
    if (req.method !== 'POST') {
      return validationError('Method not allowed');
    }

    // Parse request body
    const { agentId, path, value, meta } = (await req.json()) as PatchStepRequest;

    // Validate required fields
    if (!agentId) {
      return validationError('Agent ID is required');
    }

    if (!path) {
      return validationError('Path is required');
    }

    // Initialize Supabase client
    const supabase = initServiceRoleSupabase();

    // Get the current response configuration
    const { data: response, error: responseError } = await supabase
      .from('agents')
      .select('config')
      .eq('id', agentId)
      .single();

    if (responseError) {
      throw new Error(`Failed to get response configuration: ${responseError.message}`);
    }

    if (!response || !response.config) {
      return validationError('Response configuration not found');
    }

    // Create a deep copy of the configuration
    const updatedConfig = JSON.parse(JSON.stringify(response.config));

    // Update the specific path
    set(updatedConfig, path, value);

    // Update the response configuration
    const { error: updateError } = await supabase
      .from('agents')
      .update({ config: updatedConfig })
      .eq('id', agentId);

    if (updateError) {
      throw new Error(`Failed to update response configuration: ${updateError.message}`);
    }

    // Handle any side effects based on metadata
    if (meta) {
      console.log('Handling metadata for update:', meta);
      // Future implementation for handling metadata
    }

    return new Response(JSON.stringify({ success: true } as PatchStepResponse), {
      headers: corsHeaders,
    });
  } catch (error) {
    return errorResponse(error);
  }
}
