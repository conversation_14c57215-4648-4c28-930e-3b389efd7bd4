import { ToolResultContent, UserMessage } from '../_protocol/index';

export type PromptRequest =
  // New conversation - always a user message
  | {
      id: string;
      mode: 'agent' | 'task';
      confirmedTools?: never;
      toolResults?: never;
      isNew: true;
      message: UserMessage;
      dateTime: string;
    }
  // Existing conversation - user message
  | {
      id: string;
      mode: 'agent' | 'task';
      confirmedTools?: never;
      toolResults?: never;
      isNew?: false;
      message: UserMessage;
      dateTime: string;
    }
  // Existing conversation - tool call confirmation
  | {
      id: string;
      mode: 'task';
      confirmedTools: Record<string, boolean>;
      toolResults?: never;
      isNew?: false;
      message?: never;
      dateTime: string;
    }
  // Existing conversation - tool result
  | {
      id: string;
      mode: 'agent' | 'task';
      confirmedTools?: never;
      toolResults: ToolResultContent[];
      isNew?: false;
      message: never;
      dateTime: string;
    };
