import { AssistantMessage, Message, MessagePart, ToolCallPart } from '../../_protocol/index';

function getUnfinishedToolParts(parts: MessagePart[]) {
  return parts.filter(
    (tc, index) =>
      tc.type === 'tool_call' &&
      !parts
        .slice(index + 1)
        .find(tr => tr.type === 'tool_result' && tr.content.toolCallId === tc.content.toolCallId)
  ) as ToolCallPart[];
}

function getLastAssistantMessageParts(messages: Message[]) {
  const lastAssistantMessageParts: AssistantMessage['parts'] =
    [...messages].reverse().find(m => m.role === 'assistant')?.parts || [];

  return lastAssistantMessageParts;
}

function updateLastAssistantMessageParts(messages: Message[], parts: MessagePart[]) {
  const lastAssistantMessage = [...messages].reverse().find(m => m.role === 'assistant');

  if (!lastAssistantMessage) {
    return messages;
  }

  return messages.map(m => (m.id === lastAssistantMessage.id ? ({ ...m, parts } as Message) : m));
}

export { getLastAssistantMessageParts, getUnfinishedToolParts, updateLastAssistantMessageParts };
