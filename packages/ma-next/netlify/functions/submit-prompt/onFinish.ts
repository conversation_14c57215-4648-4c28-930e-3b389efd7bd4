import { initServiceRoleSupabase } from '../_shared/supabase';
import { Message, MessagePart } from '../_protocol';

/**
 * Saves the Message[] streamed to the frontend to the conversation in the db
 */
export async function onFinish(
  parts: MessagePart[],
  priorMessages: Message[],
  supabase: ReturnType<typeof initServiceRoleSupabase>,
  id: string,
  isContinuation: boolean,
  mode: 'agent' | 'task',
  oaiResponseId: string
) {
  const [messages, messageToUpdate] = isContinuation
    ? [priorMessages.slice(0, -1), priorMessages[priorMessages.length - 1]]
    : [priorMessages, null];

  try {
    const assistantMessage = (
      messageToUpdate
        ? {
            ...messageToUpdate,
            parts,
          }
        : {
            id: crypto.randomUUID(),
            role: 'assistant',
            parts,
            createdAt: new Date(),
          }
    ) as Message;

    // Save the Message[] streamed to the frontend to the conversation in the db
    const updateData: any = {
      messages: [...messages, assistantMessage],
      oaiResponseId,
    };

    // If mode is provided, update it as well
    if (mode) {
      updateData.mode = mode;
    }

    const { error: updateError } = await supabase
      .from('conversations')
      .update(updateData)
      .eq('id', id);

    if (updateError) {
      console.error('Error updating conversation messages:', updateError);
    }
  } catch (error) {
    console.error('Error saving message to database:', error);
  }
}
