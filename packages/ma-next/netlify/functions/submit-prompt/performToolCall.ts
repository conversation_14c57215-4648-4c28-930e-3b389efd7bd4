import {
  <PERSON><PERSON>all<PERSON>rg<PERSON>,
  ActionCallResult,
  ToolCallPart,
  ToolResult,
  ToolResultPayload,
} from '../_protocol';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { getRunner } from '../_tools/actions';
import { getPseudoNangoAction } from '../_nango/getPseudoNangoAction';
import { RichResultModelDisplay } from '../../../src/components/rich-ui/RichResultModelDisplay';
import { ACTION_OUTPUT_MODELS_KEYED } from '../_agents/nangoConstants';

type Context = {
  supabase: ReturnType<typeof initServiceRoleSupabase>;
  user: any;
};

const UNHANDLED_OUTCOME = Symbol('unhandled');

/**
 * Handle tool calls and returns an actual ToolResult
 */
async function performToolCall(
  toolCall: ToolCallPart,
  { supabase, user }: Context
): Promise<typeof UNHANDLED_OUTCOME | ToolResult> {
  const result = await performTool<PERSON>allInner(toolCall, { supabase, user });

  if (result === UNHANDLED_OUTCOME) {
    return UNHANDLED_OUTCOME;
  }

  return {
    toolCallId: toolCall.content.toolCallId,
    toolName: toolCall.content.toolName,
    result,
  } as ToolResult;
}

/**
 * Returns the ToolResult["result"]
 */
async function performToolCallInner(
  toolCall: ToolCallPart,
  { supabase, user }: Context
): Promise<typeof UNHANDLED_OUTCOME | ToolResult['result']> {
  if (toolCall.content.toolName === 'actionCall') {
    return await handleActionCallTool(toolCall.content.args, { supabase, user });
  }

  return UNHANDLED_OUTCOME;
}

/**
 * Handle actionCallTool
 */
async function handleActionCallTool(
  { providerKey, actionKey, actionParameters }: ActionCallArgs,
  { supabase, user }: Context
): Promise<ToolResultPayload<ActionCallResult>> {
  if (!actionParameters) {
    return {
      error:
        "You're nearly there! Please try calling callAction again: you MUST include actionParameters as an object",
    };
  }

  try {
    const { data: connection } = await supabase
      .from('connections')
      .select('id')
      .eq('userId', user.id)
      .eq('providerKey', providerKey)
      .single();

    if (!connection || !connection.id) {
      throw new Error(`Prevetted connection not found for provider: ${providerKey}`);
    }

    const connectionId = connection?.id;

    try {
      const nango = getPseudoNangoAction(providerKey, connectionId);

      const runner = getRunner(providerKey, actionKey);

      if (!runner) {
        return {
          success: false,
          error: 'No handler found for: ${providerKey} - ${actionKey}',
        };
      }

      const response = await runner(nango, actionParameters);

      if (response && 'error' in response) {
        return {
          success: false,
          error: response.error,
        };
      }

      const modelName =
        ACTION_OUTPUT_MODELS_KEYED[`${providerKey}:${actionKey}` as keyof typeof ACTION_OUTPUT_MODELS_KEYED]
          ?.model;
      return {
        success: true,
        data: response,
        richUISupport: Boolean(modelName && RichResultModelDisplay.canDisplay(modelName)),
      };
    } catch (nangoError) {
      const errorMessage = nangoError instanceof Error ? nangoError.message : String(nangoError);

      return {
        success: false,
        error: `API error: ${errorMessage}`,
      };
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    return {
      success: false,
      error: `Action execution failed: ${errorMessage}`,
    };
  }
}

export { performToolCall, UNHANDLED_OUTCOME };
