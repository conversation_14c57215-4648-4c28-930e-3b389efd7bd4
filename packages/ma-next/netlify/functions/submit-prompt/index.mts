import { corsHeaders } from '../_shared/cors';
import { errorResponse } from '../_shared/error';
import { ChatProtocolEnqueuer } from '../_protocol/chatProtocolEnqueuer';
import { vercel } from '../_protocol/vercel';
import { validateAndAuthenticate } from './validateAndAuthenticate';
import { makeTransform } from './transform';
import { onFinish } from './onFinish';
import { upsertConversation } from './upsertConversation';
import { getNewLLMContext } from './getNewLLMContext';
import { performToolCall, UNHANDLED_OUTCOME } from './performToolCall';
import { getUnfinishedToolParts } from './helpers/unfinishedToolCalls';
import { actionsAgent } from '../_agents/actionsAgent';
import { agentAgent } from '../_agents/agentAgent';

export default async function serve(req: Request) {
  try {
    // 1. Validate
    const [response, user, supabase, supabaseUser, requestJSON] =
      await validateAndAuthenticate(req);
    if (response) return response;

    return new Response(
      new ReadableStream({
        async start(controller) {
          try {
            // 2. Prepare convo
            const {
              messages,
              oaiResponseId: responseId,
              userProfile,
            } = await upsertConversation({
              supabase,
              supabaseUser,
              user,
              requestJSON,
            });

            // 3. & 4 loop prep
            const {
              messagesWithAddedToolResults,
              initialOutgoingVercelMessages,
              serverAddedToolResults,
            } = await getNewLLMContext({
              messages,
              requestJSON,
              supabase,
              user,
            });

            const lastMessage =
              messagesWithAddedToolResults[messagesWithAddedToolResults.length - 1];
            const isContinuation = lastMessage.role === 'assistant';

            const chatProtocolEnqueuer = new ChatProtocolEnqueuer(
              controller,
              isContinuation ? lastMessage.parts : []
            );

            // FIXME: // Set up abort signal handler to send cancellation message
            // if (req.signal) {
            //   req.signal.addEventListener("abort", () => {
            //     try {
            //       // Send a special error message that the client can interpret as a cancellation
            //       chatProtocolEnqueuer.addError("Generation cancelled by user");
            //       controller.close();
            //     } catch (error) {
            //       console.error("Error sending cancellation message:", error);
            //     }
            //   });
            // }

            if (isContinuation) {
              chatProtocolEnqueuer.sendToolResultsToClient(serverAddedToolResults);
            }

            const transform = makeTransform({
              chatProtocolEnqueuer,
              user,
              supabase,
              conversationId: requestJSON.id,
              userProfile,
            });

            let oaiResponseId = responseId;
            let outgoingMessages = initialOutgoingVercelMessages;

            /*
              LOOP
            */
            while (true) {
              // 3. Query Agent
              const agentResponse = (requestJSON.mode === 'agent' ? agentAgent : actionsAgent)(
                outgoingMessages,
                {
                  previousResponseId: oaiResponseId,
                  userDateTime: requestJSON.dateTime,
                  userProfile,
                  abortSignal: req.signal,
                }
              );

              // for await (const chunk of agentResponse.fullStream) {
              //   console.log(chunk)
              // }

              // 4. Interpret & send response
              for await (const chunk of agentResponse.toDataStream()) {
                await transform(chunk);
              }

              oaiResponseId = (await agentResponse.providerMetadata)?.openai?.responseId as string;

              const unfinishedToolCalls = getUnfinishedToolParts(chatProtocolEnqueuer.getParts());
              const someRequireConfirmation = unfinishedToolCalls.some(
                call =>
                  call.content.toolName === 'actionCall' &&
                  call.content.confirmationRequired &&
                  !call.content.autoApproved
              );

              if (someRequireConfirmation) {
                break;
              }

              outgoingMessages = [];

              const toolOutcomes = await Promise.all(
                unfinishedToolCalls.map(async unfinishedToolCall => {
                  const toolOutcome = await performToolCall(unfinishedToolCall, { supabase, user });
                  if (toolOutcome !== UNHANDLED_OUTCOME) {
                    chatProtocolEnqueuer.addToolResult(toolOutcome);
                    outgoingMessages.push(
                      vercel.toolCallMessage(unfinishedToolCall),
                      vercel.toolResultMessage(toolOutcome)
                    );
                  }
                  return toolOutcome;
                })
              );

              const validToolOutcomes = toolOutcomes.filter(
                outcome => outcome !== UNHANDLED_OUTCOME
              );

              if (validToolOutcomes.length === 0) {
                break;
              }
            } // loop end

            // The loop is all one message from the client perspective.
            await onFinish(
              chatProtocolEnqueuer.getParts(),
              messagesWithAddedToolResults,
              supabase,
              requestJSON.id,
              isContinuation,
              requestJSON.mode || 'task',
              oaiResponseId
            );

            controller.close();
          } catch (error) {
            console.error('Error in stream processing:', error);
            controller.error(error);
          }
        }, // start end
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'text/plain; charset=utf-8',
          'Transfer-Encoding': 'chunked',
        },
      }
    );
  } catch (error) {
    console.error('Error in main handler:', error);
    return errorResponse(error);
  }
}
