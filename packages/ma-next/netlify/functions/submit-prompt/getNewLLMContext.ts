import { initServiceRoleSupabase } from '../_shared/supabase';
import { Message, MessagePart, ToolResult, ToolResultContent } from '../_protocol/index';
import { vercel, VercelCoreMessage } from '../_protocol/vercel';
import { keyBy, mapValues } from 'lodash-es';
import { performToolCall, UNHANDLED_OUTCOME } from './performToolCall';
import { PromptRequest } from './types';
import {
  getLastAssistantMessageParts,
  getUnfinishedToolParts,
  updateLastAssistantMessageParts,
} from './helpers/unfinishedToolCalls';

type Deps = {
  messages: Message[];
  requestJSON: PromptRequest;
  supabase: ReturnType<typeof initServiceRoleSupabase>;
  user: any;
};

type LlmContext = {
  initialOutgoingVercelMessages: VercelCoreMessage[];
  messagesWithAddedToolResults: Message[];
  serverAddedToolResults: ToolResult[];
};

/**
 * Extracts the last assistant message, and finds unfinished tool calls, performs them, and adds the results back to the
 * message, noting new vercel messages and the results added along the way.
 */
async function getNewLLMContext({
  messages,
  requestJSON: { confirmedTools: explicitConfirmations, message, toolResults },
  supabase, // only to get connectionIds
  user,
}: Deps): Promise<LlmContext> {
  const lastAssistantMessageParts = getLastAssistantMessageParts(messages);

  const unfinishedPartsById = keyBy(
    getUnfinishedToolParts(lastAssistantMessageParts),
    part => part.content.toolCallId
  );

  const clientResultsById = keyBy(toolResults, result => result.toolCallId);

  const confirmationsById = message
    ? mapValues(unfinishedPartsById, () => false)
    : explicitConfirmations;

  const addedToolResultsServer = new Set<string>();

  // We're doing: [textPart, toolCallPart] => [P(textPart), P(toolCallPart), P(toolPartResult)] where P is a Promise
  // This approach makes the mental model simple.
  const partsWithResultsAddedPromises = lastAssistantMessageParts.flatMap<
    Promise<MessagePart | typeof UNHANDLED_OUTCOME>
  >(part => {
    if (part.type !== 'tool_call') {
      return [Promise.resolve(part)];
    }

    const id = part.content.toolCallId;

    if (!unfinishedPartsById[id]) {
      return [Promise.resolve(part)]; // already completed tool call
    }

    // Now: Unfinished tool calls handling

    if (clientResultsById[id]) {
      return [
        Promise.resolve(part),
        Promise.resolve({
          type: 'tool_result',
          content: clientResultsById[id],
        }),
      ];
    }

    switch (confirmationsById[id]) {
      case undefined: {
        return [Promise.resolve(part)];
      }
      case false: {
        addedToolResultsServer.add(id);
        return [
          Promise.resolve(part),
          Promise.resolve({
            type: 'tool_result',
            content: {
              toolCallId: part.content.toolCallId,
              toolName: part.content.toolName,
              result: { userCancelled: true },
            },
          }),
        ];
      }
      case true: {
        addedToolResultsServer.add(id);
        return [
          Promise.resolve(part),
          performToolCall(part, { supabase, user }).then(result =>
            result === UNHANDLED_OUTCOME
              ? UNHANDLED_OUTCOME
              : {
                  type: 'tool_result' as const,
                  content: result,
                }
          ),
        ];
      }
    }
  });

  const nextParts: MessagePart[] = (await Promise.all(partsWithResultsAddedPromises)).filter(
    part => part && part !== UNHANDLED_OUTCOME
  );

  /*
    Now we have to extract the new tool_results
  */
  const vercelMessages = nextParts.flatMap<VercelCoreMessage>(part => {
    if (
      (part.type === 'tool_call' || part.type === 'tool_result') &&
      (addedToolResultsServer.has(part.content.toolCallId) ||
        clientResultsById[part.content.toolCallId])
    ) {
      return part.type === 'tool_call'
        ? [vercel.toolCallMessage(part)]
        : [vercel.toolResultMessage(part)];
    }
    return [];
  });

  if (message) {
    vercelMessages.push(vercel.userMessage(message));
  }

  const serverAddedToolResults: ToolResultContent[] = nextParts.flatMap(part =>
    part.type === 'tool_result' && addedToolResultsServer.has(part.content.toolCallId)
      ? [part.content]
      : []
  );

  return {
    messagesWithAddedToolResults: updateLastAssistantMessageParts(messages, nextParts),
    initialOutgoingVercelMessages: vercelMessages,
    serverAddedToolResults,
  };
}

export { getNewLLMContext };
