import { Tool<PERSON>all, ToolResult } from 'ai/dist/index';
import { SupabaseClient } from '@supabase/supabase-js';

async function makeAction(
  part: { type: 'tool_call'; value: ToolCall<string, any> },
  supbase: SupabaseClient
) {
  console.log('makeAgent', part.value.args);
  return [{ type: 'action' }];
}

async function makeAgent(
  part: { type: 'tool_result'; value: ToolResult<string, any, any> },
  supbase: SupabaseClient
) {
  console.log('makeAgent', part.value.result);

  return [{ type: 'agent' }];
}

export { makeAction, makeAgent };
