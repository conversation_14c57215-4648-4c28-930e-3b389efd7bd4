import { handleCors } from '../_shared/cors';
import { validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { User } from '@supabase/supabase-js';
import { initServiceRoleSupabase, initUserRoleSupabase } from '../_shared/supabase';
import { getDebugMode } from '../_shared/debug';
import { PromptRequest } from './types';

const DEBUG = getDebugMode();

/**
 * Validates the request and authenticates the user
 */
async function validateAndAuthenticate(
  req: Request
): Promise<
  | [Response]
  | [
      null,
      User,
      ReturnType<typeof initServiceRoleSupabase>,
      ReturnType<typeof initUserRoleSupabase>,
      PromptRequest,
    ]
> {
  const corsResponse = handleCors(req);
  if (corsResponse) {
    if (DEBUG) console.log('CORS response:', corsResponse);
    return [corsResponse];
  }

  if (req.method !== 'POST') {
    if (DEBUG) console.log('Method not allowed:', req.method);
    return [validationError('Method not allowed')];
  }

  if (!process.env.MASTRA_URL) {
    if (DEBUG) console.error('Mastra URL is not set');
    return [validationError('Mastra URL is not set')];
  }

  if (!process.env.MASTRA_INTENT_AGENT_NAME) {
    if (DEBUG) console.error('Mastra intent agent name is not set');
    return [validationError('Mastra intent agent name is not set')];
  }

  const requestJSON: PromptRequest = await req.json();
  const { id, message, mode = 'agent', confirmedTools, toolResults, isNew = false } = requestJSON;

  if (DEBUG) {
    console.log('Prompt:', message);
    console.log('Client conversation ID:', id);
    console.log('Mode:', mode);
    console.log('Is new conversation:', isNew);
    if (toolResults) console.log('Tool results:', toolResults);
  }

  if (!id || !(message || confirmedTools || toolResults) || !mode) {
    if (DEBUG)
      console.error('Conversation ID, message/confirmedTools/toolResults, and mode are required');
    return [
      validationError('Conversation ID, message/confirmedTools/toolResults, and mode are required'),
    ];
  }

  const auth = await initAuthenticate(req);

  if (DEBUG && auth[0]) console.error('Authentication error:', auth[0]);
  if (auth[0]) return [auth[0]];

  return [...auth, requestJSON];
}

export { validateAndAuthenticate };
