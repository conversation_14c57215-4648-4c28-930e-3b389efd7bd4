import { User } from '@supabase/supabase-js';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { TextProcessor } from '../_taskflow-extraction/textProcessor';
import { ToolCallProcessor } from '../_protocol/toolCallProcessor';
import { ChatProtocolEnqueuer } from '../_protocol/chatProtocolEnqueuer';
import { ToolResultContent } from '../_protocol/index';
import { VercelToolCallPart, VercelToolResultPart } from '../_protocol/vercel';

/**
 * Transform function for the TransformStream
 */
export type TransformContext = {
  chatProtocolEnqueuer: ChatProtocolEnqueuer;
  user: User;
  supabase: ReturnType<typeof initServiceRoleSupabase>;
  conversationId: string;
  userProfile?: {
    firstName?: string;
    lastName?: string;
    preferences?: {
      timeZone?: string;
      autoApprovals?: string[];
    };
  };
};

export const makeTransform = (context: TransformContext) => {
  const { chatProtocolEnqueuer } = context;
  const decoder = new TextDecoder();
  const textProcessor = new TextProcessor(context);
  const toolCallProcessor = new ToolCallProcessor(context);
  let buffer = '';

  return async (chunk: Uint8Array) => {
    const text = decoder.decode(chunk);
    const lines = text.split('\n');

    for (const line of lines) {
      if (line.trim() === '') continue;

      buffer += line;

      if (buffer[1] !== ':') {
        continue;
      }

      const prefix = buffer.slice(0, 2);
      const jsonText = buffer.substring(2).trim();

      let content: unknown;
      try {
        content = JSON.parse(jsonText);
      } catch (_) {
        continue;
      }

      buffer = '';

      if (prefix !== '0:') {
        textProcessor.flush();
      }

      switch (prefix) {
        case '0:': {
          await textProcessor.processChunk(content as string);
          break;
        }
        case '9:': {
          await toolCallProcessor.processToolCall(content as VercelToolCallPart);
          break;
        }
        case 'a:': {
          chatProtocolEnqueuer.addToolResult(content as VercelToolResultPart as ToolResultContent);
          break;
        }
        case '3:': {
          chatProtocolEnqueuer.addError(content as string);
          break;
        }
        // Pass through any other Vercel SDK data stream parts
        case 'b:': // Tool Call Streaming Start
        case 'c:': // Tool Call Delta
        case 'd:': // Finish Message
        case 'e:': // Finish Step
        case 'f:': // Start Step
        case 'g:': // Reasoning
        case 'h:': // Source
        case 'i:': // Redacted Reasoning
        case 'j:': // Reasoning Signature
        case 'k:': // File
        case '2:': // Data
        case '8:': // Message Annotation
          // These cases would benefit from proper typing using our Vercel type adaptors
          // controller.enqueue(chunk);
          break;
        default: {
          // do nothing
        }
      }
    }
  };
};
