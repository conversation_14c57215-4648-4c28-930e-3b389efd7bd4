import { getDebugMode } from '../_shared/debug';
import { User } from '@supabase/supabase-js';
import { initServiceRoleSupabase, initUserRoleSupabase } from '../_shared/supabase';
import { Message } from '../_protocol';
import { PromptRequest } from './types';

const DEBUG = getDebugMode();

/**
 * Initialises or updates the conversation based on the submitted prompt,
 * always resepecting the conversationId generated by the frontend.
 */
export async function upsertConversation({
  supabase,
  supabaseUser,
  user,
  requestJSON: { id, message, isNew, mode },
}: {
  supabase: ReturnType<typeof initServiceRoleSupabase>;
  supabaseUser: ReturnType<typeof initUserRoleSupabase>;
  user: User;
  requestJSON: PromptRequest;
}) {
  const userProfilePromise = getUserProfilePromise(supabaseUser, user);

  if (isNew) {
    if (DEBUG) console.log('Creating new conversation');
    const { error } = await supabaseUser
      .from('conversations')
      .insert([
        {
          id,
          title: message.content.substring(0, 50),
          userId: user.id,
          messages: [message],
          mode,
        },
      ])
      .select()
      .single();

    if (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }

    return { messages: [message], oaiResponseId: undefined, userProfile: await userProfilePromise };
  }

  const { data, error } = await supabase.rpc('append_conversation_message', {
    conv_id: id,
    new_message: message ? message : null,
  });

  if (error) {
    console.error('Error updating conversation messages:', error);
    throw error;
  }

  return {
    ...(data as { messages: Message[]; oaiResponseId: string }),
    userProfile: await userProfilePromise,
  };
}

function getUserProfilePromise(supabaseUser: ReturnType<typeof initUserRoleSupabase>, user: User) {
  return supabaseUser
    .from('profiles')
    .select('firstName, lastName, preferences')
    .eq('id', user.id)
    .single()
    .then(({ data, error }) => {
      if (error || !data) {
        return undefined;
      }
      return data;
    });
}
