import { executeTaskFlow } from '../_taskflow/executeTaskFlow';
import { DebugTracer } from '../_taskflow/debugTracer';
import { RawTaskflowTransformer } from '../_taskflow-extraction/RawTaskflowTransformer';
import type { NodeHandlers } from '../_taskflow/types';
import type { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';
import { TaskflowInMemoryFacade } from './taskflowInMemoryFacade';
import { v4 as uuidv4 } from 'uuid';

type ValidateOptions = {
  facade?: TaskflowInMemoryFacade;
  userId?: string;
  handlers?: Partial<NodeHandlers>;
  connections?: string[];
};

async function validateTaskflow(
  definition: any,
  triggerData: any,
  options: ValidateOptions = {}
): Promise<boolean> {
  const transformer = new RawTaskflowTransformer(JSON.parse(JSON.stringify(definition)));
  transformer.extractSyncTriggers();
  const schema = transformer.getSchema();

  const facade =  new TaskflowInMemoryFacade();

  // Setup user and conversation explicitly
  const userId = uuidv4();
  const conversationId = uuidv4();
  await facade.addUser(userId);
  await facade.addConversation(conversationId, userId);
  const {
    data: { id: taskflowId },
  } = await facade.createTaskflow(schema, conversationId, false);

  if (options.connections) {
    for (const connection of options.connections) {
      await facade.addConnection(uuidv4(), connection, userId);
    }
  }

  const supabaseFacade = facade as unknown as TaskflowSupabaseFacade;

  const tracer = new DebugTracer(supabaseFacade);

  const [result, error] = await executeTaskFlow({
    taskflowId,
    supabaseFacade,
    userId,
    triggerData,
    tracer,
    handlers: options.handlers,
  });

  if (error) throw error;
  if ('errors' in result && result.errors) {
    throw new Error('Taskflow execution failed');
  }

  return true;
}

export { validateTaskflow };
