import { test } from 'node:test';
import { strictEqual } from 'node:assert';
import { EMAIL_WORKFLOW_SCHEMA, A_TO_B_INSTANCE_SCHEMA } from '../_agents/sampleWorkflows';
import { validateTaskflow } from './validateTaskflow';
import { NodeHandlers } from '../_taskflow/types';

import dotEnv from 'dotenv';

dotEnv.config();

const handlers: Partial<NodeHandlers> = {
  executeActionNode: async () => ({ status: 'SUCCESS', result: { faked: true } }),
  aiNodeDependencies: {
    createOpenAI: () => ({ responses: () => ({}) }),
    generateObject: async () => ({ object: { subject: 's', body: 'b' } }),
    generateText: async () => ({ text: 'x' }),
    jsonSchema: (s: any) => s,
  } as any,
};

// test('validateTaskflow runs workflow without errors', async () => {
//   const result = await validateTaskflow(
//     EMAIL_WORKFLOW_SCHEMA,
//     {
//       id: '1',
//       sender: '<EMAIL>',
//       recipients: '<EMAIL>',
//       date: '2024-01-01T00:00:00Z',
//       subject: 'Test Subject',
//       body: 'Test Body',
//       attachments: [],
//       threadId: 'thread-1',
//       isDraft: false,
//       labels: ['INBOX', 'IMPORTANT'],
//       snippet: 'Test snippet',
//       cc: '<EMAIL>',
//       bcc: '<EMAIL>',
//       messageId: 'msg-1',
//       inReplyTo: 'msg-0',
//       references: 'msg-0',
//     },
//     {
//       handlers: {
//         ...handlers,
//         aiNodeDependencies: {
//           ...handlers.aiNodeDependencies,
//           generateObject: () => ({
//             object: {
//               subject: 'Re: Test Subject',
//               body: 'Thank you for your email. I will get back to you shortly.\n\nBest regards,\nYour Name',
//             },
//           }),
//         },
//       } as any,
//     }
//   );
//   strictEqual(result, true);
// });

test('validateTaskflow runs aToB agent workflow without errors', async () => {
  const mockPullRequest = {
    id: 123,
    number: 456,
    title: 'Test PR',
    body: 'This is a test pull request',
    state: 'open',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    repository: {
      id: 789,
      name: 'test-repo',
      full_name: 'test-owner/test-repo',
      owner: {
        login: 'test-owner',
        id: 101,
      },
    },
    user: {
      login: 'test-user',
      id: 102,
    },
    head: {
      ref: 'feature-branch',
      sha: 'abc123',
    },
    base: {
      ref: 'main',
      sha: 'def456',
    },
  };

  const result = await validateTaskflow(A_TO_B_INSTANCE_SCHEMA, mockPullRequest, {
    connections: ['github'],
    handlers: {
      ...handlers,
      agentNodeDependencies: {
        getRunner: () => async () => ({
          url: 'https://github.com/test-owner/test-repo/pull/456',
          id: 123,
          node_id: 'MDExOlB1bGxSZXF1ZXN0MTIz',
          html_url: 'https://github.com/test-owner/test-repo/pull/456',
          diff_url: 'https://github.com/test-owner/test-repo/pull/456.diff',
          patch_url: 'https://github.com/test-owner/test-repo/pull/456.patch',
          issue_url: 'https://api.github.com/repos/test-owner/test-repo/issues/456',
          number: 456,
          state: 'open',
          locked: false,
          title: 'Updated PR Title',
          user: {
            login: 'test-user',
            id: 102,
            node_id: 'MDQ6VXNlcjEwMg==',
            avatar_url: '',
            gravatar_id: '',
            url: '',
            html_url: '',
            followers_url: '',
            following_url: '',
            gists_url: '',
            starred_url: '',
            subscriptions_url: '',
            organizations_url: '',
            repos_url: '',
            events_url: '',
            received_events_url: '',
            type: 'User',
            site_admin: false,
          },
          body: 'Updated PR Description generated by agent',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          closed_at: null,
          merged_at: null,
          merge_commit_sha: null,
          assignee: null,
          assignees: [],
          requested_reviewers: [],
          requested_teams: [],
          labels: [],
          milestone: null,
          draft: false,
          commits_url: '',
          review_comments_url: '',
          review_comment_url: '',
          comments_url: '',
          statuses_url: '',
          head: {},
          base: {},
          _links: {},
          author_association: 'CONTRIBUTOR',
          auto_merge: null,
          active_lock_reason: null,
          merged: false,
          mergeable: null,
          rebaseable: null,
          mergeable_state: 'unknown',
          merged_by: null,
          comments: 0,
          review_comments: 0,
          maintainer_can_modify: true,
          commits: 1,
          additions: 10,
          deletions: 2,
          changed_files: 1,
        }),
        getPseudoNangoAction: () => ({}),

        /*
          OpenAI mocking
        */
        generateObject: async () => ({
          object: {
            owner: 'test-owner',
            repo: 'test-repo',
            pullNumber: 456,
            title: 'Updated PR Title',
            body: 'Updated PR Description generated by agent',
            state: 'open',
            base: 'main',
            maintainer_can_modify: true,
          },
        }),
      },
    } as any,
  });
  strictEqual(result, true);
});
