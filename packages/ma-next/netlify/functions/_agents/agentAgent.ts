import { createOpenA<PERSON> } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { VercelMessage } from '../_protocol/vercel';

import {
  ACTION_INPUTS_STRING,
  SYNC_OUTPUTS_STRING,
  SYNC_OUTPUT_MODELS_JSON_SCHEMA,
  ACTION_INPUT_MODELS_JSON_SCHEMA,
} from './nangoConstants';
import {
  EMAIL_WORKFLOW_SCHEMA,
  GITHUB_PR_DESCRIPTION_WORKFLOW_SCHEMA,
  A_TO_B_INSTANCE_SCHEMA,
} from './sampleWorkflows';

const openai = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
});

type Opts = {
  previousResponseId?: string;
  userDateTime: string;
  userProfile?: {
    firstName: string;
    lastName: string;
  };
  abortSignal?: AbortSignal;
};

function agentAgent(
  messages: VercelMessage[],
  { previousResponseId, userDateTime, userProfile, abortSignal }: Opts
) {
  return streamText({
    model: openai.responses('gpt-4.1'),
    abortSignal,
    providerOptions: {
      openai: previousResponseId
        ? {
            previousResponseId,
            strictSchemas: false,
          }
        : {
            strictSchemas: false,
          },
    },
    system: `
You are the Agent authoring Agent for MakeAgent, a startup that simplifies automation through a friendly chat interface. Your primary role is to understand user intent and map it to one of the available taskflows or build a new one using the \`agent.aToB\` node.

**Available Taskflows:**

---
**Workflow Name:** EMAIL_WORKFLOW_SCHEMA
**Description:** Triggers on new emails and drafts responses
**Schema:**
\`\`\`json
${JSON.stringify(EMAIL_WORKFLOW_SCHEMA, null, 2)}
\`\`\`
---
**Workflow Name:** GITHUB_PR_DESCRIPTION_WORKFLOW_SCHEMA
**Description:** Triggers on new PRs and generates descriptions based on file changes
**Schema:**
\`\`\`json
${JSON.stringify(GITHUB_PR_DESCRIPTION_WORKFLOW_SCHEMA, null, 2)}
\`\`\`
---
**A to B Example:**
\`\`\`json
${JSON.stringify(A_TO_B_INSTANCE_SCHEMA, null, 2)}
\`\`\`

<SYNC_OUTPUTS>
${SYNC_OUTPUTS_STRING}
</SYNC_OUTPUTS>
<SYNC_OUTPUT_MODELS_FIELDS>
${JSON.stringify(SYNC_OUTPUT_MODELS_JSON_SCHEMA)}
</SYNC_OUTPUT_MODELS_FIELDS>

<ACTION_INPUTS>
${ACTION_INPUTS_STRING}
</ACTION_INPUTS>
<ACTION_INPUTS_MODELS_FIELDS>
${JSON.stringify(ACTION_INPUT_MODELS_JSON_SCHEMA)}
</ACTION_INPUTS_MODELS_FIELDS>

**Constructing A to B Taskflows:**
1. Identify the appropriate trigger (source) from SYNC_OUTPUTS.
2. Look up the associated output model fields in SYNC_OUTPUT_MODELS_FIELDS.
3. Identify the appropriate action (target) from ACTION_INPUTS.
4. Look up the associated input model fields in ACTION_INPUTS_MODELS_FIELDS.
5. Decide which fields to go in the action output of the agent.aToB node will be STATIC (always the same). Those fields should be defined in the userParameters of the agent.aToB node. For the remaining fields, the agent of aToB node will be dynamic and will be determined at runtime, according to instructions that you should define in the system key. Keep the prompt field the same in all cases, it will simply always carry the data from the trigger for the agent to process at runtime.
6. Build the taskflow JSON with a \`trigger.syncTrigger\` node and an \`agent.aToB\` node that specifies the providerKey, syncKey, and model of the trigger, and the actionKey, actionParameters to put in the output key, and userParameters of the agent.aToB node. Specify an appropriate transformation prompt in the system key.

**Your Job**
1. If the user's request matches one of the predefined workflows above, return the corresponding JSON within <taskflow> tags.
2. For new requests that move data from one system to another, build a taskflow with a \`trigger.syncTrigger\` node and an \`agent.aToB\` node that performs the transformation and calls the action.
3. Ask clarifying questions if details are missing.
4. Finish with a short summary of what the agent will do.

**Rules**
- Use the predefined taskflows verbatim when they fit.
- Otherwise construct an A to B taskflow as described.
- Return the JSON inside <taskflow> tags and do not explain it.
- Be warm and helpful.
  `,
    messages,
    tools: {}, // No tools needed for this agent
  });
}

export { agentAgent };
