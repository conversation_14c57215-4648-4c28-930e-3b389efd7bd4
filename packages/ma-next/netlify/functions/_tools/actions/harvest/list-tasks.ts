import type { HarvestTaskList, HarvestTasksInput, NangoAction } from '../models';
import { getHarvestAccountId } from './harvestHelpers';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Lists tasks from Harvest.
 *
 * @param {Object} input - The input parameters
 * @param {boolean} [input.is_active] - Optional filter for active tasks only
 * @param {string} [input.updated_since] - Optional filter for tasks updated since a specific date
 * @param {number} [input.page] - Optional page number for pagination
 * @param {number} [input.per_page] - Optional number of results per page
 * @returns {Promise<HarvestTaskList | ActionError>} The list of tasks or an error object
 */
export default async function runAction(
  nango: NangoAction,
  input?: HarvestTasksInput
): Promise<HarvestTaskList | ActionError> {
  try {
    const accountIdResult = await getHarvestAccountId(nango);

    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error('Failed to get Harvest Account ID:', accountIdResult.error.message);
      return accountIdResult;
    }
    const accountId = accountIdResult;

    const queryParams = new URLSearchParams();

    if (input?.is_active !== undefined) {
      queryParams.append('is_active', input.is_active.toString());
    }

    if (input?.updated_since) {
      queryParams.append('updated_since', input.updated_since);
    }

    if (input?.page) {
      queryParams.append('page', input.page.toString());
    }

    if (input?.per_page) {
      queryParams.append('per_page', input.per_page.toString());
    }

    const endpoint = `/v2/tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    const response = await nango.proxy({
      method: 'GET',
      endpoint,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error listing tasks from Harvest:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while listing tasks.';

    return {
      error: {
        status: status,
        message: `Failed to list tasks: ${message}`,
      },
    };
  }
}
