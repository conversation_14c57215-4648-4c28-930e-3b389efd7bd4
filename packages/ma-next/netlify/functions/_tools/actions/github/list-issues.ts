import type { GithubIssueList, GithubIssuesInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubIssuesInput
): Promise<GithubIssueList | NangoError> {
  try {
    const { owner, repo, state, sort, direction, per_page, page } = input;

    if (!owner || !repo) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner and name are required',
        },
      };
    }

    const params: Record<string, any> = {};
    if (state) params['state'] = state;
    if (sort) params['sort'] = sort;
    if (direction) params['direction'] = direction;
    if (per_page) params['per_page'] = per_page;
    if (page) params['page'] = page;

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/issues`,
      params,
    });

    return { issues: response.data };
  } catch (error: any) {
    console.error('Error fetching issues:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while fetching the issues.';
    return { error: { status, message } };
  }
}
