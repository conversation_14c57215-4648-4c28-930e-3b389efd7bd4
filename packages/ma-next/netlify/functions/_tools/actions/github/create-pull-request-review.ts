import type {
  GithubCreatePullRequestReviewInput,
  GithubPullRequestReview,
  NangoAction,
} from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubCreatePullRequestReviewInput
): Promise<GithubPullRequestReview | NangoError> {
  try {
    const { owner, repo, pullNumber, body, event, commitId, comments } = input;

    const data: { [key: string]: any } = {
      event: event,
    };

    if (body) {
      data['body'] = body;
    }

    if (commitId) {
      data['commit_id'] = commitId;
    }

    if (comments) {
      data['comments'] = comments;
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: `/repos/${owner}/${repo}/pulls/${pullNumber}/reviews`,
      data: data,
    });

    if (response.status !== 200) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to create pull request review: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return response.data;
  } catch (error: any) {
    console.error('Error creating pull request review:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while creating the pull request review.';
    return { error: { status, message } };
  }
}
