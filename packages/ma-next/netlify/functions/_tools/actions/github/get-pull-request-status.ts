import type { GithubCombinedStatus, GithubPullRequestInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubPullRequestInput
): Promise<GithubCombinedStatus | NangoError> {
  try {
    const { owner, repo, pullNumber } = input;

    const pullRequestResponse = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/pulls/${pullNumber}`,
    });

    if (pullRequestResponse.status !== 200) {
      console.error('GitHub API Error:', pullRequestResponse.status, pullRequestResponse.data);
      return {
        error: {
          status: pullRequestResponse.status,
          message: `GitHub API Error: Failed to get pull request: ${pullRequestResponse.status} ${JSON.stringify(pullRequestResponse.data)}`,
        },
      };
    }

    const pullRequest = pullRequestResponse.data;
    const headSha = pullRequest.head.sha;

    const statusResponse = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/commits/${headSha}/status`,
    });

    if (statusResponse.status !== 200) {
      console.error('GitHub API Error:', statusResponse.status, statusResponse.data);
      return {
        error: {
          status: statusResponse.status,
          message: `GitHub API Error: Failed to get combined status: ${statusResponse.status} ${JSON.stringify(statusResponse.data)}`,
        },
      };
    }

    return statusResponse.data;
  } catch (error: any) {
    console.error('Error getting pull request status:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while getting the pull request status.';
    return { error: { status, message } };
  }
}
