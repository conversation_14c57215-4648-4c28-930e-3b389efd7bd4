import type { GithubPullRequest, GithubPullRequestInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubPullRequestInput
): Promise<GithubPullRequest | NangoError> {
  try {
    const { owner, repo, pullNumber } = input;

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/pulls/${pullNumber}`,
    });

    if (response.status !== 200) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to get pull request: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return response.data;
  } catch (error: any) {
    console.error('Error getting pull request:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while getting the pull request.';
    return { error: { status, message } };
  }
}
