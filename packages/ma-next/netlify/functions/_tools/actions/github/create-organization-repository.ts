import type {
  GithubCreateOrganizationRepositoryInput,
  GithubRepository,
  NangoAction,
} from '../models';

interface ActionError {
  error: {
    status: number;
    message: string;
    details?: any;
  };
}

export default async function runAction(
  nango: NangoAction,
  input: GithubCreateOrganizationRepositoryInput
): Promise<GithubRepository | ActionError> {
  const { org, ...repoDetails } = input;

  if (repoDetails.private === undefined) {
    repoDetails.private = true;
  }

  const endpoint = `/orgs/${org}/repos`;

  try {
    const response = await nango.proxy({
      method: 'POST',
      endpoint: endpoint,
      data: repoDetails,
      headers: {
        Accept: 'application/vnd.github+json',
        'X-GitHub-Api-Version': '2022-11-28',
      },
      retries: 1,
    });

    return response.data as GithubRepository;
  } catch (error: any) {
    console.error('Error in create-organization-repository action:', error);

    const status = error?.response?.status || 500;
    let message = 'An unexpected error occurred while creating the organization repository.';
    let details = undefined;

    if (error?.response?.data?.message) {
      message = error.response.data.message;
      details = error.response.data.errors;
    } else if (error.message) {
      message = error.message;
    }

    return {
      error: {
        status: status,
        message: message,
        details: details,
      },
    };
  }
}
