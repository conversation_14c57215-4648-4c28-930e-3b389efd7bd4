import type { GithubRepository, GithubRepositoryInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubRepositoryInput
): Promise<GithubRepository | NangoError> {
  try {
    const { owner, repo } = input;

    if (!owner || !repo) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner and name are required',
        },
      };
    }

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}`,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error fetching repository:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while fetching the repository.';
    return { error: { status, message } };
  }
}
