import type { GithubIssue, GithubUpdateIssueInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubUpdateIssueInput
): Promise<GithubIssue | NangoError> {
  try {
    const { owner, repo, issue_number, title, body, state, assignees, labels } = input;

    if (!owner || !repo || !issue_number) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner, name, and issue number are required',
        },
      };
    }

    const payload: Record<string, any> = {};

    if (title !== undefined) payload['title'] = title;
    if (body !== undefined) payload['body'] = body;
    if (state !== undefined) payload['state'] = state;
    if (assignees !== undefined) payload['assignees'] = assignees;
    if (labels !== undefined) payload['labels'] = labels;

    const response = await nango.proxy({
      method: 'PATCH',
      endpoint: `/repos/${owner}/${repo}/issues/${issue_number}`,
      data: payload,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error updating issue:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while updating the issue.';
    return { error: { status, message } };
  }
}
