import type { GithubRepositoryList, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  _input?: void
): Promise<GithubRepositoryList | NangoError> {
  try {
    const response = await nango.proxy({
      method: 'GET',
      endpoint: '/user/repos',
      params: {
        sort: 'updated',
        per_page: 100,
      },
    });

    const fullSet = new Set(response.data.map((org: any) => org.login));

    const returnData = {
      note:
        fullSet.size === 1
          ? 'Ensure relevant organisations have access to the MakeAgent application to include their repos in this response - https://github.com/settings/applications'
          : null,
      repositories: response.data,
    };

    return returnData;
  } catch (error: any) {
    console.error('Error fetching repositories:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while fetching the repositories.';
    return { error: { status, message } };
  }
}
