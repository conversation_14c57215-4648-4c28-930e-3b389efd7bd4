import type {
  GithubBranchUp<PERSON><PERSON><PERSON><PERSON>,
  GithubUpdatePullRequestBranchInput,
  NangoAction,
} from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: <PERSON>goAction,
  input: GithubUpdatePullRequestBranchInput
): Promise<GithubBranchUpdateResult | NangoError> {
  try {
    const { owner, repo, pullNumber, expectedHeadSha } = input;

    const data: { [key: string]: any } = {};

    if (expectedHeadSha) {
      data['expected_head_sha'] = expectedHeadSha;
    }

    const response = await nango.proxy({
      method: 'PUT',
      endpoint: `/repos/${owner}/${repo}/pulls/${pullNumber}/update-branch`,
      data: data,
    });

    if (response.status !== 202) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to update pull request branch: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return response.data;
  } catch (error: any) {
    console.error('Error updating pull request branch:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while updating the pull request branch.';
    return { error: { status, message } };
  }
}
