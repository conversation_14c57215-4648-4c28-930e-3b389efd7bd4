import type { GithubCreatePullRequestInput, GithubPullRequest, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubCreatePullRequestInput
): Promise<GithubPullRequest | NangoError> {
  try {
    const { owner, repo, title, head, base, body, draft, maintainer_can_modify } = input;

    const data: { [key: string]: any } = {
      title: title,
      head: head,
      base: base,
    };

    if (body) {
      data['body'] = body;
    }

    if (draft) {
      data['draft'] = draft;
    }

    if (maintainer_can_modify) {
      data['maintainer_can_modify'] = maintainer_can_modify;
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: `/repos/${owner}/${repo}/pulls`,
      data: data,
    });

    if (response.status !== 201) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to create pull request: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return response.data;
  } catch (error: any) {
    console.error('Error creating pull request:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while creating the pull request.';
    return { error: { status, message } };
  }
}
