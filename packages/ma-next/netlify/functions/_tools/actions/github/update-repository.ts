import type { GithubRepository, GithubUpdateRepositoryInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubUpdateRepositoryInput
): Promise<GithubRepository | NangoError> {
  try {
    const {
      owner,
      repo,
      name,
      description,
      private: isPrivate,
      has_issues,
      has_projects,
      has_wiki,
      default_branch,
    } = input;

    if (!owner || !repo) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner and name are required',
        },
      };
    }

    const payload: Record<string, any> = {};

    if (name !== undefined) payload['name'] = name;
    if (description !== undefined) payload['description'] = description;
    if (isPrivate !== undefined) payload['private'] = isPrivate;
    if (has_issues !== undefined) payload['has_issues'] = has_issues;
    if (has_projects !== undefined) payload['has_projects'] = has_projects;
    if (has_wiki !== undefined) payload['has_wiki'] = has_wiki;
    if (default_branch !== undefined) payload['default_branch'] = default_branch;

    const response = await nango.proxy({
      method: 'PATCH',
      endpoint: `/repos/${owner}/${repo}`,
      data: payload,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error updating repository:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while updating the repository.';
    return { error: { status, message } };
  }
}
