import type { GithubMergePullRequestInput, GithubMergeResult, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubMergePullRequestInput
): Promise<GithubMergeResult | NangoError> {
  try {
    const { owner, repo, pullNumber, commit_title, commit_message, merge_method } = input;

    const data: { [key: string]: any } = {};

    if (commit_title) {
      data['commit_title'] = commit_title;
    }
    if (commit_message) {
      data['commit_message'] = commit_message;
    }
    if (merge_method) {
      data['merge_method'] = merge_method;
    }

    const response = await nango.proxy({
      method: 'PUT',
      endpoint: `/repos/${owner}/${repo}/pulls/${pullNumber}/merge`,
      data: data,
    });

    if (response.status !== 200) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to merge pull request: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return response.data;
  } catch (error: any) {
    console.error('Error merging pull request:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while merging the pull request.';
    return { error: { status, message } };
  }
}
