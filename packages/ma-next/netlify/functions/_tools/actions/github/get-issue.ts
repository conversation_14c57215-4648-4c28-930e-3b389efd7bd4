import type { GithubIssue, GithubIssueInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubIssueInput
): Promise<GithubIssue | NangoError> {
  try {
    const { owner, repo, issue_number } = input;

    if (!owner || !repo || !issue_number) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner, name, and issue number are required',
        },
      };
    }

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/issues/${issue_number}`,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error fetching issue:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while fetching the issue.';
    return { error: { status, message } };
  }
}
