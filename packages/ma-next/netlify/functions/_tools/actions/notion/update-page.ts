import type { NangoAction, NotionUpdatePageInput, NotionPageOrDatabase } from '../models';

const NOTION_API_VERSION = '2022-06-28';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: NotionUpdatePageInput
): Promise<NotionPageOrDatabase | ErrorResponse> {
  const { pageId, properties, archived, icon, cover } = input;

  if (!pageId) {
    return { error: { status: 400, message: 'Page ID (pageId) is required.' } };
  }

  const requestBody: Record<string, any> = {};
  if (properties !== undefined) requestBody['properties'] = properties;
  if (archived !== undefined) requestBody['archived'] = archived;
  if (icon !== undefined) requestBody['icon'] = icon;
  if (cover !== undefined) requestBody['cover'] = cover;
  if (Object.keys(requestBody).length === 0) {
    return {
      error: {
        status: 400,
        message:
          'At least one property (properties, archived, icon, cover) must be provided to update the page.',
      },
    };
  }

  try {
    const config = {
      method: 'PATCH' as const,
      endpoint: `/v1/pages/${pageId}`,
      data: requestBody,
      headers: {
        'Content-Type': 'application/json',
        'Notion-Version': NOTION_API_VERSION,
      },
      retries: 3,
    };

    const response = await nango.proxy<NotionPageOrDatabase>(config);

    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      `Unknown error updating Notion page ${pageId}`;
    const status = error.response?.status ?? 500;
    console.error(`Error updating Notion page ${pageId}: Status ${status}, Message: ${errorMessage}`);
    return { error: { status: status, message: errorMessage } };
  }
}
