import type {
  NangoAction,
  NotionQueryDatabaseInput,
  NotionQueryDatabaseOutput,
} from '../models';

const NOTION_API_VERSION = '2022-06-28';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: NotionQueryDatabaseInput
): Promise<NotionQueryDatabaseOutput | ErrorResponse> {
  const { databaseId, filter, sorts, start_cursor, page_size } = input;

  if (!databaseId) {
    return { error: { status: 400, message: 'Database ID is required.' } };
  }

  const requestBody: Record<string, any> = {};
  if (filter !== undefined) requestBody['filter'] = filter;
  if (sorts !== undefined) requestBody['sorts'] = sorts;
  if (start_cursor !== undefined) requestBody['start_cursor'] = start_cursor;
  if (page_size !== undefined) requestBody['page_size'] = page_size;

  try {
    const config = {
      method: 'POST' as const,
      endpoint: `/v1/databases/${databaseId}/query`,
      data: requestBody,
      headers: {
        'Content-Type': 'application/json',
        'Notion-Version': NOTION_API_VERSION,
      },
      retries: 3,
    };

    const response = await nango.proxy<NotionQueryDatabaseOutput>(config);

    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      `Unknown error querying Notion database ${databaseId}`;
    const status = error.response?.status ?? 500;
    console.error(`Error querying Notion database ${databaseId}: Status ${status}, Message: ${errorMessage}`);
    return { error: { status: status, message: errorMessage } };
  }
}
