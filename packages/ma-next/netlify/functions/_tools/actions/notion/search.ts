import type { NangoAction, NotionSearchInput, NotionSearchOutput } from '../models';

const NOTION_API_VERSION = '2022-06-28';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: NotionSearchInput = {}
): Promise<NotionSearchOutput | ErrorResponse> {
  const { query: inputQuery, sort, filter, start_cursor, page_size } = input;
  const query = inputQuery ?? '';
  const requestBody: Record<string, any> = {};
  requestBody['query'] = query;
  if (sort !== undefined) requestBody['sort'] = sort;
  if (filter !== undefined) requestBody['filter'] = filter;
  if (start_cursor !== undefined) requestBody['start_cursor'] = start_cursor;
  if (page_size !== undefined) requestBody['page_size'] = page_size;

  try {
    const config = {
      method: 'POST' as const,
      endpoint: '/v1/search',
      data: requestBody,
      headers: {
        'Content-Type': 'application/json',
        'Notion-Version': NOTION_API_VERSION,
      },
      retries: 3,
    };

    const response = await nango.proxy<NotionSearchOutput>(config);

    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message || error.message || 'Unknown error searching Notion';
    const status = error.response?.status ?? 500;
    console.error(`Error searching Notion: Status ${status}, Message: ${errorMessage}`);
    return { error: { status: status, message: errorMessage } };
  }
}
