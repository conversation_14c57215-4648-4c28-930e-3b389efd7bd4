import type { NangoAction, NotionGetPageInput, NotionPageOrDatabase } from '../models';

const NOTION_API_VERSION = '2022-06-28';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: NotionGetPageInput
): Promise<NotionPageOrDatabase | ErrorResponse> {
  const { pageId } = input;

  if (!pageId) {
    return { error: { status: 400, message: 'Page ID is required.' } };
  }

  try {
    const config = {
      method: 'GET' as const,
      endpoint: `/v1/pages/${pageId}`,
      headers: {
        'Notion-Version': NOTION_API_VERSION,
      },
      retries: 3,
    };

    const response = await nango.proxy<NotionPageOrDatabase>(config);

    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      `Unknown error getting Notion page ${pageId}`;
    const status = error.response?.status ?? 500;
    console.error(`Error getting Notion page ${pageId}: Status ${status}, Message: ${errorMessage}`);
    return { error: { status: status, message: errorMessage } };
  }
}
