import type {
  NangoA<PERSON>,
  GoogleSheetUpdateOutput,
  GoogleSheetUpdateInput,
  SheetUpdate,
} from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GoogleSheetUpdateInput | any
): Promise<GoogleSheetUpdateOutput | NangoError> {
  let spreadsheetId: string;
  let updates: SheetUpdate[];

  if (input.spreadsheetId && input.updates) {
    spreadsheetId = input.spreadsheetId;
    updates = input.updates;
  } else if (input.range && input.values) {
    const rangeParts = input.range.split('!');
    const sheetName = rangeParts[0];
    const range = rangeParts.length > 1 ? rangeParts[1] : 'A1';

    spreadsheetId = input.spreadsheetId || '';
    updates = [
      {
        sheetName,
        range,
        data: {
          rows: input.values.map((row: any[]) => ({ cells: row })),
        },
      },
    ];
  } else {
    spreadsheetId = input.spreadsheetId;
    updates = input.updates;
  }

  try {
    if (!spreadsheetId) {
      return {
        error: { status: 400, message: 'Input validation failed: Spreadsheet ID is required' },
      };
    }
    if (!updates || !Array.isArray(updates) || updates.length === 0) {
      return {
        error: { status: 400, message: 'Input validation failed: At least one update is required' },
      };
    }

    for (const update of updates) {
      if (!update.data) {
        return {
          error: {
            status: 400,
            message: 'Input validation failed: Each update must have a data property',
          },
        };
      }
      if (!update.data.rows || !Array.isArray(update.data.rows)) {
        const anyUpdate = update as any;
        if (anyUpdate.values && Array.isArray(anyUpdate.values)) {
          update.data = {
            rows: anyUpdate.values.map((row: any[]) => ({ cells: row })),
          };
        } else {
          return {
            error: {
              status: 400,
              message:
                'Input validation failed: Each update must have a data.rows array or a values array',
            },
          };
        }
      }
    }

    let totalUpdatedRows = 0;
    let totalUpdatedColumns = 0;
    let totalUpdatedCells = 0;
    const updatedRanges: string[] = [];

    for (const update of updates) {
      try {
        let sheetId = update.sheetId;
        const range = update.range;

        if (!sheetId && update.sheetName) {
          const sheetsResponse = await nango.proxy({
            method: 'GET',
            endpoint: `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}`,
            retries: 3,
          });

          const sheets = sheetsResponse.data.sheets || [];
          const targetSheet = sheets.find((s: any) => s.properties.title === update.sheetName);

          if (!targetSheet) {
            return {
              error: {
                status: 400,
                message: `Input validation failed: Sheet with name '${update.sheetName}' not found`,
              },
            };
          }

          sheetId = targetSheet.properties.sheetId;
        }

        if (!update.data || !update.data.rows || !Array.isArray(update.data.rows)) {
          return {
            error: {
              status: 400,
              message: 'Failed to update sheet: Missing or invalid data structure',
            },
          };
        }

        const rows = update.data.rows.map((row: any) => {
          if (!row.cells || !Array.isArray(row.cells)) {
            throw new Error('Each row must have a cells array');
          }

          return {
            values: row.cells.map((cell: any) => ({
              userEnteredValue: {
                stringValue: cell ? cell.toString() : '',
              },
            })),
          };
        });

        if (range) {
          const values = update.data.rows.map(row => {
            if (!row.cells || !Array.isArray(row.cells)) {
              return [];
            }
            return row.cells;
          });

          const valuesResponse = await nango.proxy({
            method: 'PUT',
            endpoint: `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}`,
            params: {
              valueInputOption: 'USER_ENTERED',
            },
            data: {
              values: values,
            },
            retries: 3,
          });

          updatedRanges.push(valuesResponse.data.updatedRange);
          totalUpdatedRows += valuesResponse.data.updatedRows || 0;
          totalUpdatedColumns += valuesResponse.data.updatedColumns || 0;
          totalUpdatedCells += valuesResponse.data.updatedCells || 0;
        } else {
          const startRow = update.startRow || 0;
          const startColumn = update.startColumn || 0;

          await nango.proxy({
            method: 'POST',
            endpoint: `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}:batchUpdate`,
            data: {
              requests: [
                {
                  updateCells: {
                    start: {
                      sheetId: sheetId,
                      rowIndex: startRow,
                      columnIndex: startColumn,
                    },
                    rows: rows,
                    fields: 'userEnteredValue',
                  },
                },
              ],
            },
            retries: 3,
          });

          const numRows = update.data.rows.length;
          const numCols =
            update.data.rows.length > 0
              ? Math.max(
                  ...update.data.rows.map((row: any) =>
                    row.cells && Array.isArray(row.cells) ? row.cells.length : 0
                  )
                )
              : 0;

          const colToA1 = (col: number): string => {
            let a1 = '';
            while (col >= 0) {
              a1 = String.fromCharCode((col % 26) + 65) + a1;
              col = Math.floor(col / 26) - 1;
            }
            return a1;
          };

          const sheetName = update.sheetName || `Sheet${sheetId}`;
          const rangeA1 = `${sheetName}!${colToA1(startColumn)}${startRow + 1}:${colToA1(startColumn + numCols - 1)}${startRow + numRows}`;

          updatedRanges.push(rangeA1);
          totalUpdatedRows += numRows;
          totalUpdatedColumns += numCols;
          totalUpdatedCells += numRows * numCols;
        }
      } catch (error: any) {
        return { error: { status: 500, message: `Failed to update sheet: ${error.message}` } };
      }
    }

    return {
      spreadsheetId,
      updatedRange: updatedRanges.join(', '),
      updatedRows: totalUpdatedRows,
      updatedColumns: totalUpdatedColumns,
      updatedCells: totalUpdatedCells,
    };
  } catch (error: any) {
    console.error('Error updating sheet:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while updating the sheet.';
    return { error: { status, message } };
  }
}
