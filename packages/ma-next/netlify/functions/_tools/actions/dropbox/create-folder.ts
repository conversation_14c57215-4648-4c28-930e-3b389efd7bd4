import type { DropboxCreateFolderInput, DropboxFolder, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: DropboxCreateFolderInput
): Promise<DropboxFolder | NangoError> {
  try {
    const { path, autorename = false } = input;

    if (!path) {
      return { error: { status: 400, message: 'Input validation failed: Path is required' } };
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/2/files/create_folder_v2',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        path,
        autorename,
      },
      retries: 3,
    });

    const folderMetadata = response.data.metadata;

    return {
      id: folderMetadata.id,
      name: folderMetadata.name,
      path_display: folderMetadata.path_display,
      path_lower: folderMetadata.path_lower,
    };
  } catch (error: any) {
    console.error('Error creating folder in Dropbox:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error_summary ||
      error?.message ||
      'An unknown error occurred while creating the folder in Dropbox.';
    return { error: { status, message } };
  }
}
