import type { <PERSON>goAction, GmailDeleteMessageOutput, GmailMessageIdInput } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailMessageIdInput
): Promise<GmailDeleteMessageOutput | NangoError> {
  const { messageId } = input;

  if (!messageId) {
    return {
      error: {
        status: 400,
        message: 'Input validation failed: Message ID is required to delete a message.',
      },
    };
  }

  try {
    await nango.proxy({
      method: 'DELETE',
      endpoint: `/gmail/v1/users/me/messages/${messageId}`,
    });

    return { success: true };
  } catch (error: any) {
    console.error('Failed to delete message:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while deleting the message.';
    return { error: { status, message } };
  }
}
