import type { NangoAction, GmailMessage, GmailMessageIdInput } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailMessageIdInput
): Promise<GmailMessage | NangoError> {
  const { messageId } = input;

  try {
    if (!messageId) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Message ID is required to trash a message.',
        },
      };
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: `/gmail/v1/users/me/messages/${messageId}/trash`,
    });

    return response.data as GmailMessage;
  } catch (error: any) {
    console.error('Failed to trash message:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while trashing the message.';
    return { error: { status, message } };
  }
}
