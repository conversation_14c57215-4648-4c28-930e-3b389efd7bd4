import type { NangoAction, GmailMessage, GmailGetMessageInput } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

interface Attachments {
  filename: string;
  mimeType: string;
  size: number;
  attachmentId: string;
}

interface Schema$MessagePart {
  partId?: string | null;
  mimeType?: string | null;
  filename?: string | null;
  headers?: Array<{ name?: string | null; value?: string | null }> | null;
  body?: {
    attachmentId?: string | null;
    size?: number | null;
    data?: string | null;
  } | null;
  parts?: Schema$MessagePart[] | null;
}

function processParts(
  parts: Schema$MessagePart[],
  bodyObj: { body: string; mimeType?: string },
  attachments: Attachments[]
): void {
  let foundBody = false;
  for (const part of parts) {
    if (part.mimeType === 'text/plain' && part.body?.data) {
      bodyObj.body = Buffer.from(part.body.data, 'base64').toString('utf8');
      bodyObj.mimeType = part.mimeType;
      foundBody = true;
      break;
    }
    if (part.parts?.length) {
      processParts(part.parts, bodyObj, attachments);
      if (bodyObj.body && bodyObj.mimeType === 'text/plain') {
        foundBody = true;
        break;
      }
    }
  }

  if (!foundBody) {
    for (const part of parts) {
      if (part.mimeType === 'text/html' && part.body?.data && !bodyObj.body) {
        bodyObj.body = Buffer.from(part.body.data, 'base64').toString('utf8');
        bodyObj.mimeType = part.mimeType;
      } else if (part.filename && part.body?.attachmentId) {
        if (part.mimeType && part.body?.size !== undefined && part.body?.size !== null) {
          attachments.push({
            filename: part.filename,
            mimeType: part.mimeType,
            size: part.body.size,
            attachmentId: part.body.attachmentId,
          });
        }
      }
      if (part.parts?.length) {
        processParts(part.parts, bodyObj, attachments);
      }
    }
  } else {
    for (const part of parts) {
      if (part.filename && part.body?.attachmentId) {
        if (part.mimeType && part.body?.size !== undefined && part.body?.size !== null) {
          attachments.push({
            filename: part.filename,
            mimeType: part.mimeType,
            size: part.body.size,
            attachmentId: part.body.attachmentId,
          });
        }
      }
      if (part.parts?.length) {
        processParts(part.parts, bodyObj, attachments);
      }
    }
  }
}

export default async function runAction(
  nango: NangoAction,
  input: GmailGetMessageInput
): Promise<GmailMessage | NangoError> {
  try {
    const { id, format = 'full' } = input;
    if (!id) {
      return { error: { status: 400, message: 'Input validation failed: Message ID is required' } };
    }

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/gmail/v1/users/me/messages/${id}`,
      params: { format },
      retries: 3,
    });

    const message = response.data;

    const headers = message.payload?.headers || [];

    const bodyObj = { body: '', mimeType: message.payload?.mimeType || undefined };
    const attachments: Attachments[] = [];

    if (message.payload?.parts?.length) {
      processParts(message.payload.parts, bodyObj, attachments);
    } else if (message.payload?.body?.data) {
      try {
        bodyObj.body = Buffer.from(message.payload.body.data, 'base64').toString('utf-8');
      } catch (e) {
        console.warn(`Could not decode simple message body for ID ${id}:`, e);
        bodyObj.body = '';
        bodyObj.mimeType = undefined;
      }
    }

    const topLevelFilename = message.payload?.filename || undefined;

    return {
      id: message.id,
      threadId: message.threadId,
      labelIds: message.labelIds || [],
      snippet: message.snippet || '',
      payload: message.payload,
      sizeEstimate: message.sizeEstimate,
      historyId: message.historyId,
      internalDate: message.internalDate,
      headers: headers,
      body: bodyObj.body,
      mimeType: bodyObj.mimeType,
      filename: topLevelFilename,
      attachments: attachments,
    };
  } catch (error: any) {
    console.error('Error fetching Gmail message:', error);
    const status = error?.response?.status || 500;
    const message = error?.response?.data?.error?.message || error.message || 'Unknown error';
    return { error: { status, message } };
  }
}
