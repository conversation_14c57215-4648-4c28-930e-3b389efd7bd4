import type {
  GmailBasicMessageDetails,
  GmailListMessagesInput,
  GmailMessageList,
  NangoAction,
} from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailListMessagesInput = {}
): Promise<GmailMessageList | NangoError> {
  try {
    const { maxResults = 10, labelIds, q = '-in:drafts', pageToken } = input;

    const listParams: Record<string, any> = {
      maxResults,
    };

    if (labelIds && labelIds.length > 0) {
      listParams['labelIds'] = labelIds.join(',');
    }

    if (q) {
      listParams['q'] = q;
    }

    if (pageToken) {
      listParams['pageToken'] = pageToken;
    }

    const listResponse = await nango.proxy({
      method: 'GET',
      endpoint: '/gmail/v1/users/me/messages',
      params: listParams,
      retries: 3,
    });

    if (!listResponse.data.messages) {
      return { messages: [] };
    }

    const messageIds: { id: string; threadId: string }[] = listResponse.data.messages;
    const fetchDetailsPromises = messageIds.map(async msgId => {
      try {
        const messageResponse = await nango.proxy({
          method: 'GET',
          endpoint: `/gmail/v1/users/me/messages/${msgId.id}`,
          params: { format: 'metadata' }, // Request only metadata
          retries: 3,
        });

        const headers = messageResponse.data.payload?.headers || [];
        const subject = headers.find((h: any) => h.name === 'Subject')?.value || '';
        const date = headers.find((h: any) => h.name === 'Date')?.value || '';

        return {
          id: msgId.id,
          threadId: msgId.threadId,
          labelIds: messageResponse.data.labelIds || [],
          snippet: messageResponse.data.snippet || '',
          subject,
          date,
        } as GmailBasicMessageDetails;
      } catch (messageError: any) {
        console.error(`Error fetching details for message ID ${msgId.id}:`, messageError);
        // Return null or a specific error object to indicate failure for this message
        return null;
      }
    });

    const results = await Promise.allSettled(fetchDetailsPromises);

    const basicMessageDetails: GmailBasicMessageDetails[] = results
      .filter(result => result.status === 'fulfilled' && result.value !== null)
      .map(result => (result as PromiseFulfilledResult<GmailBasicMessageDetails>).value);

    return {
      messages: basicMessageDetails,
      nextPageToken: listResponse.data.nextPageToken,
    };
  } catch (error: any) {
    console.error('Error listing Gmail messages:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while listing Gmail messages.';
    return { error: { status, message } };
  }
}
