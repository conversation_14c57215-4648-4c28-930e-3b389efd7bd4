import type {
  GmailMessageList,
  GmailMessageMetadata,
  GmailSearchMessagesInput,
  NangoAction,
} from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailSearchMessagesInput
): Promise<GmailMessageList | NangoError> {
  try {
    const { maxResults = 10, labelIds, pageToken } = input;
    let { q } = input;

    // @ts-ignore
    if (input.query) {
      // @ts-ignore
      q = input.query;
    }

    if (!q) {
      return {
        error: { status: 400, message: 'Input validation failed: Search query is required' },
      };
    }

    const params: Record<string, any> = {
      q,
      maxResults,
    };

    if (labelIds && labelIds.length > 0) {
      params['labelIds'] = labelIds.join(',');
    }

    if (pageToken) {
      params['pageToken'] = pageToken;
    }

    const response = await nango.proxy({
      method: 'GET',
      endpoint: '/gmail/v1/users/me/messages',
      params,
      retries: 3,
    });

    if (!response.data.messages) {
      return { messages: [] };
    }

    const messages: GmailMessageMetadata[] = response.data.messages.map((msg: any) => ({
      id: msg.id,
      threadId: msg.threadId,
      labelIds: msg.labelIds || [],
      snippet: msg.snippet || '',
    }));

    return {
      messages,
      nextPageToken: response.data.nextPageToken,
    };
  } catch (error: any) {
    console.error('Error searching Gmail messages:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while searching Gmail messages.';
    return { error: { status, message } };
  }
}
