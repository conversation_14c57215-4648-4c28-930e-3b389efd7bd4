import type { NangoAction, LinearCreateIssueInput, LinearIssue } from '../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: LinearCreateIssueInput
): Promise<LinearIssue | ActionError> {
  try {
    if (!input.teamId || !input.title) {
      return {
        error: {
          status: 400,
          message: 'Missing required parameters: teamId and title are required',
        },
      };
    }

    const createInput: Record<string, any> = {
      teamId: input.teamId,
      title: input.title,
    };

    if (input.description) createInput['description'] = input.description;
    if (input.stateId) createInput['stateId'] = input.stateId;
    if (input.assigneeId) createInput['assigneeId'] = input.assigneeId;
    if (input.priority !== undefined) createInput['priority'] = input.priority;
    if (input.projectId) createInput['projectId'] = input.projectId;
    if (input.labelIds && input.labelIds.length > 0) createInput['labelIds'] = input.labelIds;

    const mutation = `
            mutation CreateIssue($input: IssueCreateInput!) {
                issueCreate(input: $input) {
                    issue {
                        id
                        title
                        description
                        number
                        priority
                        url
                        createdAt
                        updatedAt
                        state {
                            id
                            name
                            color
                            type
                        }
                        assignee {
                            id
                            name
                            email
                            avatarUrl
                        }
                        team {
                            id
                            name
                            key
                        }
                        project {
                            id
                            name
                            icon
                            color
                        }
                        labels {
                            nodes {
                                id
                                name
                                color
                            }
                        }
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query: mutation,
        variables: {
          input: createInput,
        },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error creating issue in Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (response.status >= 400 || !response.data?.data?.issueCreate?.issue) {
      const status = response.status || 500;
      const message = `Failed to create issue. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error creating issue in Linear:', message);
      return { error: { status, message } };
    }

    const issue = response.data.data.issueCreate.issue;

    const result: LinearIssue = {
      id: issue.id,
      title: issue.title,
      description: issue.description || '',
      number: issue.number,
      priority: issue.priority,
      url: issue.url,
      createdAt: issue.createdAt,
      updatedAt: issue.updatedAt,
      state: {
        id: issue.state.id,
        name: issue.state.name,
        color: issue.state.color,
        type: issue.state.type,
      },
      team: {
        id: issue.team.id,
        name: issue.team.name,
        key: issue.team.key,
      },
      labels:
        issue.labels?.nodes.map((label: any) => ({
          id: label.id,
          name: label.name,
          color: label.color,
        })) || [],
    };

    if (issue.assignee) {
      result.assignee = {
        id: issue.assignee.id,
        name: issue.assignee.name,
        email: issue.assignee.email,
      };
    }

    if (issue.project) {
      result.project = {
        id: issue.project.id,
        name: issue.project.name,
        color: issue.project.color,
      };
    }

    return result;
  } catch (error: any) {
    console.error('Error creating issue in Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while creating the issue.';

    return {
      error: {
        status: status,
        message: `Failed to create issue: ${message}`,
      },
    };
  }
}
