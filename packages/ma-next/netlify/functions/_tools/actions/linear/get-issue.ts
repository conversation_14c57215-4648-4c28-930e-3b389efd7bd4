import type { NangoAction, LinearIssueInput, LinearIssue } from '../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: LinearIssueInput
): Promise<LinearIssue | ActionError> {
  try {
    if (!input.issueId) {
      return {
        error: {
          status: 400,
          message: 'Missing required parameter: issueId is required',
        },
      };
    }

    const query = `
            query GetIssue($issueId: String!) {
                issue(id: $issueId) {
                    id
                    title
                    description
                    number
                    priority
                    url
                    createdAt
                    updatedAt
                    state {
                        id
                        name
                        color
                        type
                    }
                    assignee {
                        id
                        name
                        email
                        avatarUrl
                    }
                    team {
                        id
                        name
                        key
                    }
                    project {
                        id
                        name
                        icon
                        color
                    }
                    labels {
                        nodes {
                            id
                            name
                            color
                        }
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query,
        variables: { issueId: input.issueId },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error fetching issue from Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.issue) {
      const status = response.status === 200 ? 404 : response.status || 404;
      const message = `Issue with ID ${input.issueId} not found.`;
      console.error('Error fetching issue from Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to fetch issue. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error fetching issue from Linear:', message);
      return { error: { status, message } };
    }

    const issue = response.data.data.issue;

    const result: LinearIssue = {
      id: issue.id,
      title: issue.title,
      description: issue.description || '',
      number: issue.number,
      priority: issue.priority,
      url: issue.url,
      createdAt: issue.createdAt,
      updatedAt: issue.updatedAt,
      state: {
        id: issue.state.id,
        name: issue.state.name,
        color: issue.state.color,
        type: issue.state.type,
      },
      team: {
        id: issue.team.id,
        name: issue.team.name,
        key: issue.team.key,
      },
      labels:
        issue.labels?.nodes.map((label: any) => ({
          id: label.id,
          name: label.name,
          color: label.color,
        })) || [],
    };

    if (issue.assignee) {
      result.assignee = {
        id: issue.assignee.id,
        name: issue.assignee.name,
        email: issue.assignee.email,
        avatarUrl: issue.assignee.avatarUrl,
      };
    }

    if (issue.project) {
      result.project = {
        id: issue.project.id,
        name: issue.project.name,
        icon: issue.project.icon,
        color: issue.project.color,
      };
    }

    return result;
  } catch (error: any) {
    console.error('Error fetching issue from Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching the issue.';

    return {
      error: {
        status: status,
        message: `Failed to fetch issue: ${message}`,
      },
    };
  }
}
