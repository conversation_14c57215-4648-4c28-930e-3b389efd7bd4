import type { NangoAction, LinearIssuesInput, LinearIssueList } from '../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Lists issues from Linear with optional filtering and pagination.
 *
 * @param {NangoAction} nango - The Nango action object
 * @param {LinearIssuesInput} input - Optional filter parameters
 * @returns {Promise<LinearIssueList | ActionError>} The list of issues or an error object
 */
export default async function runAction(
  nango: NangoAction,
  input?: LinearIssuesInput
): Promise<LinearIssueList | ActionError> {
  try {
    const first = input?.first || input?.limit || 10;
    const after = input?.after ? `, after: "${input.after}"` : '';

    const filterParts: string[] = [];

    if (input?.teamId) {
      filterParts.push(`team: { id: { eq: "${input.teamId}" } }`);
    }

    if (input?.projectId) {
      filterParts.push(`project: { id: { eq: "${input.projectId}" } }`);
    }

    if (input?.assigneeId) {
      filterParts.push(`assignee: { id: { eq: "${input.assigneeId}" } }`);
    }

    if (input?.priority !== undefined) {
      filterParts.push(`priority: { eq: ${input.priority} }`);
    }

    if (input?.states && input.states.length > 0) {
      const stateIds = input.states.map(id => `"${id}"`).join(', ');
      filterParts.push(`state: { id: { in: [${stateIds}] } }`);
    }

    let orderBy = 'createdAt';
    if (input?.sortBy) {
      orderBy = input.sortBy;
    }

    let query = `
            query ListIssues {
                issues(first: ${first}${after}`;

    if (filterParts.length > 0) {
      query += `, filter: { ${filterParts.join(', ')} }`;
    }

    query += `, orderBy: ${orderBy}`;
    query += `) {
                    nodes {
                        id title description number priority url createdAt updatedAt
                        state { id name color type }
                        assignee { id name email }
                        team { id name key }
                        project { id name }
                        labels { nodes { id name } }
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: { query },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error fetching issues from Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.issues?.nodes || !response.data?.data?.issues?.pageInfo) {
      const status = response.status === 200 ? 500 : response.status || 500;
      const message = `Failed to fetch issues: Invalid response structure. Response: ${JSON.stringify(response.data)}`;
      console.error('Error fetching issues from Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to fetch issues. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error fetching issues from Linear:', message);
      return { error: { status, message } };
    }

    const issues = response.data.data.issues.nodes.map((issue: any) => {
      return {
        id: issue.id,
        title: issue.title,
        description: issue.description,
        number: issue.number,
        priority: issue.priority,
        url: issue.url,
        createdAt: issue.createdAt,
        updatedAt: issue.updatedAt,
        state: issue.state,
        assignee: issue.assignee || undefined,
        team: issue.team,
        project: issue.project || undefined,
        labels: issue.labels?.nodes || [],
      };
    });

    const pageInfo = response.data.data.issues.pageInfo;

    return {
      issues,
      pageInfo: {
        hasNextPage: pageInfo.hasNextPage,
        endCursor: pageInfo.endCursor || undefined,
      },
    };
  } catch (error: any) {
    console.error('Error fetching issues from Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching issues.';

    return {
      error: {
        status: status,
        message: `Failed to fetch issues: ${message}`,
      },
    };
  }
}
