import type { NangoAction, LinearProject, LinearUpdateProjectInput } from '../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: LinearUpdateProjectInput
): Promise<LinearProject | ActionError> {
  try {
    const { projectId, name, description, color, state, teamIds } = input;

    if (!projectId) {
      return { error: { status: 400, message: 'Project ID is required' } };
    }

    const updateInput: any = {};

    if (name !== undefined) updateInput.name = name;
    if (description !== undefined) updateInput.description = description;
    if (color !== undefined) updateInput.color = color;
    if (state !== undefined) updateInput.state = state;
    if (teamIds !== undefined) updateInput.teamIds = teamIds;

    if (Object.keys(updateInput).length === 0) {
      return {
        error: {
          status: 400,
          message:
            'At least one field to update is required (name, description, color, state, teamIds)',
        },
      };
    }

    const mutation = `
            mutation UpdateProject($id: String!, $input: ProjectUpdateInput!) {
                projectUpdate(id: $id, input: $input) {
                    success
                    project {
                        id name description url color state url createdAt updatedAt
                        teams { nodes { id name key } }
                        lead { id name email }
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query: mutation,
        variables: { id: projectId, input: updateInput },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error updating project in Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (
      !response.data?.data?.projectUpdate?.success ||
      !response.data?.data?.projectUpdate?.project
    ) {
      const status = response.status || 500;
      const message = `Failed to update project (success: ${response.data?.data?.projectUpdate?.success}). Response: ${JSON.stringify(response.data)}`;
      console.error('Error updating project in Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to update project. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error updating project in Linear:', message);
      return { error: { status, message } };
    }

    const project = response.data.data.projectUpdate.project;
    const teams = project.teams.nodes;

    const leadUser = project.lead
      ? {
          id: project.lead.id,
          name: project.lead.name,
          email: project.lead.email,
        }
      : null;

    const teamsList = teams.map((team: any) => ({
      id: team.id,
      name: team.name,
      key: team.key,
    }));

    const returnObj: LinearProject = {
      id: project.id,
      name: project.name,
      state: project.state,
      teams: teamsList,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
    };

    if (project.description !== null && project.description !== undefined) {
      returnObj.description = project.description;
    }
    if (project.url) {
      returnObj.url = project.url;
    }
    if (project.color) {
      returnObj.color = project.color;
    }
    if (leadUser) {
      returnObj.lead = leadUser;
    }

    return returnObj;
  } catch (error: any) {
    console.error('Error updating project in Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while updating the project.';

    return {
      error: {
        status: status,
        message: `Failed to update project: ${message}`,
      },
    };
  }
}
