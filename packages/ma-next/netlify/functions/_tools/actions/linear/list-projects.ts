import type { NangoAction, LinearProjectList, LinearProjectsInput } from '../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * List all projects from Linear
 *
 * @param nango - The Nango SDK instance
 * @param input - Optional input parameters including pagination
 * @returns A list of projects with pagination info or an error object
 */
export default async function runAction(
  nango: NangoAction,
  input?: LinearProjectsInput
): Promise<LinearProjectList | ActionError> {
  try {
    const first = input?.first || 10;
    const after = input?.after ? `, after: "${input.after}"` : '';

    const query = `
            query ListProjects {
                projects(first: ${first}${after}) {
                    nodes {
                        id name description url color createdAt updatedAt state
                        lead { id name email }
                        teams { nodes { id name key } }
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: { query },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error fetching projects from Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.projects?.nodes || !response.data?.data?.projects?.pageInfo) {
      const status = response.status === 200 ? 500 : response.status || 500;
      const message = `Failed to fetch projects: Invalid response structure. Response: ${JSON.stringify(response.data)}`;
      console.error('Error fetching projects from Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to fetch projects. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error fetching projects from Linear:', message);
      return { error: { status, message } };
    }

    const projects = response.data.data.projects.nodes.map((project: any) => ({
      id: project.id,
      name: project.name,
      description: project.description,
      url: project.url,
      color: project.color,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      state: project.state,
      lead: project.lead
        ? {
            id: project.lead.id,
            name: project.lead.name,
            email: project.lead.email,
          }
        : undefined,
      teams: project.teams.nodes.map((team: any) => ({
        id: team.id,
        name: team.name,
        key: team.key,
      })),
    }));

    const pageInfo = response.data.data.projects.pageInfo;

    return {
      projects,
      pageInfo: {
        hasNextPage: pageInfo.hasNextPage,
        endCursor: pageInfo.endCursor || undefined,
      },
    };
  } catch (error: any) {
    console.error('Error fetching projects from Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching projects.';

    return {
      error: {
        status: status,
        message: `Failed to fetch projects: ${message}`,
      },
    };
  }
}
