import type { NangoAction, TwitterUserProfile } from '../models';

type ErrorResponse = { error: { status: number; message: string } };

/**
 * Get the authenticated user's profile information
 * This action can be used to test if a connection is working correctly
 */
export default async function getTwitterUserProfile(
  action: NangoAction
): Promise<TwitterUserProfile | ErrorResponse> {
  const { connectionId } = action;
  await action.log(`Getting Twitter user profile for connection ID: ${connectionId}`);

  let response: { status: number; statusText: string; data: any };
  try {
    response = await action.proxy({
      endpoint:
        '/2/users/me?user.fields=id,name,username,profile_image_url,description,location,url,protected,verified,public_metrics',
      method: 'GET',
    });

    await action.log(`Twitter API response status: ${response.status}`);

    if (response.status !== 200) {
      const errorDetails =
        response.data?.detail || response.data?.title || response.statusText || 'Unknown API error';
      await action.log(
        `Error response from Twitter API: Status ${response.status}, Details: ${JSON.stringify(
          response.data
        )}`
      );
      return {
        error: {
          status: response.status,
          message: `Failed to get Twitter user profile: ${errorDetails}`,
        },
      };
    }

    if (typeof response.data === 'object' && response.data !== null && response.data.data) {
      await action.log(`Response data keys: ${Object.keys(response.data)}`);

      const userData = response.data.data;

      const profile: TwitterUserProfile = {
        id: userData.id || '',
        name: userData.name || '',
        username: userData.username || '',
        profile_image_url: userData.profile_image_url || '',
        description: userData.description || '',
        location: userData.location || '',
        url: userData.url || '',
        protected: !!userData.protected,
        verified: !!userData.verified,
        followers_count: userData.public_metrics?.followers_count ?? 0,
        following_count: userData.public_metrics?.following_count ?? 0,
        tweet_count: userData.public_metrics?.tweet_count ?? 0,
        listed_count: userData.public_metrics?.listed_count ?? 0,
      };

      if (!profile.id || !profile.name || !profile.username) {
        await action.log(
          `Error: Missing required fields in Twitter API response. Data: ${JSON.stringify(
            userData
          )}`
        );
        return {
          error: {
            status: 500,
            message:
              'Failed to parse Twitter user profile: Missing required fields (id, name, username)',
          },
        };
      }

      await action.log(`Extracted Twitter profile: ${JSON.stringify(profile, null, 2)}`);
      return profile;
    } else {
      await action.log(
        `Error: Unexpected response data structure. Data: ${JSON.stringify(response.data)}`
      );
      return {
        error: {
          status: 500,
          message: 'Failed to parse Twitter user profile: Unexpected data structure',
        },
      };
    }
  } catch (error: any) {
    await action.log(`Error during Twitter profile fetch or processing: ${error.message}`);
    const errorMessage =
      error.response?.data?.detail ||
      error.response?.data?.title ||
      error.message ||
      'Unknown error getting Twitter user profile';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}
