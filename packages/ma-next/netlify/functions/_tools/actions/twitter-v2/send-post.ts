import type { NangoAction, TwitterPostOutput, TwitterPostInput } from '../models';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: TwitterPostInput
): Promise<TwitterPostOutput | ErrorResponse> {
  const { text, reply_to, quote } = input;

  if (!text || text.trim() === '') {
    return { error: { status: 400, message: 'Post text is required and cannot be empty' } };
  }

  if (text.length > 280) {
    return {
      error: {
        status: 400,
        message: `Post text exceeds the 280 character limit (current: ${text.length})`,
      },
    };
  }

  const payload: Record<string, any> = {
    text,
  };

  if (reply_to) {
    payload['reply'] = {
      in_reply_to_tweet_id: reply_to,
    };
  }

  if (quote) {
    payload['quote_tweet_id'] = quote;
  }

  try {
    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/2/tweets',
      data: payload,
      retries: 3,
    });

    console.error(
      `Twitter API Response Status: ${response.status}, Data: ${JSON.stringify(response.data)}`
    );

    if (response.status !== 201) {
      const errorDetails =
        response.data?.detail || response.data?.title || 'Unknown API error creating tweet';
      console.error(`Twitter API Error: Status ${response.status}, Details: ${errorDetails}`);
      return { error: { status: response.status, message: errorDetails } };
    }

    const tweetData = response.data?.data;
    if (!tweetData || !tweetData.id || !tweetData.text) {
      console.error(
        `Twitter API response missing expected data fields (id or text): ${JSON.stringify(response.data)}`
      );
      return {
        error: {
          status: 500,
          message: 'Twitter API response missing expected data fields (id or text).',
        },
      };
    }

    return {
      id: tweetData.id || 'unknown',
      text: tweetData.text || text,
      created_at: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error(`Error sending Twitter post: ${error.message}`);
    const errorMessage =
      error.response?.data?.detail ||
      error.response?.data?.title ||
      error.message ||
      'Unknown error sending Twitter post';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}
