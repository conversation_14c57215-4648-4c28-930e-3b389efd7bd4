import type { NangoAction, LinkedInUserProfile } from '../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  _input?: void
): Promise<LinkedInUserProfile | ActionError> {
  try {
    const response = await nango.get({
      endpoint: 'v2/userinfo',
      baseUrlOverride: 'https://api.linkedin.com',
    });
    return response.data;
  } catch (error: any) {
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching the LinkedIn profile.';
    return {
      error: {
        status,
        message,
      },
    };
  }
}
