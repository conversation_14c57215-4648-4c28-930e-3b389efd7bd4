import type { NangoAction, LinkedInPostOutput, LinkedInPostInput } from '../models';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: LinkedInPostInput
): Promise<LinkedInPostOutput | ErrorResponse> {
  let userProfile;
  try {
    userProfile = await nango.proxy<{ sub: string }>({
      method: 'GET',
      endpoint: '/v2/userinfo',
      retries: 2,
    });

    if (!userProfile?.data?.sub) {
      console.error(`LinkedIn userinfo response: ${JSON.stringify(userProfile)}`);
      return {
        error: {
          status: 400,
          message: 'Failed to retrieve LinkedIn user ID (sub) from userinfo endpoint.',
        },
      };
    }
  } catch (err: any) {
    console.error(`Error fetching LinkedIn userinfo: ${err.message}`);
    return {
      error: {
        status: err.response?.status ?? 500,
        message: err.response?.data?.message ?? err.message,
      },
    };
  }

  const authorUrn = `urn:li:person:${userProfile.data.sub}`;

  let visibilitySetting = 'PUBLIC';
  if (input.visibility && ['PUBLIC', 'CONNECTIONS'].includes(input.visibility.toUpperCase())) {
    visibilitySetting = input.visibility.toUpperCase();
  } else if (input.visibility) {
    console.error(`Invalid visibility value provided: "${input.visibility}". Defaulting to PUBLIC.`);
  }

  const postBody = {
    author: authorUrn,
    lifecycleState: 'PUBLISHED',
    specificContent: {
      'com.linkedin.ugc.ShareContent': {
        shareCommentary: {
          text: input.text,
        },
        shareMediaCategory: 'NONE',
      },
    },
    visibility: {
      'com.linkedin.ugc.MemberNetworkVisibility': visibilitySetting,
    },
  };

  try {
    const response = await nango.proxy<{ id: string }>({
      method: 'POST',
      endpoint: '/v2/ugcPosts',
      data: postBody,
      headers: {
        'Content-Type': 'application/json',
        'X-Restli-Protocol-Version': '2.0.0',
        'LinkedIn-Version': '202305',
      },
      retries: 1,
    });

    if (!response?.data?.id) {
      console.error(`LinkedIn API response: ${JSON.stringify(response)}`);
      return {
        error: {
          status: 400,
          message: 'Failed to create LinkedIn post or extract ID from response.',
        },
      };
    }

    return {
      id: response.data.id,
    };
  } catch (err: any) {
    console.error(`Error creating LinkedIn post: ${err.message}`);
    return {
      error: {
        status: err.response?.status ?? 500,
        message: err.response?.data?.message ?? err.message,
      },
    };
  }
}
