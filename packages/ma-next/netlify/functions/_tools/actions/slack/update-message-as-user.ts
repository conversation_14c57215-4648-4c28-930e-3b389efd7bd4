import type { NangoAction, SlackUpdateMessageInput, SlackUpdateMessageOutput } from '../models';

interface SlackUpdateResponse {
  ok: boolean;
  channel?: string;
  ts?: string;
  text?: string;
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackUpdateMessageInput
): Promise<SlackUpdateMessageOutput | ErrorResponse> {
  if (!input || !input.channel || !input.ts || !input.text) {
    return {
      error: {
        status: 400,
        message:
          'Channel ID, message timestamp (ts), and new text are required to update a message.',
      },
    };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const dataPayload = {
      channel: input.channel,
      ts: input.ts,
      text: input.text,
    };

    const config = {
      method: 'POST' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'chat.update',
      headers: {
        Authorization: `Bearer ${userToken}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      data: dataPayload,
      retries: 3,
    };

    const response = await nango.proxy<SlackUpdateResponse>(config);

    if (!response.data.ok) {
      const errorMessage = `Slack API error: ${response.data.error || 'Unknown error updating message'}`;
      console.error(errorMessage);
      const status = response.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    const result: SlackUpdateMessageOutput = {
      ok: response.data.ok,
      ...(response.data.channel && { channel: response.data.channel }),
      ...(response.data.ts && { ts: response.data.ts }),
      ...(response.data.text && { text: response.data.text }),
    };

    return result;
  } catch (error: any) {
    console.error(`Error updating Slack message: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error updating Slack message';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      console.error('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      console.error('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    console.error(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}
