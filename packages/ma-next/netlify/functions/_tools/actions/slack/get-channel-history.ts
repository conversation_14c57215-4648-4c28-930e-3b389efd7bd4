import type { NangoAction, SlackGetChannelHistoryInput, SlackMessageList } from '../models';

interface SlackHistoryResponse {
  ok: boolean;
  messages?: SlackApiMessage[];
  has_more?: boolean;
  response_metadata?: {
    next_cursor?: string;
  };
  error?: string;
}

interface SlackApiMessage {
  type: string;
  ts: string;
  user?: string;
  text: string;
  thread_ts?: string;
  reply_count?: number;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackGetChannelHistoryInput
): Promise<SlackMessageList | ErrorResponse> {
  if (!input || !input.channel) {
    return { error: { status: 400, message: 'Channel ID is required to get history.' } };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const params: { [key: string]: string | number } = {
      channel: input.channel,
    };
    if (input.limit !== undefined) {
      params['limit'] = input.limit;
    }
    if (input.latest !== undefined) {
      params['latest'] = input.latest;
    }
    if (input.oldest !== undefined) {
      params['oldest'] = input.oldest;
    }
    if (input.cursor !== undefined) {
      params['cursor'] = input.cursor;
    }

    const config = {
      method: 'GET' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'conversations.history',
      params: params,
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
      retries: 3,
    };
    const response = await nango.proxy<SlackHistoryResponse>(config);

    return response.data;
  } catch (error: any) {
    console.error(`Error getting Slack channel history: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error getting Slack channel history';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      console.error('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      console.error('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    console.error(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}
