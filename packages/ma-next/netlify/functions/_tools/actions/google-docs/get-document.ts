import type { NangoAction, GoogleDocsGetDocumentInput, GoogleDocsDocument } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GoogleDocsGetDocumentInput
): Promise<GoogleDocsDocument | NangoError> {
  const { documentId } = input;

  if (!documentId) {
    return { error: { status: 400, message: 'Input validation failed: Document ID is required.' } };
  }

  try {
    const config = {
      method: 'GET' as const,
      endpoint: `/v1/documents/${documentId}`,
      retries: 3,
    };

    const response = await nango.proxy<GoogleDocsDocument>(config);

    return response.data;
  } catch (error: any) {
    console.error(`Error getting Google Document ${documentId}:`, error);

    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while getting the Google Document.';

    return { error: { status, message } };
  }
}
