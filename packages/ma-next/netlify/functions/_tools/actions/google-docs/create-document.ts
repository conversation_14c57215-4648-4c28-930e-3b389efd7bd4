import type { <PERSON><PERSON>Action, GoogleDocsDocument, GoogleDocsCreateDocumentInput } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GoogleDocsCreateDocumentInput
): Promise<GoogleDocsDocument | NangoError> {
  try {
    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/v1/documents',
      data: {
        title: input?.title,
      },
    });

    return response.data as GoogleDocsDocument;
  } catch (error: any) {
    console.error('Error creating document:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while creating the document.';
    return { error: { status, message } };
  }
}
