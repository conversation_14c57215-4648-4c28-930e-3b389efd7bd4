import type {
  NangoAction,
  GoogleDocsUpdateDocumentOutput,
  GoogleDocsUpdateDocumentInput,
} from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GoogleDocsUpdateDocumentInput
): Promise<GoogleDocsUpdateDocumentOutput | NangoError> {
  const { documentId, requests, writeControl } = input;

  try {
    if (!documentId) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Document ID is required for updating a document.',
        },
      };
    }
    if (!requests || requests.length === 0) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: At least one update request is required.',
        },
      };
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: `/v1/documents/${documentId}:batchUpdate`,
      data: {
        requests: requests,
        writeControl: writeControl,
      },
    });

    return response.data as GoogleDocsUpdateDocumentOutput;
  } catch (error: any) {
    console.error('Error updating document:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while updating the document.';
    return { error: { status, message } };
  }
}
