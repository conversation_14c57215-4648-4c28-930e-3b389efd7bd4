import { initNango } from '../_shared/nango';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { MastraClient } from '@mastra/client-js';
import { getDebugMode } from '../_shared/debug';

const DEBUG = getDebugMode();

// Initialize clients
const mastraClient = new MastraClient({
  baseUrl: process.env.MASTRA_URL || '',
});
const nango = initNango();
const supabase = initServiceRoleSupabase();

// Define Slack payload types
interface SlackChallengePayload {
  type: 'url_verification';
  challenge: string;
  token: string;
}

interface SlackEventPayload {
  event: {
    type: 'message';
    text: string;
    user: string; // Slack user ID
    channel: string;
    thread_ts?: string;
    event_ts: string;
    bot_profile?: {
      bot_id: string;
    };
  };
  team_id: string;
  api_app_id: string;
  event_id: string;
  event_time: number;
}

type SlackPayload = SlackChallengePayload | SlackEventPayload;

// Background processing function
async function processSlackEvent(payload: SlackEventPayload) {
  if (DEBUG) {
    console.log('Starting processSlackEvent');
  }

  const messagePayload = payload.event;

  try {
    // Query Supabase for connection
    const { data: connections, error } = await supabase
      .from('connections')
      .select('id, userId')
      .eq('metadata->>slackUserId', messagePayload.user)
      .single();

    if (error || !connections?.id) {
      console.error('No matching connection found:', error);
      return;
    }

    const connectionId = connections.id;
    const userId = connections.userId;

    if (DEBUG) {
      console.log('Found connection:', connectionId, 'user:', userId);
    }

    // Generate AI response
    const agentResponse = await mastraClient.getAgent('slackAgent').generate({
      messages: [
        {
          role: 'system',
          content: `The user is messaging from Slack. Their userId is ${userId}. USE THIS USER ID for the agentTools call!`,
        },
        { role: 'user', content: messagePayload.text },
      ],
      threadId: messagePayload.thread_ts || messagePayload.event_ts,
      resourceId: userId,
    });

    let responseContent = "Sorry, I couldn't process your request.";
    if (typeof agentResponse === 'string') {
      responseContent = agentResponse;
    } else if (agentResponse && typeof agentResponse === 'object') {
      responseContent = (agentResponse as any).text || JSON.stringify(agentResponse);
    }

    if (DEBUG) {
      console.log('Generated response:', responseContent);
    }

    const slackResponse = await nango.post({
      endpoint: 'https://slack.com/api/chat.postMessage',
      providerConfigKey: 'slack',
      connectionId: connectionId,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
      },
      data: {
        channel: messagePayload.channel,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: responseContent,
            },
          },
        ],
        thread_ts: messagePayload.thread_ts || undefined,
      },
    });

    if (!slackResponse.data.ok) {
      console.error('Failed to send Slack reply:', slackResponse.data);
    } else if (DEBUG) {
      console.log('Slack reply sent successfully:', slackResponse.data);
    }
  } catch (error) {
    console.error('Error in processSlackEvent:', error);
  }
}

// Main handler function
export default async function handler(req: Request) {
  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    const payload: SlackPayload = await req.json();

    if (DEBUG) {
      console.log('Received payload:', JSON.stringify(payload));
    }

    /*
      Challenge
    */
    if ('challenge' in payload && payload.type === 'url_verification') {
      if (!payload.challenge) {
        return new Response('Missing challenge parameter', { status: 400 });
      }
      return new Response(JSON.stringify({ challenge: payload.challenge }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Slack-No-Retry': '1',
        },
      });
    }

    /*
      Message
    */
    const messagePayload = payload as SlackEventPayload;
    if (
      messagePayload.event.type !== 'message' ||
      !messagePayload.event.text ||
      !messagePayload.event.user ||
      messagePayload.event.bot_profile
    ) {
      if (DEBUG) {
        console.log('Ignoring non-message event\n');
      }
      return new Response('Event ignored', {
        status: 200,
        headers: { 'X-Slack-No-Retry': '1' },
      });
    }

    // Process the event in the background
    if (DEBUG) {
      console.log('Processing message:', messagePayload.event.text);
    }

    // In Netlify, we can't use EdgeRuntime.waitUntil, so we'll process synchronously
    await processSlackEvent(messagePayload);

    // Return immediate response to Slack
    return new Response('Event received', {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'X-Slack-No-Retry': '1',
      },
    });
  } catch (error) {
    console.error('Error in request handler:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'X-Slack-No-Retry': '1',
      },
    });
  }
}
