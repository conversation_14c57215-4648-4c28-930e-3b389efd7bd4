import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { getDebugMode } from '../_shared/debug';

const DEBUG = getDebugMode();

export default async function handler(req: Request) {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: corsHeaders,
    });
  }

  try {
    const { email } = await req.json();

    // Validate email
    if (!email) {
      return validationError('Email is required');
    }

    if (!isValidEmail(email)) {
      return validationError('Invalid email address');
    }

    if (DEBUG) console.log('Adding email to Resend audience:', email);

    // Get Resend API key and audience ID from environment variables
    const RESEND_API_KEY = process.env.RESEND_API_KEY;
    const RESEND_AUDIENCE_ID = process.env.RESEND_AUDIENCE_ID;

    if (!RESEND_API_KEY) {
      if (DEBUG) console.error('Missing Resend API key');
      return errorResponse(new Error('Missing Resend API key'));
    }

    if (!RESEND_AUDIENCE_ID) {
      if (DEBUG) console.error('Missing Resend audience ID');
      return errorResponse(new Error('Missing Resend audience ID'));
    }

    // Add to Resend audience
    const response = await fetch(
      'https://api.resend.com/audiences/' + RESEND_AUDIENCE_ID + '/contacts',
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          unsubscribed: false,
        }),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      if (DEBUG) console.error('Failed to add to Resend audience:', data);
      return errorResponse(new Error(data.message || 'Failed to subscribe'));
    }

    if (DEBUG) console.log('Successfully added to Resend audience:', data);

    // Return success response
    return new Response(
      JSON.stringify({
        message: 'Successfully subscribed!',
      }),
      {
        status: 200,
        headers: corsHeaders,
      }
    );
  } catch (error) {
    if (DEBUG) console.error('Error processing request:', error);
    return errorResponse(error);
  }
}

// Helper function to validate email format
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
