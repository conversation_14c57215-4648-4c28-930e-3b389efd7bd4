import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { getDebugMode } from '../_shared/debug';
import { resumeTaskFlow } from '../_taskflow/index';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';

const DEBUG = getDebugMode();

interface ResumeTaskflowRequest {
  executionId: string;
  nodeId: string;
  data: Record<string, any>;
  force?: boolean;
}

export default async function handler(req: Request) {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) {
    return corsResponse;
  }

  try {
    // Authenticate the request
    const [authErrorResponse, user] = await initAuthenticate(req);

    if (authErrorResponse) {
      return authErrorResponse;
    }

    // Parse the request body
    const { executionId, nodeId, data, force } = (await req.json()) as ResumeTaskflowRequest;

    if (DEBUG) {
      console.log('Resume taskflow request:', { executionId, nodeId, data, force });
    }

    if (!executionId || !nodeId) {
      if (DEBUG) console.error('executionId and nodeId are required');
      return validationError('executionId and nodeId are required');
    }

    // Initialize service role supabase client for taskflow operations
    const serviceRoleSupabase = initServiceRoleSupabase();

    // Resume the taskflow execution
    const resumeData = { executionId, nodeId, data, force };

    if (DEBUG) {
      console.log('Resuming taskflow with data:', resumeData);
      console.log('User ID:', user.id);
    }

    const supabaseFacade = new TaskflowSupabaseFacade(serviceRoleSupabase);

    const [result, error] = await resumeTaskFlow({
      supabaseFacade,
      userId: user.id,
      resumeData,
    });

    if (error) {
      console.error('Error resuming taskflow:', error);
      return errorResponse(error);
    }

    if (DEBUG) {
      console.log('Taskflow resumed successfully:', result, JSON.stringify(result));
    }

    // Return the result
    return new Response(JSON.stringify(result), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error in resume-taskflow:', error);
    return errorResponse(error);
  }
}
