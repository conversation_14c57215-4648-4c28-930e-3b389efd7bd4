import {
  <PERSON>rro<PERSON><PERSON>ontent,
  <PERSON>ecution<PERSON>ontent,
  MessagePart,
  TaskflowContent,
  TextContent,
  ToolCallContent,
  ToolResultContent,
} from '.';

const textEncoder = new TextEncoder();

/**
 * Combined class for encoding streaming responses and tracking message parts
 * Each method enqueues a chunk and adds a message part for database storage
 */
class ChatProtocolEnqueuer {
  private controller: { enqueue: (chunk: Uint8Array) => void };
  private parts: MessagePart[];

  constructor(
    controller: { enqueue: (chunk: Uint8Array) => void },
    initialParts: MessagePart[] = []
  ) {
    this.controller = controller;
    this.parts = initialParts;
  }

  addTextChunk(text: TextContent): void {
    this.controller.enqueue(textEncoder.encode(`0:${JSON.stringify(text)}\n`));
    if (this.parts.length > 0 && this.parts[this.parts.length - 1].type === 'text') {
      const lastPart = this.parts[this.parts.length - 1];
      lastPart.content = (lastPart.content as TextContent) + text;
    } else {
      this.parts.push({ type: 'text', content: text });
    }
  }

  addTaskflowChunk(taskflow: TaskflowContent): void {
    this.controller.enqueue(textEncoder.encode(`t:${JSON.stringify(taskflow)}\n`));
    this.parts.push({ type: 'taskflow', content: taskflow });
  }

  addExecutionChunk(execution: ExecutionContent): void {
    this.controller.enqueue(textEncoder.encode(`e:${JSON.stringify(execution)}\n`));
    this.parts.push({ type: 'execution', content: execution });
  }

  addToolCall(toolCall: ToolCallContent): void {
    this.controller.enqueue(textEncoder.encode(`9:${JSON.stringify(toolCall)}\n`));
    this.parts.push({ type: 'tool_call', content: toolCall });
  }

  addToolResult(toolResult: ToolResultContent): void {
    this.controller.enqueue(textEncoder.encode(`a:${JSON.stringify(toolResult)}\n`));
    this.parts.push({ type: 'tool_result', content: toolResult });
  }

  addError(error: ErrorContent): void {
    this.controller.enqueue(textEncoder.encode(`3:${JSON.stringify(error)}\n`));
    this.parts.push({ type: 'error', content: error });
  }

  sendToolResultsToClient(toolResults: ToolResultContent[]): void {
    toolResults.forEach(toolResult => {
      this.controller.enqueue(textEncoder.encode(`a:${JSON.stringify(toolResult)}\n`));
    });
  }

  addAuthoringTaskflow(message: string = ''): void {
    this.controller.enqueue(textEncoder.encode(`c:${JSON.stringify({ message })}\n`));
    // Note: We intentionally don't add this to this.parts as it's ephemeral
  }

  getParts(): MessagePart[] {
    return this.parts;
  }
}

export { ChatProtocolEnqueuer };
