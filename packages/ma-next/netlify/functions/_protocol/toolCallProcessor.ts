import type { TransformContext } from '../submit-prompt/transform';
import { ActionCallTool, ToolCallContent } from './index';
import { VercelToolCallPart } from './vercel';
import { getConnections } from '../submit-prompt/getConnections';
import { ACTION_INPUT_MODELS_KEYED } from '../_agents/nangoConstants';
import { generateObject } from 'ai';
import { openai } from '@ai-sdk/openai';

type ToolCallProcessorContext = TransformContext;

/**
 * @class ToolCallProcessor - Processes tool calls from the LLM stream
 * Handles special cases like connection requirements and confirmation needs
 */
class ToolCallProcessor {
  private ctx: ToolCallProcessorContext;

  constructor(context: ToolCallProcessorContext) {
    this.ctx = context;
  }

  /**
   * @method processToolCall - Process a tool call and determine if it needs special handling
   * - Checks if a connection is required
   * - Checks if user confirmation is required
   */
  async processToolCall(vercelToolCall: VercelToolCallPart): Promise<void> {
    const { chatProtocolEnqueuer } = this.ctx;

    let toolCall = this.convertToToolCallContent(vercelToolCall);

    if (toolCall.toolName === 'actionCall') {
      toolCall = await repairIfNeedsRepair(toolCall);
      const { providerKey, actionKey, actionParameters } = toolCall.args;

      const connections = await this.getConnections();
      const connectionExists = connections.some(c => c.providerKey === providerKey);

      if (!connectionExists) {
        chatProtocolEnqueuer.addToolCall({
          ...toolCall,
          connectionRequired: true,
          confirmationRequired: true,
        });
        return;
      }

      if (needsConfirmation(actionKey)) {
        const isAutoApproved = this.isActionAutoApproved(providerKey, actionKey);

        if (isAutoApproved) {
          chatProtocolEnqueuer.addToolCall({
            ...toolCall,
            autoApproved: true,
          });
        } else {
          chatProtocolEnqueuer.addToolCall({
            ...toolCall,
            confirmationRequired: true,
          });
        }
        return;
      }
    }

    chatProtocolEnqueuer.addToolCall(toolCall);
  }

  /**
   * Get connections from the context
   * Caches the result to avoid repeated database calls
   */
  private connections: { id: string; providerKey: string }[] | null = null;

  private async getConnections(): Promise<{ id: string; providerKey: string }[]> {
    if (!this.connections) {
      this.connections = await getConnections(this.ctx);
    }
    return this.connections || [];
  }

  private convertToToolCallContent(vercelToolCall: VercelToolCallPart): ToolCallContent {
    return vercelToolCall as ToolCallContent;
  }

  /**
   * Checks if an action is auto-approved based on user preferences
   */
  private isActionAutoApproved(providerKey: string, actionKey: string): boolean {
    const { userProfile } = this.ctx;

    if (!userProfile?.preferences?.autoApprovals) {
      return false;
    }

    const autoApprovalKey = `${providerKey}:${actionKey}`;
    return userProfile.preferences.autoApprovals.includes(autoApprovalKey);
  }
}

/**
 * Determines if an action requires user confirmation before execution
 * Read-only actions like get, list, fetch, search don't require confirmation
 */
function needsConfirmation(actionKey: string): boolean {
  return !(
    actionKey.includes('get') ||
    actionKey.includes('list') ||
    actionKey.includes('fetch') ||
    actionKey.includes('search')
  );
}

/**
 * Validates and repairs action parameters for a tool call if needed.
 * @param toolCall - The tool call object containing action details.
 * @returns The original or repaired tool call with validated parameters.
 */
async function repairIfNeedsRepair(toolCall: ActionCallTool): Promise<ActionCallTool> {
  const { providerKey, actionKey, actionParameters } = toolCall.args;

  const { zodSchema } =
    ACTION_INPUT_MODELS_KEYED[
      `${providerKey}:${actionKey}` as keyof typeof ACTION_INPUT_MODELS_KEYED
    ] || {};

  if (!zodSchema) {
    return toolCall;
  }

  try {
    zodSchema.parse(actionParameters);
    return toolCall;
  } catch {
    console.log('Repairing action parameters...', JSON.stringify(actionParameters, null, 2));
    try {
      const { object: repairedArgs } = await generateObject({
        model: openai('gpt-4.1'),
        schema: zodSchema,
        prompt: [
          `A model tried to call the tool "${toolCall.toolName}" with the following arguments:`,
          JSON.stringify(toolCall.args),
          `The tool accepts the following schema:`,
          JSON.stringify(zodSchema.shape),
          'Please fix the arguments.',
        ].join('\n'),
      });

      zodSchema.parse(repairedArgs);
      console.log('Repaired action parameters:', JSON.stringify(repairedArgs, null, 2));
      return {
        ...toolCall,
        args: {
          ...toolCall.args,
          actionParameters: repairedArgs,
        },
      };
    } catch {
      console.error('Failed to repair action parameters:', actionParameters);
      return toolCall;
    }
  }
}

export type { ToolCallProcessorContext };
export { needsConfirmation, ToolCallProcessor };
