import type { AssistantMessage, Message, ToolMessage, UserMessage } from './messages';
import type { ToolCallContent, ToolResultContent } from './content';
import type { TextPart, ToolCallPart, ToolResultPart } from './parts';
import type { Tool<PERSON>all, ToolResult } from './tools';
import {
  VercelAssistantMessage,
  VercelMessage,
  VercelSystemMessage,
  VercelTextPart,
  VercelToolCallPart,
  VercelToolMessage,
  VercelToolResultPart,
  VercelUserMessage,
} from './vercel';

const vercel = {
  userMessage(input: string | UserMessage): VercelUserMessage {
    const content = typeof input === 'string' ? input : input.content;
    return {
      role: 'user',
      content: content,
    };
  },

  systemMessage(input: string): VercelSystemMessage {
    return {
      role: 'system',
      content: input,
    };
  },

  toolResultMessage(input: ToolMessage | ToolResultPart | ToolResultContent): VercelToolMessage {
    if ('role' in input) {
      return {
        role: 'tool',
        content: input.parts.map(part => this.toolResultPart(part.content)),
      };
    }
    return {
      role: 'tool',
      content: [this.toolResultPart('content' in input ? input.content : input)],
    };
  },

  toolCallMessage(input: ToolCallPart | ToolCallContent): VercelAssistantMessage {
    const toolCall: ToolCall = 'content' in input ? input.content : input;
    return {
      role: 'assistant',
      content: [this.toolCallPart(toolCall)],
    };
  },

  assistantMessage(input: AssistantMessage | string): VercelAssistantMessage {
    if (typeof input === 'string') {
      return {
        role: 'assistant',
        content: [this.textPart(input)],
      };
    }

    return {
      role: 'assistant',
      content: input.parts.map(part => {
        switch (part.type) {
          case 'text':
            return this.textPart(part);
          case 'tool_call':
            return this.toolCallPart(part.content);
          default:
            return part;
        }
      }),
    };
  },

  toolCallPart(input: ToolCall): VercelToolCallPart {
    return {
      type: 'tool-call',
      toolCallId: input.toolCallId,
      toolName: input.toolName,
      args: input.args,
    };
  },

  toolResultPart(input: ToolResult): VercelToolResultPart {
    return {
      type: 'tool-result',
      toolCallId: input.toolCallId,
      toolName: input.toolName,
      result: input.result,
    };
  },

  textPart(input: string | TextPart): VercelTextPart {
    if (typeof input === 'string') {
      return {
        type: 'text',
        text: input,
      };
    }
    return {
      type: 'text',
      text: input.content,
    };
  },

  messagesToVercelMessages(messages: Message[]): VercelMessage[] {
    return messages.map(message => {
      if (typeof message === 'string') {
        return this.userMessage(message);
      }

      switch (message.role) {
        case 'user':
          return this.userMessage(message.content);
        case 'system':
          return this.systemMessage(message.content);
        case 'tool':
          return this.toolResultMessage(message);
        case 'assistant':
          return this.assistantMessage(message);
        default:
          return message;
      }
    });
  },
};

export { vercel };
