export interface ActionCallArgs {
  providerKey: string;
  actionKey: string;
  actionParameters?: Record<string, unknown>;
  userExplanation?: string;
}

export interface TestLoopHandlerArgs {
  message: string;
  options?: string[];
}

export interface ClientHandlerArgs {
  clientOnly: true;
  payload: Record<string, unknown>;
}

export interface DatabaseQueryArgs {
  query: string;
  params?: Record<string, unknown>;
  database?: string;
}

export type ActionCallTool = {
  toolCallId: string;
  toolName: 'actionCall';
  args: ActionCallArgs;
  connectionRequired?: boolean;
  autoApproved?: boolean;
  confirmationRequired?: boolean;
};

export type TestLoopHandlerTool = {
  toolCallId: string;
  toolName: 'test_loop_handler';
  args: TestLoopHandlerArgs;
};

export type ClientHandlerTool = {
  toolCallId: string;
  toolName: 'test_client_handler';
  args: ClientHandlerArgs;
};

export type DatabaseQueryTool = {
  toolCallId: string;
  toolName: 'database_query';
  args: DatabaseQueryArgs;
};

export type ToolCall = ActionCallTool | TestLoopHandlerTool | ClientHandlerTool | DatabaseQueryTool;

export type ToolResultPayload<T> =
  | T
  | { error: string }
  | {
      userCancelled: boolean;
      cancellationMessage?: string;
    };

export type ActionCallResult = {
  success: boolean;
  userCancelled?: boolean;
  data?: unknown;
  error?: string;
  richUISupport?: boolean;
};

export type DatabaseQueryResult = {
  rows: unknown[];
  rowCount: number;
};

export type ActionCallToolResult = {
  toolCallId: string;
  toolName: 'actionCall';
  result: ToolResultPayload<ActionCallResult>;
};

export type TestLoopHandlerToolResult = {
  toolCallId: string;
  toolName: 'test_loop_handler';
  result: ToolResultPayload<string>;
};

export type TestClientHandlerToolResult = {
  toolCallId: string;
  toolName: 'test_client_handler';
  result: ToolResultPayload<string>;
};

export type DatabaseQueryToolResult = {
  toolCallId: string;
  toolName: 'database_query';
  result: ToolResultPayload<DatabaseQueryResult>;
};

export type ToolResult =
  | ActionCallToolResult
  | TestLoopHandlerToolResult
  | TestClientHandlerToolResult
  | DatabaseQueryToolResult;
