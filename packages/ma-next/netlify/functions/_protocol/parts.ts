import {
  Error<PERSON>ontent,
  Exec<PERSON><PERSON>ontent,
  TaskflowContent,
  TextContent,
  Tool<PERSON>allContent,
  ToolResultContent,
} from './content';

export type TextPart = { type: 'text'; content: TextContent };
export type ErrorPart = { type: 'error'; content: ErrorContent };
export type TaskflowPart = { type: 'taskflow'; content: TaskflowContent };
export type ExecutionPart = { type: 'execution'; content: ExecutionContent };
export type ToolCallPart = { type: 'tool_call'; content: ToolCallContent };
export type ToolResultPart = { type: 'tool_result'; content: ToolResultContent };

export type MessagePartType =
  | 'text'
  | 'error'
  | 'taskflow'
  | 'execution'
  | 'connection'
  | 'tool_call'
  | 'tool_result';

export type MessagePart =
  | TextPart
  | ErrorPart
  | TaskflowPart
  | ExecutionPart
  | ToolCallPart
  | ToolResultPart;
