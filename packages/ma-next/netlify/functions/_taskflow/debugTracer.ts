import fs from 'fs';
import { getDebugMode } from '../_shared/debug';
import { TaskflowSupabaseFacade } from './taskflowSupabaseFacade';

export class DebugTracer {
  private steps: { description: string; data: any }[] = [];
  private supabaseFacade: TaskflowSupabaseFacade;

  constructor(supabaseFacade: TaskflowSupabaseFacade) {
    this.supabaseFacade = supabaseFacade;
  }

  addStep(description: string, data: Record<string, any> = {}) {
    let cloned;
    try {
      cloned = JSON.parse(JSON.stringify(data));
    } catch (error) {
      cloned = this.createSafeSnapshot(data);
    }
    this.steps.push({ description, data: cloned });
  }

  private createSafeSnapshot(obj: any, visited = new WeakSet()): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (visited.has(obj)) {
      return '[Circular Reference]';
    }
    visited.add(obj);

    if (Array.isArray(obj)) {
      return obj.map((item) => this.createSafeSnapshot(item, visited));
    }

    const result: any = {};
    try {
      for (const [key, value] of Object.entries(obj)) {
        try {
          if (typeof value === 'function') {
            result[key] = '[Function]';
          } else if (typeof value === 'undefined') {
            result[key] = '[undefined]';
          } else if (typeof value === 'symbol') {
            result[key] = '[Symbol]';
          } else if (value && typeof value === 'object' && value.constructor) {
            const constructorName = value.constructor.name;
            if (
              constructorName === 'Timeout' ||
              constructorName === 'Immediate' ||
              constructorName === 'TimersList'
            ) {
              result[key] = `[${constructorName}]`;
            } else {
              result[key] = this.createSafeSnapshot(value, visited);
            }
          } else {
            result[key] = this.createSafeSnapshot(value, visited);
          }
        } catch (error) {
          result[key] = '[Non-serializable]';
        }
      }
    } catch (error) {
      return '[Object enumeration failed]';
    }

    return result;
  }

  getTrace() {
    return this.steps;
  }

  async commit(executionId: string) {
    if (getDebugMode()) {
      fs.writeFileSync(
        `debug_trace.json`,
        JSON.stringify(this.steps, null, 2)
      );
    }

    await this.supabaseFacade.createExecutionTrace(executionId, this.steps);
  }
}
