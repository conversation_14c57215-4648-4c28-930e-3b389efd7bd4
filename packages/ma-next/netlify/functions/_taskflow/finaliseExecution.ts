import { ExecTaskflowParams, ExecutionContext } from './types';
import { ExecutionResult, FinalResult } from './executeTaskFlow';
import { DebugTracer } from './debugTracer';

/**
 * Finalizes the taskflow execution by updating the result and completion time
 * If the last node is not completed, returns the execution context without finalizing
 *
 * @param executionId The execution ID
 * @param context The execution context
 * @param lastNodeId The ID of the last node in the workflow (optional)
 * @returns A tuple with the execution result and error (if any)
 */
async function finaliseExecution(
  executionId: string,
  context: ExecutionContext,
  supabaseFacade: ExecTaskflowParams['supabaseFacade'],
  lastNodeId: string | undefined,
  tracer: DebugTracer
): Promise<[FinalResult | ExecutionResult, null] | [null, Error]> {
  const facade = supabaseFacade;
  tracer.addStep('[finaliseExecution] start', { executionId, lastNodeId });
  // If the last node is not in SUCCESS status, return the execution context without finalizing
  if (lastNodeId && context[lastNodeId]?.status !== 'SUCCESS') {
    // If the last node is in ERROR status, we still want to finalize but mark it as an error result
    if (lastNodeId && context[lastNodeId]?.status === 'ERROR') {
      // Create a result object that preserves both outputs and HITL parameters
      const result = Object.fromEntries(
        Object.entries(context).map(([k, v]) => {
          // For each node, include its output and any HITL parameters
          return [
            k,
            {
              output: v.output || null,
              hitl: v.hitl, // Preserve HITL parameters
              status: v.status,
            },
          ];
        })
      );

      // Include errors in the result
      const errors = Object.fromEntries(
        Object.entries(context)
          .filter(([_, v]) => v.status === 'ERROR' && v.error)
          .map(([k, v]) => [k, v.error])
      );

      await facade.updateExecution(executionId, {
        result,
        errors,
        status: 'ERROR',
        completedAt: new Date(),
      });
      tracer.addStep('[finaliseExecution] error result', { result, errors });

      const finalResult: FinalResult = { executionId, result, errors };
      return [finalResult, null];
    }

    return [{ executionId, context }, null];
  }
  // Create a result object that preserves both outputs and HITL parameters
  tracer.addStep('[finaliseExecution] building result', { context });

  const result = Object.fromEntries(
    Object.entries(context).map(([k, v]) => {
      if (v.hitl) {
        tracer.addStep('[finaliseExecution] preserve HITL', { nodeId: k, hitl: v.hitl });
      }

      return [
        k,
        {
          output: v.output,
          ...(v.hitl ? { hitl: v.hitl } : {}),
          status: v.status,
        },
      ];
    })
  );

  tracer.addStep('[finaliseExecution] result ready', { result });

  await facade.updateExecution(executionId, {
    result,
    status: 'SUCCESS',
    completedAt: new Date(),
  });

  const finalResult: FinalResult = { executionId, result };
  return [finalResult, null];
}

export { finaliseExecution };
