import {
  CreateExecutionRequest,
  ResumeExecutionRequest,
  TriggerWithExecutionRequest,
} from './types';
import { createTaskflowExecution } from './createTaskflowExecution';
import { executeTaskFlow } from './executeTaskFlow';
import { evaluateNodeParameters } from './evaluateNodeParameters';
import { DebugTracer } from './debugTracer';

/**
 * Creates a taskflow execution without starting it
 */
const createExecution = (params: CreateExecutionRequest) => createTaskflowExecution(params);

/**
 * Resumes a paused taskflow execution
 */
const resumeTaskFlow = (params: ResumeExecutionRequest) =>
  executeTaskFlow({
    ...params,
    tracer: new DebugTracer(params.supabaseFacade),
  });

/**
 * Triggers a taskflow with an existing execution ID
 */
const triggerWithExecution = (params: TriggerWithExecutionRequest) =>
  executeTaskFlow({
    ...params,
    taskflowExecutionId: params.taskflowExecutionId,
    tracer: new DebugTracer(params.supabaseFacade),
  });

/**
 * Triggers a taskflow by creating a new execution
 */
const triggerTaskFlow = (params: CreateExecutionRequest) =>
  executeTaskFlow({
    ...params,
    taskflowId: params.taskflowId,
    tracer: new DebugTracer(params.supabaseFacade),
  });

export {
  createExecution,
  evaluateNodeParameters,
  resumeTaskFlow,
  triggerTaskFlow,
  triggerWithExecution,
};
