import { isValid, parseISO } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { PreparedData } from './initExecution';

/**
 * Creates evaluation functions for node parameter evaluation
 *
 * @param prep The prepared execution data
 * @returns Object containing evaluation functions
 */
export function getEvaluationFunctions(prep: PreparedData) {
  return {
    isoDateUser: (dateString: string) => {
      let date = dateString ? parseISO(dateString) : new Date();

      if (!isValid(date)) {
        date = new Date();
      }

      return formatInTimeZone(
        new Date(date),
        prep.userProfile.preferences?.timeZone || 'UTC',
        "yyyy-MM-dd'T'HH:mm:ssXXX"
      );
    },
    JSON: {
      parse: (jsonString: string) => {
        try {
          return JSON.parse(jsonString);
        } catch (error) {
          console.error(`[getEvaluationFunctions] Error parsing JSON:`, error);
          return null;
        }
      },
      stringify: (jsonObject: object) => {
        try {
          return JSON.stringify(jsonObject);
        } catch (error) {
          console.error(`[getEvaluationFunctions] Error stringifying JSON:`, error);
          return null;
        }
      },
    },
  };
}
