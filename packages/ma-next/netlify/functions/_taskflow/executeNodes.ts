import { isValid, parseISO } from 'date-fns';
import { evaluateNodeParameters } from './evaluateNodeParameters';
import { getTournament } from './getTournament';
import { ExecTaskflowParams, NodeHandlers, AiNodeDependencies } from './types';
import { ExecutionResult } from './executeTaskFlow';
import { PreparedData } from './initExecution';
import { executeNode } from './executeNode';
import { executeAiNode } from './node-handlers';
import { formatInTimeZone } from 'date-fns-tz';
import { DebugTracer } from './debugTracer';
import { omit } from 'lodash-es';
import { getEvaluationFunctions } from './getEvaluationFunctions';

/**
 * Executes the nodes in the taskflow
 *
 * @param executionData The prepared execution data
 * @param facade The TaskflowSupabaseFacade instance
 * @param userId The user ID
 * @returns A tuple with the execution result and error (if any)
 */
type ExecuteNodesParams = {
  prep: PreparedData;
  facade: ExecTaskflowParams['supabaseFacade'];
  userId: string;
  tracer: DebugTracer;
  handlers?: Partial<NodeHandlers>;
};

async function executeNodes({
  prep,
  facade,
  userId,
  tracer,
  handlers = {},
}: ExecuteNodesParams): Promise<[ExecutionResult, null] | [null, Error]> {
  const {
    taskflow: { nodes },
    taskflowExecution: { id: executionId, context, triggerData, resumeData },
  } = prep;

  if (resumeData?.force && resumeData?.nodeId) {
    tracer.addStep('[executeNodes] force wipe', { nodeId: resumeData.nodeId });

    const resumeNodeIndex = nodes.findIndex(node => node.id === resumeData.nodeId);

    if (resumeNodeIndex !== -1) {
      const futureNodes = nodes.slice(resumeNodeIndex + 1);

      for (const node of futureNodes) {
        delete context[node.id];
      }

      tracer.addStep('[executeNodes] facade.updateExecution with wiped context', { context });
      await facade.updateExecution(executionId, { context });
    }
  }

  for (const node of nodes) {
    tracer.addStep(`[executeNodes] start node: ${node.id}`, { node });

    if (context[node.id]?.status === 'SUCCESS' && !resumeData?.force) {
      tracer.addStep('[executeNodes] node already successful, skipping');
      continue;
    }

    tracer.addStep('[executeNodes] context before running', { context });

    // { [node1.id]: data, [node2.id]: data, trigger: data, ...fns }
    const input = {
      trigger: triggerData,
      ...Object.fromEntries(Object.entries(context).map(([k, v]) => [k, v.output])),
      ...getEvaluationFunctions(prep),
    };

    tracer.addStep('[executeNodes] evaluateNodeParameters', { input: omit(input, 'isoDateUser') });

    const resolvedParams = evaluateNodeParameters(node.parameters, input, getTournament());

    tracer.addStep('[executeNodes] resolved parameters', { resolvedParams });

    context[node.id] = { status: 'RUNNING', type: node.type, parameters: resolvedParams };
    const { error: runningUpdateError } = await facade.updateExecution(executionId, {
      context,
      status: 'RUNNING',
    });

    if (runningUpdateError) {
      tracer.addStep('[executeNodes] update running error', { runningUpdateError });
      return [null, new Error(`Failed to update execution context: ${runningUpdateError.message}`)];
    }

    let output;
    try {
      output = await executeNode(
        {
          node: { ...node, parameters: resolvedParams },
          supabaseFacade: facade,
          userId,
          executionId,
          resumePayload: node.id === resumeData?.nodeId ? resumeData?.data : undefined,
          userProfile: prep.userProfile,
          tracer,
        },
        handlers
      );
    } catch (error) {
      tracer.addStep('[executeNodes] node error', {
        nodeId: node.id,
        error: error instanceof Error ? error.message : String(error),
      });
      context[node.id] = {
        status: 'ERROR',
        type: node.type,
        parameters: resolvedParams,
        error: error instanceof Error ? error.message : String(error),
      };

      const { error: errorUpdateError } = await facade.updateExecution(executionId, {
        context,
        status: 'ERROR',
      });

      if (errorUpdateError) {
        tracer.addStep('[executeNodes] update error after node failure', { errorUpdateError });
      }

      const result: ExecutionResult = { executionId, context };
      tracer.addStep('[executeNodes] node error result', { result });
      return [result, null];
    }

    if (output.status === 'PAUSED') {
      context[node.id] = {
        status: 'PAUSED',
        type: node.type,
        parameters: resolvedParams,
        hitl: output.hitlParams,
      };
      const { error: pausedUpdateError } = await facade.updateExecution(executionId, {
        context,
        status: 'PAUSED',
      });

      if (pausedUpdateError) {
        tracer.addStep('[executeNodes] update paused error', { pausedUpdateError });
        return [
          null,
          new Error(
            `Failed to update execution context for PAUSED state: ${pausedUpdateError.message}`
          ),
        ];
      }

      const result: ExecutionResult = { executionId, context };
      tracer.addStep('[executeNodes] paused result', { result });
      return [result, null];
    }

    const existingHitl = context[node.id]?.hitl;
    const hitl = existingHitl || output.hitlParams;

    if (hitl) {
      tracer.addStep('[executeNodes] preserve HITL', { nodeId: node.id, hitl });
    }

    context[node.id] = {
      type: node.type,
      status: output.status,
      parameters: resolvedParams,
      output: output.result,
      ...(output.executionData ? { executionData: output.executionData } : {}),
      ...(hitl ? { hitl } : {}),
      ...(output.error ? { error: output.error } : {}),
    };

    const { error: successUpdateError } = await facade.updateExecution(executionId, {
      context,
      status: output.status,
    });

    if (successUpdateError) {
      tracer.addStep('[executeNodes] update success error', { successUpdateError });
      return [
        null,
        new Error(
          `Failed to update execution context for SUCCESS state: ${successUpdateError.message}`
        ),
      ];
    }
  }

  const result: ExecutionResult = { executionId, context };
  tracer.addStep('[executeNodes] finished', { result });
  return [result, null];
}

export { executeNodes };
