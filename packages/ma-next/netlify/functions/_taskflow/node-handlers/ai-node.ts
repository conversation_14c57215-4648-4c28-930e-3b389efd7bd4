import { generateObject, generateText, jsonSchema } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { NodeExecutionSubtypeParams, NodeOutput } from '../types';
import { omit } from 'lodash-es';

const DEFAULT_DEPS: Required<AiNodeDependencies> = {
  createOpenAI: () => {
    const apiKey = process.env.OPENAI_API_KEY;

    if (!apiKey) {
      throw new Error(
        'OpenAI API key is missing. Please set the OPENAI_API_KEY environment variable.'
      );
    }
    return createOpenAI({ apiKey });
  },
  generateObject,
  generateText,
  jsonSchema,
};

/**
 * Executes an AI node that generates text or structured data using OpenAI
 *
 * @param params The node execution parameters
 * @returns The node execution output
 */
type AiNodeDependencies = {
  createOpenAI?: typeof createOpenAI;
  generateObject?: typeof generateObject;
  generateText?: typeof generateText;
  jsonSchema?: typeof jsonSchema;
};

async function executeAiNode(
  execCtx: NodeExecutionSubtypeParams,
  deps: AiNodeDependencies = {}
): Promise<NodeOutput> {
  const merged = { ...DEFAULT_DEPS, ...deps };
  const { subtype } = execCtx.node;

  switch (subtype) {
    case 'simple':
      return handleSimpleAiNode(execCtx, merged);
    default:
      return {
        status: 'ERROR',
        error: `Unsupported AI node subtype: ${subtype}`,
      };
  }
}

/**
 * Handles the execution of a simple AI node
 *
 * @param parameters The parameters for the simple AI node
 * @returns The node execution output
 */
async function handleSimpleAiNode(
  execCtx: NodeExecutionSubtypeParams,
  deps: Required<AiNodeDependencies>
): Promise<NodeOutput> {
  const { tracer } = execCtx;
  tracer.addStep('[handleSimpleAiNode] start', { execCtx });

  const {
    node: { parameters: { system: systemRaw, prompt = '', outputSchema, model = 'gpt-4.1' } = {} },
    userProfile,
  } = execCtx;

  const system =
    systemRaw +
    '\n\n <USER_CONTEXT>THE PROFILE OF THE USER WHO YOU ARE ACTING ON BEHALF OF IS:.\n\n' +
    JSON.stringify(omit(userProfile, 'id'), null, 2) +
    '</USER_CONTEXT>';

  try {
    const openai = deps.createOpenAI();
    const llm = openai.responses(model);

    if (outputSchema) {
      tracer.addStep('[handleSimpleAiNode] output schema provided', { outputSchema });

      const modifiedSchema = {
        ...outputSchema,
        additionalProperties: false,
      };

      tracer.addStep('[handleSimpleAiNode] structured schema', { schema: modifiedSchema });

      const schema = deps.jsonSchema(modifiedSchema);

      const { object: result } = await deps.generateObject({
        model: llm,
        schema,
        prompt,
        system,
      });

      tracer.addStep('[handleSimpleAiNode] structured result', { result });

      return {
        status: 'SUCCESS',
        result,
      };
    } else {
      tracer.addStep('[handleSimpleAiNode] no output schema, generating text');
      const { text } = await deps.generateText({
        model: llm,
        prompt,
        system,
      });

      tracer.addStep('[handleSimpleAiNode] generated text', { text });

      return {
        status: 'SUCCESS',
        result: text,
      };
    }
  } catch (error) {
    const aiError = error as Error;
    tracer.addStep('[handleSimpleAiNode] error', { error: aiError.message });
    return {
      status: 'ERROR',
      error: `Error executing AI node: ${aiError.message}`,
    };
  }
}

export type { AiNodeDependencies };
export { executeAiNode };
