import { strictEqual, deepStrictEqual } from 'node:assert';
import { test } from 'node:test';
import { z } from 'zod';
import { executeAgentNode, AgentNodeDependencies } from './agent-node';
import { NodeExecutionSubtypeParams } from '../types';
import {
  ACTION_INPUT_MODELS_KEYED,
  ACTION_OUTPUT_MODELS_KEYED,
} from '../../_agents/nangoConstants';
import { DebugTracer } from '../debugTracer';

// setup fake schemas
const INPUT_SCHEMA = z.object({ foo: z.string() });
const OUTPUT_SCHEMA = z.object({ bar: z.string() });
(ACTION_INPUT_MODELS_KEYED as any)['testProvider:testAction'] = {
  model: 'TestInput',
  zodSchema: INPUT_SCHEMA,
  jsonSchema: { type: 'object', properties: { foo: { type: 'string' } }, required: ['foo'] },
};
(ACTION_OUTPUT_MODELS_KEYED as any)['testProvider:testAction'] = {
  model: 'TestOutput',
  zodSchema: OUTPUT_SCHEMA,
  jsonSchema: { type: 'object', properties: { bar: { type: 'string' } }, required: ['bar'] },
};

let genCalls: any[] = [];
const mockGenerateObject = async (opts: any) => {
  genCalls.push(opts);
  return { object: { foo: 'baz' } };
};

const deps: AgentNodeDependencies = {
  createOpenAI: () => ((model: string) => ({})) as any,
  generateObject: mockGenerateObject,
  jsonSchema: (s: any) => s,
  getRunner: () => (n: any, p: any) => Promise.resolve({ bar: 'out', params: p }),
  getPseudoNangoAction: () => ({}) as any,
};

const mockSupabase = {
  getConnection: () => Promise.resolve({ data: { id: 'c1' }, error: null }),
};

const params: NodeExecutionSubtypeParams = {
  node: {
    id: 'n1',
    type: 'agent.aToB',
    subtype: 'aToB',
    parameters: {
      system: 'sys',
      prompt: 'data',
      output: { providerKey: 'testProvider', actionKey: 'testAction' },
      userDeterminedParameters: { extra: 'val' },
    },
  },
  supabaseFacade: mockSupabase as any,
  executionId: 'e1',
  userId: 'u1',
  userProfile: {},
  tracer: new DebugTracer(mockSupabase as any),
};

test('agent aToB node executes', async () => {
  genCalls = [];
  const res = await executeAgentNode(params, deps);
  strictEqual(res.status, 'SUCCESS');
  strictEqual(genCalls.length, 1);
  deepStrictEqual((res.result as any).bar, 'out');
  deepStrictEqual(res.executionData?.actionParameters, { foo: 'baz', extra: 'val' });
});

test('agent aToB node retries on validation error', async () => {
  genCalls = [];
  const originalSchema = (ACTION_INPUT_MODELS_KEYED as any)['testProvider:testAction'].zodSchema;
  (ACTION_INPUT_MODELS_KEYED as any)['testProvider:testAction'].zodSchema = z.object({ foo: z.number() });

  const res = await executeAgentNode(params, deps);

  strictEqual(res.status, 'ERROR');
  strictEqual(genCalls.length, 5);
  strictEqual(genCalls[1].prompt.includes('Previously you returned'), true);

  (ACTION_INPUT_MODELS_KEYED as any)['testProvider:testAction'].zodSchema = originalSchema;
});
