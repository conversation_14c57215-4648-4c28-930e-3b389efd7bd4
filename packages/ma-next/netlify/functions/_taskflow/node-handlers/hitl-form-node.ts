import { NodeExecutionSubtypeParams, NodeOutput } from '../types';

/**
 * Executes a human-in-the-loop form node
 *
 * This node type pauses execution and waits for human input via a form
 * When resumed, it returns the form data provided by the human
 *
 * @param params The node execution parameters
 * @returns The node execution output
 */
async function executeHitlFormNode({
  node: { subtype, parameters },
  resumePayload,
}: NodeExecutionSubtypeParams): Promise<NodeOutput> {
  // Validate the node subtype
  if (subtype !== 'form') {
    return {
      status: 'ERROR',
      error: `Unsupported hitlForm node subtype: ${subtype}`,
    };
  }

  // If resumePayload is provided, the node is being resumed with human input
  if (resumePayload) {
    return {
      status: 'SUCCESS',
      result: resumePayload,
      // Preserve the HITL parameters so they can be displayed in the UI
      hitlParams: {
        type: 'form',
        parameters,
      },
    };
  }

  // Otherwise, pause execution and wait for human input
  return {
    status: 'PAUSED',
    hitlParams: {
      type: 'form',
      parameters,
    },
  };
}

export { executeHitlFormNode };
