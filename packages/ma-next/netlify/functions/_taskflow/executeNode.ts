import * as handlers from './node-handlers/index';
import { NodeExecutionParams, NodeHandlers, NodeOutput, NodeHandlerDependencies } from './types';

/**
 * Entry point for all node types task execution
 * Routes to the appropriate handler based on the node type
 *
 * @param params The node execution parameters
 * @returns The node execution output
 */
async function executeNode(
  params: NodeExecutionParams,
  {
    executeActionNode = handlers.executeActionNode,
    executeAiNode = handlers.executeAiNode,
    executeHitlFormNode = handlers.executeHitlFormNode,
    executeAgentNode = handlers.executeAgentNode,
    ...dependencies
  }: Partial<NodeHandlers> = {}
): Promise<NodeOutput> {
  const tracer = params.tracer;
  tracer.addStep('[executeNode] start', { nodeId: params.node.id, type: params.node.type });

  const [primaryType, ...subtypeParts] = params.node.type.split('.');
  const subtype = subtypeParts.join('.');
  const subtypeParams = { ...params, node: { ...params.node, subtype } };

  let output: NodeOutput;

  if (primaryType === 'provider') {
    tracer.addStep('[executeNode] executing action node', { subtype });
    output = await executeActionNode(subtypeParams, dependencies.actionNodeDependencies);
  } else if (primaryType === 'hitl') {
    tracer.addStep('[executeNode] executing HITL form node', { subtype });
    output = await executeHitlFormNode(subtypeParams);
  } else if (primaryType === 'ai') {
    tracer.addStep('[executeNode] executing AI node', { subtype });
    output = await executeAiNode(subtypeParams, dependencies.aiNodeDependencies);
  } else if (primaryType === 'agent') {
    tracer.addStep('[executeNode] executing agent node', { subtype });
    output = await handlers.executeAgentNode(subtypeParams, dependencies.agentNodeDependencies);
  } else {
    output = {
      status: 'ERROR',
      error: `Unsupported node type: ${params.node.type}`,
    };
  }

  tracer.addStep('[executeNode] done', { nodeId: params.node.id, output });

  return output;
}

export { executeNode };
