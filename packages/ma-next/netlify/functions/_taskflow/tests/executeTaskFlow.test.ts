import { deepStrictEqual as assertEquals, deepStrictEqual as assertObjectMatch } from 'node:assert';
import { test } from 'node:test';

import { initExecution } from '../initExecution';

// Mock Supabase client for taskflow execution
const mockExecuteSupabase = {
  from: (table: string) => {
    if (table === 'taskflow_executions') {
      return {
        select: (_columns: string) => ({
          eq: (_column: string, _value: any) => ({
            is: (_column: string, _value: any) => ({
              maybeSingle: () =>
                Promise.resolve({
                  data: {
                    id: 'execution-123',
                    context: {},
                    triggerData: { input: 'test' },
                    taskflowId: 'taskflow-123',
                  },
                  error: null,
                }),
            }),
          }),
        }),
        update: (_data: any) => ({
          eq: (_column: string, _value: any) =>
            Promise.resolve({
              data: { success: true },
              error: null,
            }),
        }),
        insert: (_data: any) => ({
          select: (_columns: string) => ({
            single: () =>
              Promise.resolve({
                data: { id: 'execution-123' },
                error: null,
              }),
          }),
        }),
      };
    } else if (table === 'taskflows') {
      return {
        select: (_columns: string) => ({
          eq: (_column: string, _value: any) => ({
            eq: (_column: string, _value: any) => ({
              single: () =>
                Promise.resolve({
                  data: {
                    taskflowSchema: {
                      nodes: [
                        {
                          id: 'node1',
                          type: 'ai.simple',
                          parameters: { system: 'Test', prompt: 'Hello' },
                        },
                      ],
                    },
                  },
                  error: null,
                }),
            }),
          }),
        }),
      };
    }
    return {};
  },
};

// Create a mock executeTask function that we'll inject
let mockExecuteTaskCalls: any[] = [];
const mockExecuteTask = async (params: any) => {
  mockExecuteTaskCalls.push(params);
  return { status: 'COMPLETED', result: { output: 'Test result' } };
};

// Create a mock for paused execution
let mockPausedExecuteTaskCalls: any[] = [];
const mockPausedExecuteTask = async (params: any) => {
  mockPausedExecuteTaskCalls.push(params);
  return { status: 'PAUSED', hitlParams: { type: 'form', parameters: {} } };
};

// Create a custom executeNodes function that uses our mock executeTask
const createCustomExecuteNodes = (mockTaskFn: any) => {
  return async (preparedData: any, supabaseFacade: any, userId: any, tracer: any) => {
    const {
      taskflow: { nodes },
      taskflowExecution: { id: executionId, context, triggerData, resumeData },
    } = preparedData;

    // This is a simplified version of executeNodes that uses our mock executeTask
    for (const node of nodes) {

      if (context[node.id]?.status === 'COMPLETED' && !resumeData?.force) continue;

      const input = {
        trigger: triggerData,
        ...Object.fromEntries(
          Object.entries(context as Record<string, any>).map(([k, v]) => [k, v.output])
        ),
      };

      // We're skipping the parameter evaluation for simplicity in tests

      // Use our mock executeTask instead of the real one
      const output = await mockTaskFn({
        node,
        supabase,
        userId,
        executionId,
        resumePayload: node.id === resumeData?.nodeId ? resumeData?.data : undefined,
      });

      if (output.status === 'PAUSED') {
        context[node.id] = { status: 'PAUSED', hitl: output.hitlParams };
        await supabase.from('taskflow_executions').update({ context }).eq('id', executionId);

        return [{ executionId, context }, null]; // exits loop & outer function
      }

      // Preserve hitl parameters if they exist
      const hitl = context[node.id]?.hitl;
      context[node.id] = {
        status: 'COMPLETED',
        output: output.result,
        ...(hitl ? { hitl } : {}),
      };
      await supabase.from('taskflow_executions').update({ context }).eq('id', executionId);
    }

    return [{ executionId, context }, null];
  };
};

test('executeTaskFlow - executes a taskflow with new execution', async () => {
  // Reset mock calls
  mockExecuteTaskCalls = [];

  // Create a custom version of executeTaskFlow that uses our mocked executeNodes
  const customExecuteTaskFlow = async (params: any) => {
    const [prep, prepError] = await initExecution(params);

    if (prep === null || prepError !== null) {
      return [null, prepError || new Error('Failed to prepare execution')];
    }

    // Use our custom executeNodes with the mock executeTask
    return await createCustomExecuteNodes(mockExecuteTask)(prep, params.supabase, params.userId, {});

    const [result, error] = await customExecuteTaskFlow({
      taskflowId: 'taskflow-123',
      triggerData: { input: 'test' },
      supabaseFacade: mockExecuteSupabase as any,
      userId: 'user-123',
    });

    assertEquals(error, null);
    assertObjectMatch(result as Record<string, any>, {
      executionId: 'execution-123',
      result: { node1: { output: 'Test result' } },
    });

    // Verify executeTask was called
    assertEquals(mockExecuteTaskCalls.length, 1);
  };
});

test('executeTaskFlow - resumes an existing execution', async () => {
  // Reset mock calls
  mockExecuteTaskCalls = [];

  // Create a custom version of executeTaskFlow that uses our mocked executeNodes
  const customExecuteTaskFlow = async (params: any) => {
    const [prep, prepError] = await initExecution(params);

    if (prep === null || prepError !== null) {
      return [null, prepError || new Error('Failed to prepare execution')];
    }

    // Use our custom executeNodes with the mock executeTask
    return await createCustomExecuteNodes(mockExecuteTask)(prep, params.supabase, params.userId, {});

    const [result, error] = await customExecuteTaskFlow({
      resumeData: {
        nodeId: 'node1',
        executionId: 'execution-123',
        data: { input: 'resumed' },
      },
      supabaseFacade: mockExecuteSupabase as any,
      userId: 'user-123',
    });

    assertEquals(error, null);
    assertObjectMatch(result as Record<string, any>, {
      executionId: 'execution-123',
      result: { node1: { output: 'Test result' } },
    });

    // Verify executeTask was called
    assertEquals(mockExecuteTaskCalls.length, 1);
  };
});

test('executeTaskFlow - pauses execution when node returns PAUSED status', async () => {
  // Reset mock calls
  mockPausedExecuteTaskCalls = [];

  // Create a custom version of executeTaskFlow that uses our mocked executeNodes
  const customExecuteTaskFlow = async (params: any) => {
    const [prep, prepError] = await initExecution(params);

    if (prep === null || prepError !== null) {
      return [null, prepError || new Error('Failed to prepare execution')];
    }

    // Use our custom executeNodes with the mock executeTask
    return await createCustomExecuteNodes(mockPausedExecuteTask)(
      prep,
      params.supabase,
      params.userId,
      {}
    );

    const [result, error] = await customExecuteTaskFlow({
      taskflowId: 'taskflow-123',
      triggerData: { input: 'test' },
      supabaseFacade: mockExecuteSupabase as any,
      userId: 'user-123',
    });

    assertEquals(error, null);
    assertObjectMatch(result as Record<string, any>, {
      executionId: 'execution-123',
      context: {
        node1: {
          status: 'PAUSED',
          hitl: { type: 'form', parameters: {} },
        },
      },
    });

    // Verify executeTask was called
    assertEquals(mockPausedExecuteTaskCalls.length, 1);
  };
});
