import { deepStrictEqual as assertEquals, deepStrictEqual as assertObjectMatch } from 'node:assert';
import { test } from 'node:test';
import { finaliseExecution } from '../finaliseExecution';
import { NodeStatus } from '../types';
import { DebugTracer } from '../debugTracer';

// Minimal facade for testing updateExecution
const mockFacade = {
  updateExecution: (_id: string, _data: any) =>
    Promise.resolve({ data: {}, error: null }),
};

test('finaliseExecution - preserves HITL parameters in result', async () => {
  // Create a mock execution context with HITL parameters
  const executionId = 'execution-123';
  const context = {
    node1: {
      status: 'SUCCESS' as NodeStatus,
      type: 'ai.simple',
      output: { result: 'test output' },
      hitl: { type: 'form', parameters: { fields: [] } },
    },
    node2: {
      status: 'SUCCESS' as NodeStatus,
      type: 'ai.simple',
      output: { result: 'another output' },
    },
  };

  // Call finaliseExecution
  const [result, error] = await finaliseExecution(
    executionId,
    context,
    mockFacade as any,
    'node2',
    new DebugTracer(mockFacade as any)
  );

  // Verify the result
  assertEquals(error, null);
  assertEquals(result?.executionId, executionId);

  // Check that the result contains the expected data
  const finalResult = result as any;

  // Verify that node1's HITL parameters are preserved
  assertObjectMatch(finalResult.result.node1, {
    status: 'SUCCESS',
    output: { result: 'test output' },
    hitl: { type: 'form', parameters: { fields: [] } },
  });

  // Verify that node2's data is correct
  assertObjectMatch(finalResult.result.node2, {
    status: 'SUCCESS',
    output: { result: 'another output' },
  });
});

test('finaliseExecution - handles error status correctly', async () => {
  // Create a mock execution context with an error
  const executionId = 'execution-123';
  const context = {
    node1: {
      status: 'SUCCESS' as NodeStatus,
      type: 'ai.simple',
      output: { result: 'test output' },
    },
    node2: {
      status: 'ERROR' as NodeStatus,
      type: 'ai.simple',
      error: 'Something went wrong',
      hitl: { type: 'form', parameters: { fields: [] } },
    },
  };

  // Call finaliseExecution
  const [result, error] = await finaliseExecution(
    executionId,
    context,
    mockFacade as any,
    'node2',
    new DebugTracer(mockFacade as any)
  );

  // Verify the result
  assertEquals(error, null);
  assertEquals(result?.executionId, executionId);

  // Since the last node has ERROR status, we should get a result with errors
  const finalResult = result as any;

  // Check that errors are included
  assertObjectMatch(finalResult.errors, {
    node2: 'Something went wrong',
  });

  // Check that the result contains the expected data with HITL preserved
  assertEquals(finalResult.result.node1.status, 'SUCCESS');
  assertEquals(finalResult.result.node1.output, { result: 'test output' });

  assertEquals(finalResult.result.node2.status, 'ERROR');
  assertObjectMatch(finalResult.result.node2.hitl, { type: 'form', parameters: { fields: [] } });
});
