import { deepStrictEqual as assertEquals, deepStrictEqual as assertObjectMatch } from 'node:assert';
import { test } from 'node:test';
import { createTaskflowExecution } from '../createTaskflowExecution';

// Mock Supabase client for taskflow creation
const mockCreateSupabase = {
  from: (table: string) => ({
    select: (_columns: string) => ({
      eq: (_column: string, _value: any) => ({
        eq: (_column: string, _value: any) => ({
          single: () =>
            Promise.resolve({
              data: {
                id: 'taskflow-123',
                taskflowSchema: {
                  nodes: [
                    {
                      id: 'node1',
                      type: 'ai.simple',
                      parameters: { system: 'Test', prompt: 'Hello' },
                    },
                  ],
                },
              },
              error: null,
            }),
        }),
      }),
    }),
    insert: (_data: any) => ({
      select: (_columns: string) => ({
        single: () =>
          Promise.resolve({
            data: { id: 'execution-123' },
            error: null,
          }),
      }),
    }),
  }),
};

test('createTaskflowExecution - creates a new execution', async () => {
  const [result, error] = await createTaskflowExecution({
    taskflowId: 'taskflow-123',
    triggerData: { input: 'test' },
    supabaseFacade: mockCreateSupabase as any,
    userId: 'user-123',
  });

  assertEquals(error, null);
  assertObjectMatch(result as Record<string, any>, {
    taskflow: {
      schema: {
        nodes: [
          { id: 'node1', type: 'ai.simple', parameters: { system: 'Test', prompt: 'Hello' } },
        ],
      },
      nodes: [{ id: 'node1', type: 'ai.simple', parameters: { system: 'Test', prompt: 'Hello' } }],
    },
    taskflowExecution: {
      id: 'execution-123',
      context: {},
      triggerData: { input: 'test' },
    },
  });
});
