import { deepStrictEqual as assertEquals } from 'node:assert';
import { test } from 'node:test';
import { Tournament } from '@n8n/tournament/dist/index.js';
import { evaluateNodeParameters } from '../evaluateNodeParameters';
import { formatInTimeZone } from 'date-fns-tz';

test('Parameter Evaluator - basic string template', () => {
  const tournament = new Tournament();
  const parameters = {
    greeting: '{{input.hello}}',
  };
  const input = {
    input: {
      hello: 'world',
    },
  };

  const result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, { greeting: 'world' });
});

test('Parameter Evaluator - nested objects', () => {
  const tournament = new Tournament();
  const parameters = {
    user: {
      name: '{{input.user.name}}',
      age: '{{input.user.age}}',
    },
  };
  const input = {
    input: {
      user: {
        name: 'John',
        age: 30,
      },
    },
  };

  const result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, { user: { name: '<PERSON>', age: 30 } });
});

test('Parameter Evaluator - preserves arrays in parameters', () => {
  const tournament = new Tournament();
  const parameters = {
    items: ['item1', 'item2', '{{input.item3}}'],
  };
  const input = {
    input: {
      item3: 'item3',
    },
  };

  const result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, { items: ['item1', 'item2', 'item3'] });
});

test('Parameter Evaluator - preserves arrays in input', () => {
  const tournament = new Tournament();
  const parameters = {
    items: '{{input.items}}',
  };
  const input = {
    input: {
      items: ['item1', 'item2', 'item3'],
    },
  };

  const result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, { items: ['item1', 'item2', 'item3'] });
});

test('Parameter Evaluator - handles array of objects', () => {
  const tournament = new Tournament();
  const parameters = {
    users: [{ name: '{{input.users[0].name}}' }, { name: '{{input.users[1].name}}' }],
  };
  const input = {
    input: {
      users: [{ name: 'John' }, { name: 'Jane' }],
    },
  };

  const result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, { users: [{ name: 'John' }, { name: 'Jane' }] });
});

test('Parameter Evaluator - handles array as root parameter', () => {
  const tournament = new Tournament();
  const parameters = ['{{input.items[0]}}', '{{input.items[1]}}', { name: '{{input.name}}' }];
  const input = {
    input: {
      items: ['item1', 'item2'],
      name: 'John',
    },
  };

  const result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, ['item1', 'item2', { name: 'John' }]);
});

test('Parameter Evaluator - custom function', () => {
  const tournament = new Tournament();
  const parameters = { items: '{{ sayHello() }}' };
  const input = {
    sayHello: () => 'Hello World',
  };

  const result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, { items: 'Hello World' });
});

test('Parameter Evaluator - isoDateUser', () => {
  const tournament = new Tournament();
  const parameters = { date: "{{ isoDateUser('2025-05-14T01:28:00Z') }}" };
  const input = {
    isoDateUser: (date: string) => {
      const isoDate = formatInTimeZone(
        new Date(date),
        'Pacific/Auckland',
        "yyyy-MM-dd'T'HH:mm:ssXXX"
      );
      return isoDate;
    },
  };

  const result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, {
    date: '2025-05-14T13:28:00+12:00', // Expected Pacific/Auckland output (NZST)
  });
});

test('Parameter Evaluator - email extraction', () => {
  const tournament = new Tournament();
  const parameters = {
    recipient: '{{trigger.sender.match(/[^<]+<([^>]+)>/)?.[1] || trigger.sender}}',
  };

  // Case 1: Name <<EMAIL>>
  let input = {
    trigger: {
      sender: 'SendTestEmail <<EMAIL>>',
    },
  };
  let result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, { recipient: '<EMAIL>' });

  // Case 2: <EMAIL>
  input = {
    trigger: {
      sender: '<EMAIL>',
    },
  };
  result = evaluateNodeParameters(parameters, input, tournament);
  assertEquals(result, { recipient: '<EMAIL>' });
});
