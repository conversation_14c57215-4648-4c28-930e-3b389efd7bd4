import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';

interface DeleteConnectionRequest {
  connectionId: string;
  providerConfigKey: string;
}

/**
 * Single endpoint managing multiple methods (currently DELETE)
 */
export default async function handler(req: Request) {
  try {
    // Handle CORS
    const corsResponse = handleCors(req, ['DELETE']);
    if (corsResponse) return corsResponse;

    // Only handle DELETE method
    if (req.method !== 'DELETE') {
      return validationError('Method not allowed');
    }

    // Authenticate request
    const [authErrorResponse, , , supabaseUser] = await initAuthenticate(req);
    if (authErrorResponse) {
      return authErrorResponse;
    }

    // Parse request body
    const { connectionId, providerConfigKey } = (await req.json()) as DeleteConnectionRequest;

    if (!connectionId || !providerConfigKey) {
      return validationError('Connection ID and provider key are required');
    }

    // 1. First deactivate any agents using this connection
    const { data: agents, error: fetchError } = await supabaseUser
      .from('taskflows')
      .select('id, schema')
      .eq('active', true);

    if (fetchError) throw fetchError;

    // Filter agents that use this provider
    const agentsToDeactivate = agents?.filter(agent => {
      const steps = agent.schema?.steps || [];
      return steps.some(
        (step: any) => step.type === 'connectProvider' && step.provider === providerConfigKey
      );
    });

    if (agentsToDeactivate?.length) {
      // Deactivate matching agents
      const { error: updateError } = await supabaseUser
        .from('taskflows')
        .update({ active: false })
        .in(
          'id',
          agentsToDeactivate.map(a => a.id)
        );

      if (updateError) throw updateError;
    }

    // 2. Delete the connection from Nango
    const nangoToken = process.env.NANGO_SECRET_KEY;
    if (!nangoToken) {
      throw new Error('Nango secret key not configured');
    }

    // Check if the connection exists first
    const connectionCheckResponse = await fetch(
      `https://api.nango.dev/connection/${connectionId}?provider_config_key=${providerConfigKey}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${nangoToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    // If connection doesn't exist, skip deletion from Nango
    if (connectionCheckResponse.status === 404) {
      console.log(`Connection ${connectionId} not found in Nango, skipping deletion`);
    } else {
      // Delete the connection from Nango
      const nangoResponse = await fetch(
        `https://api.nango.dev/connection/${connectionId}?provider_config_key=${providerConfigKey}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${nangoToken}`,
            'Content-Type': 'application/json',
          },
        }
      );
      if (!nangoResponse.ok) {
        throw new Error(`Failed to delete Nango connection: ${nangoResponse.statusText}`);
      }
    }

    // 3. Delete the connection from our database
    const { error: deleteError } = await supabaseUser
      .from('connections')
      .delete()
      .eq('id', connectionId);

    if (deleteError) throw deleteError;

    return new Response(JSON.stringify({ success: true }), { headers: corsHeaders });
  } catch (error) {
    return errorResponse(error);
  }
}
