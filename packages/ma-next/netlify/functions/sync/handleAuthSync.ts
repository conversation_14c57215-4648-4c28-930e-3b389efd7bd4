import { AuthCreationPayload } from './sync-types';
import { SupabaseClient } from '@supabase/supabase-js';
import { fetchProviderMetadata, ProviderMetadata } from './provider-metadata';
import { debug } from '../_shared/debug';

/**
 * Handles incoming Nango webhook requests for authentication events.
 *
 * @param body - The webhook payload
 * @param supabase - The Supabase client
 * @returns Response indicating success or failure of auth event processing
 */
export async function handleAuthSync(
  body: AuthCreationPayload,
  supabase: SupabaseClient
): Promise<number> {
  if (!body.success || body.operation !== 'creation') {
    debug('Auth event ignored (only successful creations processed)');
    return 200; // Indicate success for ignored events
  }

  // Check if user exists in the database
  const { data: user, error: userError } = await supabase
    .from('profiles')
    .select('id')
    .eq('id', body.endUser.endUserId)
    .single();

  if (userError || !user) {
    debug('User not found in database', {
      userId: body.endUser.endUserId,
      error: userError?.message,
    });
    return 204;
  }

  const providerMetadata: ProviderMetadata | null = await fetchProviderMetadata(
    body.connectionId,
    body.providerConfigKey
  );

  const { error } = await supabase
    .from('connections')
    .insert({
      id: body.connectionId,
      providerKey: body.providerConfigKey,
      userId: body.endUser.endUserId,
      providerUserId: providerMetadata?.providerUserId || null,
      displayName: providerMetadata?.displayName || null,
      metadata: providerMetadata,
    })
    .select('id, providerKey, userId, providerUserId, displayName')
    .single();

  if (error) {
    console.error('handleAuthSync: Failed to insert connection', error);
    return 500; // Indicate internal server error on database failure
  }

  debug('handleAuthSync: Auth event processed successfully', {
    connectionId: body.connectionId,
    providerKey: body.providerConfigKey,
    userId: body.endUser.endUserId,
    providerUserId: providerMetadata?.providerUserId || null,
    displayName: providerMetadata?.displayName || null,
  });

  return 200; // Indicate success
}
