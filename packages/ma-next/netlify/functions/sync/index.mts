import { initNango } from '../_shared/nango';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { handleAuthSync } from './handleAuthSync';
import { handleDataSync } from './handleDataSync';
import { debug } from '../_shared/debug';

const supabase = initServiceRoleSupabase();
const nango = initNango();

/**
 * Handles incoming Nango webhook requests for authentication and sync events.
 */
export default async function handler(req: Request): Promise<Response> {
  try {
    const signature = req.headers.get('X-Nango-Signature');
    const body = await req.json();
    debug(`[handler] Received webhook event\n\n`);

    if (!nango.verifyWebhookSignature(signature!, body)) {
      return new Response('Invalid webhook signature', { status: 401 });
    }

    if (body.type === 'auth' && body.operation === 'creation' && body.success) {
      const statusCode = await handleAuthSync(body, supabase);
      return new Response(null, { status: statusCode });
    }

    if (body.type === 'sync' && body.success && body.syncType === 'INCREMENTAL') {
      debug(`[handler] Sync event`);
      const statusCode = await handleDataSync(body, supabase, nango);
      return new Response(null, { status: statusCode });
    }

    return new Response('Unhandled webhook type or failed sync', {
      status: 200,
    });
  } catch (error) {
    console.error('Webhook processing failed:', error);
    return new Response('Internal server error', { status: 500 });
  }
}
