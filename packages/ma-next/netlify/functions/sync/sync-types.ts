/*
  See: https://docs.nango.dev/guides/webhooks/webhooks-from-nango
*/

// Type definitions
interface BaseWebhookPayload {
  type: 'auth' | 'sync';
  connectionId: string;
  providerConfigKey: string;
  success: boolean;
}

interface AuthCreationPayload extends BaseWebhookPayload {
  type: 'auth';
  operation: 'creation';
  authMode: 'OAUTH2' | 'BASIC' | 'API_KEY' | string;
  provider: string;
  environment: 'DEV' | 'PROD' | string;
  success: true;
  endUser: {
    endUserId: string;
    organizationId: string;
  };
}

interface AuthRefreshPayload extends BaseWebhookPayload {
  type: 'auth';
  operation: 'refresh';
  authMode: 'OAUTH2' | 'BASIC' | 'API_KEY' | string;
  provider: string;
  environment: 'DEV' | 'PROD' | string;
  success: false;
  endUser: {
    endUserId: string;
    organizationId: string;
  };
  error: {
    type: string;
    description: string;
  };
}

interface SyncSuccessPayload extends BaseWebhookPayload {
  type: 'sync';
  syncName: string;
  model: string;
  syncType: 'INCREMENTAL' | 'INITIAL' | 'WEBHOOK';
  success: true;
  modifiedAfter: string;
  responseResults:
    | {
        added: number;
        updated: number;
        deleted: number;
      }
    | [number, number, number];
}

interface SyncFailurePayload extends BaseWebhookPayload {
  type: 'sync';
  syncName: string;
  model: string;
  syncType: 'INCREMENTAL' | 'INITIAL' | 'WEBHOOK';
  success: false;
  error: {
    type: string;
    description: string;
  };
  startedAt: string;
  failedAt: string;
}

type WebhookPayload =
  | AuthCreationPayload
  | AuthRefreshPayload
  | SyncSuccessPayload
  | SyncFailurePayload;

// Type for sync triggers from the database
interface SyncTrigger {
  id: string;
  taskflowId: string;
  providerKey: string;
  model: string;
  syncKey: string;
  cursor?: string;
  taskflow: {
    conversationId: string;
    conversation: {
      userId: string;
    };
  };
}

export type {
  AuthCreationPayload,
  AuthRefreshPayload,
  SyncFailurePayload,
  SyncSuccessPayload,
  WebhookPayload,
  SyncTrigger,
};
