import { Nango } from '@nangohq/node';

function getPseudoNangoAction(providerKey: string, connectionId: string) {
  if (!process.env.NANGO_SECRET_KEY) {
    throw new Error('NANGO_SECRET_KEY not set');
  }

  const nango = new Nango({
    providerConfigKey: providerKey,
    secretKey: process.env.NANGO_SECRET_KEY,
    connectionId,
  });

  // @ts-expect-error ...
  nango._getConnection = nango.getConnection;
  // @ts-expect-error ...
  nango.getConnection = async () => nango._getConnection(providerKey, connectionId);

  // @ts-expect-error ...
  nango._getMetadata = nango.getMetadata;
  // @ts-expect-error ...
  nango.getMetadata = async () => nango._getMetadata(providerKey, connectionId);

  // @ts-expect-error ...
  nango._setMetadata = nango.setMetadata;
  nango.setMetadata = async (metadata: any) =>
    // @ts-expect-error ...
    nango._setMetadata(providerKey, connectionId, metadata);

  // @ts-expect-error ...
  nango.log = async (message: string) => {
    console.log(`[Nango] ${message}`);
  };

  return nango;
}

export { getPseudoNangoAction };
