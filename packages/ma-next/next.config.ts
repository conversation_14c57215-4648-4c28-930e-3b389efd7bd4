import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // Disable TypeScript type checking during build
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },

  // Disable ESLint during build
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  // Using default .next directory for server-side rendering
  // distDir: "out", // Removed custom output directory
  // Content Security Policy (CSP) - ported from makeagent/src/layouts/Layout.astro
  // Note: CSP in Next.js is usually configured via headers, not meta tags.
  // The original policy was: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;"
  // To implement, uncomment and adjust the headers function below:
  /*
  async headers() {
    return [
      {
        source: '/(.*)', // Apply to all routes
        headers: [
          {
            key: 'Content-Security-Policy',
            // Example policy (adjust as needed, remove 'unsafe-inline' if possible):
            value: "default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self' data:;",
          },
        ],
      },
    ];
  },
  */
};

export default nextConfig;
