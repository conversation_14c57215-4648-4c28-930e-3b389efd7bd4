import React, { createContext, useContext } from 'react';
import { useChat } from '../chat/useChat';
import { ChatContextType } from '../chat/types';

const ChatContext = createContext<ChatContextType | undefined>(undefined);

interface ChatProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component that makes chat context available to all child components
 */
function ChatProvider({ children }: ChatProviderProps) {
  const chat = useChat();

  return <ChatContext.Provider value={chat}>{children}</ChatContext.Provider>;
}

/**
 * Hook to access the chat context
 */
function useChatContext(): ChatContextType {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
}

export { ChatProvider, useChatContext };
