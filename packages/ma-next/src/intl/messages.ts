import { createIntl, createIntlCache } from 'react-intl';

// Define message structure
export interface Messages {
  [key: string]: string;
}

// Create a cache for better performance
const cache = createIntlCache();

// English messages
const enMessages: Messages = {
  // App
  'app.title': 'MakeAgent',

  // Auth
  'auth.login': 'Log in',
  'auth.signup': 'Sign up',
  'auth.logout': 'Log out',
  'auth.email': 'Email',
  'auth.password': 'Password',
  'auth.loginTitle': 'Log in to MakeAgent',
  'auth.signupTitle': 'Create your account',
  'auth.createAccount': 'Create account',
  'auth.emailPlaceholder': 'Enter your email',
  'auth.passwordPlaceholder': 'Enter your password',
  'auth.needAccount': 'Need an account? Sign up',
  'auth.haveAccount': 'Already have an account? Log in',
  'auth.forgotPassword': 'Forgot password?',
  'auth.resetPassword': 'Reset password',
  'auth.backToLogin': 'Back to login',
  'auth.otpInstructions': 'Enter the 6-digit code sent to your email',
  'auth.resendCode': 'Resend code',
  'auth.verifyEmail': 'Verify your email',
  'auth.verifyEmailInstructions': "We've sent a verification code to your email",
  'auth.submitting': 'Submitting...',
  'auth.failedToAuth': 'Failed to authenticate',
  'auth.failedToRegister': 'Failed to register',
  'auth.failedToLogin': 'Failed to login',
  'auth.failedToVerifyCode': 'Failed to verify code',
  'auth.failedToSendResetEmail': 'Failed to send reset email',
  'auth.failedToSetNewPassword': 'There was an error setting your new password',
  'auth.setPassword': 'Set your new password',
  'auth.submitSetPassword': 'Save password & Log in',

  // Profile
  'profile.title': 'Edit Profile',
  'profile.email': 'Email',
  'profile.firstName': 'First Name',
  'profile.lastName': 'Last Name',

  // Common
  'common.save': 'Save',
  'common.saving': 'Saving...',
  'common.cancel': 'Cancel',

  // Navbar
  'navbar.newAgent': 'New agent',
  'navbar.manageAgents': 'Manage Agents',
  'navbar.recentConversations': 'Recent conversations',
  'navbar.myAgents': 'My agents',
  'navbar.profile': 'Profile',
  'navbar.help': 'Help & Support',
  'navbar.feedback': 'Feedback',

  // Manage Agents
  'manageAgents.searchAgents': 'Search agents...',
  'manageAgents.noAgents': 'No agents yet',
  'manageAgents.createFirst': 'Create your first agent to get started',
  'manageAgents.createAgent': 'Create Agent',
  'manageAgents.noMatches': 'No agents match your search',
  'manageAgents.active': 'Active',
  'manageAgents.inactive': 'Inactive',
  'manageAgents.untitled': 'Untitled Agent',
  'manageAgents.updated': 'Updated {date}',

  // Agent Panel
  'agent.test': 'Test Agent',
  'agent.active': 'Active',
  'agent.inactive': 'Inactive',
  'agent.simulating': 'Simulating...',
  'agent.processing': 'Processing...',
  'agent.failed': 'Failed',
  'agent.success': 'Success',

  // Steps
  'steps.auth.text': 'Please login to continue',
  'steps.connectProvider.text': 'Connect your Google account',
  'steps.fineTune.text': 'Fine-tune your agent',
  'steps.fineTune.skip': 'Skip',
  'steps.fineTune.brandBrief': 'Brand brief',
  'steps.fineTune.examples': 'Examples',
  'steps.fineTune.save': 'Save',
  'steps.fineTune.addExample': 'Add example',
  'steps.fineTune.briefPlaceholder': 'Add guidelines for how the AI should write for you',

  'agentSetup.auth.setup': 'Please login to continue',
  'agentSetup.auth.login': 'Log in',
  'agentSetup.auth.signup': 'Sign up',

  // Prompt
  'prompt.placeholder': 'What would you like to automate?',
  'prompt.taskPlaceholder': 'What can I do for you?',
  // Agent mode suggestions
  'prompt.suggestions.emailDrafts': 'Auto-create draft response emails',
  'prompt.suggestions.seoWriter': 'Create an SEO writer agent',
  'prompt.suggestions.weather': 'Fetch weather report daily and email me',
  'prompt.suggestions.monitor': 'Monitor website changes and notify me',
  'prompt.suggestions.calendar': 'Summarize my daily calendar events',
  // Task mode suggestions
  'prompt.tasks.gmailDraft': 'Draft an email for me to review',
  'prompt.tasks.slackMessage': 'Send a message to my Slack channel',
  'prompt.tasks.calendarEvents': 'Show my upcoming calendar events',
  'prompt.tasks.notionPage': 'Create a new page in my Notion workspace',
  'prompt.tasks.githubFile': 'Create a file in my GitHub repository',
  'prompt.tasks.searchDropbox': 'Find files in my Dropbox',
  'prompt.tasks.uploadFile': 'Upload a file to my cloud storage',
  'prompt.tasks.weatherReport': 'Get the current weather forecast',

  // Time
  'time.justNow': 'just now',
  'time.minuteAgo': '{count, plural, one {# minute ago} other {# minutes ago}}',
  'time.hourAgo': '{count, plural, one {# hour ago} other {# hours ago}}',
  'time.dayAgo': '{count, plural, one {# day ago} other {# days ago}}',
  'time.weekAgo': '{count, plural, one {# week ago} other {# weeks ago}}',
};

// Create an IntlProvider instance that we'll use for formatting outside of React components
const intl = createIntl(
  {
    locale: 'en',
    messages: enMessages,
  },
  cache
);

export { enMessages, intl };
