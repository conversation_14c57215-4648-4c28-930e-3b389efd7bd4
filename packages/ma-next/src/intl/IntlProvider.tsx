import React from 'react';
import { IntlProvider as ReactIntlProvider } from 'react-intl';
import { enMessages } from './messages';

interface IntlProviderProps {
  children: React.ReactNode;
}

function IntlProvider({ children }: IntlProviderProps) {
  // For now, we'll just use English, but this could be expanded to support multiple languages
  const locale = 'en';
  const messages = enMessages;

  return (
    <ReactIntlProvider locale={locale} messages={messages} defaultLocale="en">
      {children}
    </ReactIntlProvider>
  );
}

export { IntlProvider };
