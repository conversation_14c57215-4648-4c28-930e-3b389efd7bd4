import { FormattedRelativeTime as ReactIntlFormattedRelativeTime } from 'react-intl';

type FormattedRelativeTimeProps = Omit<
  Parameters<typeof ReactIntlFormattedRelativeTime>[0],
  'value'
> & {
  value: number | string;
  type?: 'past' | 'future';
};

function FormattedRelativeTime({ value, type, ...props }: FormattedRelativeTimeProps) {
  // If input is number, we assume it's already in seconds
  if (typeof value === 'number') {
    // If type is past and the value is positive, make it negative
    const adjustedValue = type === 'past' && value > 0 ? -value : value;
    return <ReactIntlFormattedRelativeTime value={adjustedValue} unit="second" {...props} />;
  }

  // If input is string, assume ISO in UTC
  let isoDate = value;
  if (!value.endsWith('Z')) {
    isoDate = `${value}Z`;
  }

  const date = new Date(isoDate);
  const now = new Date();

  // Calculate the difference in seconds
  const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000);

  // Adjust the sign based on type if needed
  const adjustedDiff =
    type === 'past'
      ? -Math.abs(diffInSeconds)
      : type === 'future'
        ? Math.abs(diffInSeconds)
        : diffInSeconds;

  return (
    <ReactIntlFormattedRelativeTime
      value={adjustedDiff}
      unit="second"
      numeric="auto"
      updateIntervalInSeconds={60}
      {...props}
    />
  );
}

export { FormattedRelativeTime };
