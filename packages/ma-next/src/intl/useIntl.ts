import { useIntl as useReactIntl } from 'react-intl';

/**
 * @hook useIntl -
 */
const useIntl = () => {
  const { formatMessage, ...rest } = useReactIntl();

  return {
    // receives a string or an object with id and values
    t: (id: string | { id: string; values: Record<string, string> }) => {
      if (typeof id === 'string') {
        return formatMessage({ id });
      }
      return formatMessage({ id: id.id }, id.values);
    },
    ...rest,
  };
};

export { useIntl };
