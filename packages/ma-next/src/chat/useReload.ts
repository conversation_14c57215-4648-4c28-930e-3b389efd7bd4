import { useCallback } from 'react';
import { supabase } from 'lib/supabase';
import { Message } from './protocol';

type UseReloadProps = {
  id: string;
  isNew: boolean;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  setStatus: React.Dispatch<React.SetStateAction<'ready' | 'streaming' | 'submitted' | 'error'>>;
  setError: React.Dispatch<React.SetStateAction<Error | null>>;
  setCurrentTaskflowId: React.Dispatch<React.SetStateAction<string | null>>;
  setMode: React.Dispatch<React.SetStateAction<'agent' | 'task'>>;
};

/**
 * Custom hook to handle reloading conversation messages
 */
function useReload({
  id,
  isNew,
  setMessages,
  setStatus,
  setError,
  setCurrentTaskflowId,
  setMode,
}: UseReloadProps) {
  const reload = useCallback(async () => {
    // If it's a new chat, just reset messages
    if (isNew) {
      setMessages([]);
      setCurrentTaskflowId(null); // Reset taskflow ID for new chats
      setStatus('ready'); // Ensure status is ready for new chats
      return;
    }

    try {
      setStatus('submitted');
      setError(null); // Clear previous errors

      const { data: conversation, error } = await supabase
        .from('conversations')
        .select('messages, currentTaskflowId, "mode"')
        .eq('id', id)
        .single();

      if (error) {
        // If the error is "not found", treat as a new chat
        if (error.code === 'PGRST116') {
          setMessages([]);
          setCurrentTaskflowId(null);
          setStatus('ready');
          return;
        }

        console.error('Error loading conversation:', error);
        setError(error as Error);
        setStatus('error');
        return;
      }

      if (conversation?.messages) {
        const loadedMessages = conversation.messages.map((msg: any) => ({
          ...msg,
          createdAt: new Date(msg.createdAt),
        }));
        setMessages(loadedMessages);
      } else {
        // If no messages found, reset to empty state
        setMessages([]);
      }
      setCurrentTaskflowId(conversation.currentTaskflowId ?? null);

      // Set the mode if it exists in the conversation
      if (conversation.mode) {
        setMode(conversation.mode);
      }

      setStatus('ready');
    } catch (e) {
      console.error('Error reloading messages:', e);
      setError(e as Error);
      setStatus('error');
    }
  }, [id, isNew, setMessages, setStatus, setError, setCurrentTaskflowId, setMode]);

  return reload;
}

export { useReload };
