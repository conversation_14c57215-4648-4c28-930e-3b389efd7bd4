import { preprocessMessageParts } from '../preprocessMessageParts';
import { MessagePart, ToolCallPart, ToolResultPart } from '../../protocol';
import { ActionCallGroupInvocation, ToolInvocationPart } from '../../protocol/ui';

describe('preprocessMessageParts', () => {
  // Test case 1: Basic conversion of tool_call to tool_invocation
  test('should convert tool_call parts to tool_invocation parts', () => {
    const toolCallPart: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_123',
        toolName: 'actionCall',
        args: { providerKey: 'test', actionKey: 'testTool', actionParameters: { input: 'test' } },
      },
    };
    const messageParts: MessagePart[] = [toolCallPart];
    const processed = preprocessMessageParts(messageParts);

    expect(processed.length).toBe(1);
    expect(processed[0].type).toBe('tool_invocation');
    const invocation = processed[0] as ToolInvocationPart;
    expect(invocation.content.toolCallId).toBe('call_123');
    expect(invocation.content.toolName).toBe('testTool');
    expect(invocation.content.parameters).toEqual({ input: 'test' });
  });

  // Test case 2: Merging tool_call and tool_result
  test('should merge tool_call and tool_result parts into a single tool_invocation', () => {
    const toolCallPart: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_456',
        toolName: 'actionCall',
        args: { providerKey: 'test', actionKey: 'anotherTool', actionParameters: { data: 'abc' } },
      },
    };
    const toolResultPart: ToolResultPart = {
      type: 'tool_result',
      content: {
        toolCallId: 'call_456',
        result: { success: true, data: { output: 'success' } },
      },
    };
    const messageParts: MessagePart[] = [toolCallPart, toolResultPart];
    const processed = preprocessMessageParts(messageParts);

    expect(processed.length).toBe(1);
    expect(processed[0].type).toBe('tool_invocation');
    const invocation = processed[0] as ToolInvocationPart;
    expect(invocation.content.toolCallId).toBe('call_456');
    expect(invocation.content.toolName).toBe('anotherTool');
    expect(invocation.content.parameters).toEqual({ data: 'abc' });
    expect(invocation.content.result).toEqual({ output: 'success' });
  });

  // Test case 3: Handling non-tool parts
  test('should include non-tool_call and non-tool_result parts', () => {
    const textPart: MessagePart = {
      type: 'text',
      content: 'Hello',
    };
    const messageParts: MessagePart[] = [textPart];
    const processed = preprocessMessageParts(messageParts);

    expect(processed.length).toBe(1);
    expect(processed[0]).toEqual(textPart);
  });

  // Test case 4: Grouping sequential actionCall invocations with confirmation required
  test('should group sequential actionCall invocations if any require confirmation', () => {
    const actionCall1: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_789',
        toolName: 'actionCall',
        args: {
          providerKey: 'test',
          actionKey: 'action1',
          actionParameters: { action: 'action1' },
        },
        confirmationRequired: true,
      },
    };
    const actionCall2: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_012',
        toolName: 'actionCall',
        args: {
          providerKey: 'test',
          actionKey: 'action2',
          actionParameters: { action: 'action2' },
        },
        confirmationRequired: false,
      },
    };
    const messageParts: MessagePart[] = [actionCall1, actionCall2];
    const processed = preprocessMessageParts(messageParts);

    expect(processed.length).toBe(1);
    expect(processed[0].type).toBe('tool_invocation');
    const groupInvocation = processed[0] as ToolInvocationPart;
    expect(groupInvocation.content.toolName).toBe('actionCallGroup');
    const groupContent = groupInvocation.content as ActionCallGroupInvocation;
    expect(groupContent.invocations.length).toBe(2);
    expect(groupContent.invocations[0].toolCallId).toBe('call_789');
    expect(groupContent.invocations[1].toolCallId).toBe('call_012');
  });

  // Test case 5: Not grouping sequential actionCall invocations if none require confirmation
  test('should not group sequential actionCall invocations if none require confirmation', () => {
    const actionCall1: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_345',
        toolName: 'actionCall',
        args: {
          providerKey: 'test',
          actionKey: 'action3',
          actionParameters: { action: 'action3' },
        },
        confirmationRequired: false,
      },
    };
    const actionCall2: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_678',
        toolName: 'actionCall',
        args: {
          providerKey: 'test',
          actionKey: 'action4',
          actionParameters: { action: 'action4' },
        },
        confirmationRequired: false,
      },
    };
    const messageParts: MessagePart[] = [actionCall1, actionCall2];
    const processed = preprocessMessageParts(messageParts);

    expect(processed.length).toBe(2);
    expect(processed[0].type).toBe('tool_invocation');
    expect((processed[0] as ToolInvocationPart).content.toolName).toBe('actionCall');
    expect(processed[1].type).toBe('tool_invocation');
    expect((processed[1] as ToolInvocationPart).content.toolName).toBe('actionCall');
  });

  // Test case 6: Handling actionCall invocations that already have a result
  test('should not group actionCall invocations that already have a result', () => {
    const actionCallWithResult: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_901',
        toolName: 'actionCall',
        args: {
          providerKey: 'test',
          actionKey: 'action5',
          actionParameters: { action: 'action5' },
        },
        result: { success: true, data: { status: 'completed' } },
      },
    };
    const actionCallWithoutResult: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_234',
        toolName: 'actionCall',
        args: {
          providerKey: 'test',
          actionKey: 'action6',
          actionParameters: { action: 'action6' },
        },
        confirmationRequired: true,
      },
    };
    const messageParts: MessagePart[] = [actionCallWithResult, actionCallWithoutResult];
    const processed = preprocessMessageParts(messageParts);

    expect(processed.length).toBe(2);
    expect(processed[0].type).toBe('tool_invocation');
    expect((processed[0] as ToolInvocationPart).content.toolName).toBe('actionCall');
    expect(processed[1].type).toBe('tool_invocation');
    expect((processed[1] as ToolInvocationPart).content.toolName).toBe('actionCall'); // This one is not grouped because the previous one had a result
  });

  // Test case 7: Mixed parts
  test('should handle a mix of different part types', () => {
    const textPart: MessagePart = { type: 'text', content: 'Start' };
    const toolCallPart: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_abc',
        toolName: 'actionCall',
        args: { providerKey: 'test', actionKey: 'tool1', actionParameters: {} },
      },
    };
    const toolResultPart: ToolResultPart = {
      type: 'tool_result',
      content: { toolCallId: 'call_abc', result: { success: true, data: { status: 'done' } } },
    };
    const actionCall1: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_def',
        toolName: 'actionCall',
        args: { providerKey: 'test', actionKey: 'actionCall', actionParameters: {} },
        confirmationRequired: true,
      },
    };
    const actionCall2: ToolCallPart = {
      type: 'tool_call',
      content: {
        toolCallId: 'call_ghi',
        toolName: 'actionCall',
        args: { providerKey: 'test', actionKey: 'actionCall', actionParameters: {} },
      },
    };
    const textPart2: MessagePart = { type: 'text', content: 'End' };

    const messageParts: MessagePart[] = [
      textPart,
      toolCallPart,
      toolResultPart,
      actionCall1,
      actionCall2,
      textPart2,
    ];
    const processed = preprocessMessageParts(messageParts);

    expect(processed.length).toBe(4);
    expect(processed[0]).toEqual(textPart);
    expect(processed[1].type).toBe('tool_invocation');
    expect((processed[1] as ToolInvocationPart).content.toolName).toBe('tool1');
    expect(processed[2].type).toBe('tool_invocation');
    expect((processed[2] as ToolInvocationPart).content.toolName).toBe('actionCallGroup');
    const groupContent = (processed[2] as ToolInvocationPart).content as ActionCallGroupInvocation;
    expect(groupContent.invocations.length).toBe(2);
    expect(groupContent.invocations[0].toolCallId).toBe('call_def');
    expect(groupContent.invocations[1].toolCallId).toBe('call_ghi');
    expect(processed[3]).toEqual(textPart2);
  });
});
