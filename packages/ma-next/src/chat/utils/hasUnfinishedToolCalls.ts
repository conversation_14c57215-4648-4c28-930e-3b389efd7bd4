import { keyBy } from 'lodash';
import { MessagePart, ToolCallPart } from '../protocol';

function getUnfinishedToolParts(parts: MessagePart[]): Record<string, ToolCallPart> {
  return keyBy(
    parts.filter(
      (tc, index) =>
        tc.type === 'tool_call' &&
        !parts
          .slice(index + 1)
          .find(
            tr =>
              tr.type === 'tool_result' &&
              tr.content.toolCallId === (tc as ToolCallPart).content.toolCallId
          )
    ) as ToolCallPart[],
    'content.toolCallId'
  );
}

export { getUnfinishedToolParts };
