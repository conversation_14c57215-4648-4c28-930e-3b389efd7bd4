import { keyBy, merge } from 'lodash-es';
import { MessagePart, ToolCallPart, ToolResultPart } from '../protocol';
import {
  ActionCallInvocation,
  ActionCallGroupInvocation,
  ToolInvocationPart,
} from '../protocol/ui';

function preprocessMessageParts(parts: MessagePart[]): (MessagePart | ToolInvocationPart)[] {
  const toolResultsMap = keyBy(
    parts.filter((part): part is ToolResultPart => part.type === 'tool_result'),
    'content.toolCallId'
  );

  // First, convert all tool_call parts to invocations
  const processedParts = parts.reduce<(MessagePart | ToolInvocationPart)[]>((result, part) => {
    if (part.type === 'tool_call') {
      const toolCall = part as ToolCallPart;
      const toolResult = toolResultsMap[toolCall.content.toolCallId];

      const invocation: ToolInvocationPart = {
        type: 'tool_invocation',
        content: merge({}, toolCall.content, toolResult?.content) as any, // Using 'any' temporarily during the transition
      };

      return [...result, invocation];
    }

    if (part.type !== 'tool_result') {
      return [...result, part];
    }

    return result;
  }, []);

  // Now, group sequential actionCall invocations if ANY of them require confirmation
  return processedParts.reduce<(MessagePart | ToolInvocationPart)[]>(
    (result, part, index, array) => {
      // If this is not a tool_invocation or not an actionCall, just add it to the result
      if (part.type !== 'tool_invocation' || part.content.toolName !== 'actionCall') {
        return [...result, part];
      }

      const invocation = part.content as ActionCallInvocation;

      // Skip if this action already has a result
      if (invocation.result) {
        return [...result, part];
      }

      // Check if we're already processing a group
      const isProcessingGroup =
        result.length > 0 &&
        result[result.length - 1].type === 'tool_invocation' &&
        (result[result.length - 1].content as any).toolName === 'actionCallGroup';

      // If we're already processing a group, add this invocation to it
      if (isProcessingGroup) {
        const lastPart = result[result.length - 1] as ToolInvocationPart;
        const groupContent = lastPart.content as ActionCallGroupInvocation;
        groupContent.invocations.push(invocation);
        return result;
      }

      // Check if this is the start of a new group (next part is also an actionCall without a result)
      const nextPart = array[index + 1];
      const nextInvocation =
        nextPart &&
        nextPart.type === 'tool_invocation' &&
        (nextPart.content as any).toolName === 'actionCall'
          ? (nextPart.content as ActionCallInvocation)
          : null;

      // Start a new group if:
      // 1. There is a next actionCall without a result, AND
      // 2. Either this action or the next action requires confirmation
      const isStartOfGroup =
        nextInvocation &&
        !nextInvocation.result &&
        (invocation.confirmationRequired || nextInvocation.confirmationRequired);

      // If this is the start of a new group, create a group with this invocation
      if (isStartOfGroup) {
        const groupInvocation: ToolInvocationPart = {
          type: 'tool_invocation',
          content: {
            toolName: 'actionCallGroup',
            invocations: [invocation],
          } as ActionCallGroupInvocation,
        };
        return [...result, groupInvocation];
      }

      // Otherwise, just add this invocation to the result
      return [...result, part];
    },
    []
  );
}

export { preprocessMessageParts };
