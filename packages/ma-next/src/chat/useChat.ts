import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { v4 } from 'uuid';
import { Message } from './protocol';
import { UseChatHelpers } from './types';
import { useParams } from 'next/navigation';
import { useConversationSubcription } from './useConversationSubscription';
import { useReload } from './useReload';
import { useAppend } from './useAppend';

/**
 * Custom implementation of useChat hook
 * Provides functionality for chat messaging with streaming support
 */
function useChat(): UseChatHelpers {
  const { chatId: chatIdParam } = useParams();
  const id = useMemo(() => (chatIdParam as string) || v4(), [chatIdParam]);
  const isNew = !chatIdParam;

  const [mode, setMode] = useState<'agent' | 'task'>('agent');
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [status, setStatus] = useState<'ready' | 'streaming' | 'submitted' | 'error'>('ready');
  const [error, setError] = useState<Error | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const previousIdRef = useRef<string>(id);
  const hasLoadedRef = useRef(false);
  const [currentTaskflowId, setCurrentTaskflowId] = useState<string | null>(null);

  // console.info(messages);

  const reload = useReload({
    id,
    isNew,
    setMessages,
    setStatus,
    setError,
    setCurrentTaskflowId,
    setMode,
  });

  const append = useAppend({
    id,
    isNew,
    mode,
    messages,
    setMessages,
    setStatus,
    setError,
    abortControllerRef,
    setCurrentTaskflowId,
  });

  useConversationSubcription({ id, setCurrentTaskflowId });

  // Initial load effect
  useEffect(() => {
    // Skip if we've already loaded or if there are already messages
    if (hasLoadedRef.current || (id && !isNew && messages.length > 0)) {
      return;
    }

    reload();
    hasLoadedRef.current = true;
  }, [id, isNew, messages.length, reload]);

  // Detect ID changes and reload messages
  useEffect(() => {
    if (previousIdRef.current !== id) {
      reload();
      previousIdRef.current = id;
      // Reset hasLoadedRef when ID changes
      hasLoadedRef.current = false;
    }
  }, [id, reload]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setInput(e.target.value);
    },
    []
  );

  const stop = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
      setStatus('ready');
    }
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!input.trim() || status === 'submitted' || status === 'streaming') return;

      const userMessage: Partial<Message> = {
        role: 'user',
        content: input,
      };

      setInput('');
      await append(userMessage);
    },
    [append, input, status]
  );

  return {
    messages,
    input,
    setInput,
    handleInputChange,
    handleSubmit,
    setMessages,
    append,
    reload,
    stop,
    status,
    error,
    id,
    mode,
    setMode,
    currentTaskflowId,
  };
}

export { useChat };
