import {
  <PERSON><PERSON>allTool,
  ActionCallToolResult,
  AskUserToSelectResourceTool,
  AskUserToSelectResourceToolResult,
} from './tools';

export type ActionCallInvocation = ActionCallTool & {
  result?: ActionCallToolResult['result'];
};

export type AskUserToSelectResourceInvocation = AskUserToSelectResourceTool & {
  result?: AskUserToSelectResourceToolResult['result'];
};

export type ActionCallGroupInvocation = {
  toolName: 'actionCallGroup';
  invocations: ActionCallInvocation[];
};

export type ToolInvocationContent =
  | ActionCallInvocation
  | AskUserToSelectResourceInvocation
  | ActionCallGroupInvocation;

export type ToolInvocationPart = {
  type: 'tool_invocation';
  content: ToolInvocationContent;
};
