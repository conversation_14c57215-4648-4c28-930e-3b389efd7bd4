import { MessagePart, TextPart } from './parts';

export type MessageRole = 'user' | 'assistant' | 'system' | 'tool';

export type BaseMessage = {
  id?: string;
  role: MessageRole;
  createdAt?: Date;
};

export type UserMessage = BaseMessage & {
  role: 'user';
  content: string;
  parts: [TextPart];
};

export type AssistantMessage = BaseMessage & {
  role: 'assistant';
  parts: MessagePart[];
  content?: never;
};

export type SystemMessage = BaseMessage & {
  role: 'system';
  content: string;
  parts: [TextPart];
};

export type ToolMessage = BaseMessage & {
  role: 'tool';
  parts: MessagePart[];
  content?: never;
};

export type Message = UserMessage | AssistantMessage | SystemMessage | ToolMessage;

export type ProcessedMessage = {
  parts: MessagePart[];
};
