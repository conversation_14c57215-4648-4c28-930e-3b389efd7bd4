export interface ActionCallArgs {
  providerKey: string;
  actionKey: string;
  actionParameters?: Record<string, unknown>;
  userExplanation?: string;
}

export interface ResourceOption {
  value: string;
  label: string;
}

export interface AskUserToSelectResourceArgs {
  title: string;
  options: ResourceOption[];
}

export type ActionCallTool = {
  toolCallId: string;
  toolName: 'actionCall';
  args: ActionCallArgs;
  connectionRequired?: boolean;
  confirmationRequired?: boolean;
  autoApproved?: boolean;
};

export type AskUserToSelectResourceTool = {
  toolCallId: string;
  toolName: 'askUserToSelectResource';
  args: AskUserToSelectResourceArgs;
};

export type ToolCall = ActionCallTool | AskUserToSelectResourceTool;

export type ToolResultPayload<T> = T | { error: string };

export type ActionCallResult = {
  success: boolean;
  userCancelled?: boolean;
  data?: unknown;
  error?: string;
};

export type ActionCallToolResult = {
  toolCallId: string;
  toolName: 'actionCall';
  result: ToolResultPayload<ActionCallResult>;
};

export type AskUserToSelectResourceToolResult = {
  toolCallId: string;
  toolName: 'askUserToSelectResource';
  result: ToolResultPayload<string>;
};

export type ToolResult = ActionCallToolResult | AskUserToSelectResourceToolResult;
