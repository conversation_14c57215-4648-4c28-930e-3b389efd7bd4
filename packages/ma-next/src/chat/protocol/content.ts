import { Tool<PERSON><PERSON>, ToolResult } from './tools';

export type TextContent = string;
export type ErrorContent = string;

export type TaskflowContent = { id: string };
export type ExecutionContent = { executionId: string };

export type ToolCallContent = ToolCall;

export type ToolResultContent = ToolResult;

export type MessagePartContent =
  | TextContent
  | ErrorContent
  | TaskflowContent
  | ExecutionContent
  | ToolCallContent
  | ToolResultContent;
