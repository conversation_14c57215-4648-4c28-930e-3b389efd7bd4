/*
  This file MUST be maintained as the inverse of ChatProtocolEnqueuer, and MUST produce the same MessagePart[].
*/
import {
  <PERSON><PERSON>r<PERSON>ontent,
  ExecutionContent,
  MessagePart,
  TaskflowContent,
  ToolCallContent,
  ToolResultContent,
} from './protocol';

/**
 * Processes a single line from the ChatProtocolEnqueuer stream.
 *
 * Decodes a chunk (e.g., 0:text, t:{...taskflow}) into a MessagePart
 * @param line - The line to process (e.g., '0:"hello"')
 * @param messageParts - The array of MessageParts to append to
 * @param updateCallback - Callback to notify UI of updates
 */
function processLine(
  line: string,
  messageParts: MessagePart[],
  updateCallback: (parts: MessagePart[]) => void
): void {
  if (!line.trim()) return; // Skip empty lines

  const prefix = line.slice(0, 2);
  const jsonText = line.substring(2);

  try {
    const content = JSON.parse(jsonText);
    switch (prefix) {
      case '0:':
        if (messageParts.length > 0 && messageParts[messageParts.length - 1].type === 'text') {
          const lastPart = messageParts[messageParts.length - 1];
          lastPart.content = (lastPart.content as string) + content;
        } else {
          messageParts.push({ type: 'text', content: content });
        }
        break;
      case 't:':
        messageParts.push({ type: 'taskflow', content: content as TaskflowContent });
        break;
      case 'e:':
        messageParts.push({ type: 'execution', content: content as ExecutionContent });
        break;
      case '9:':
        messageParts.push({ type: 'tool_call', content: content as ToolCallContent });
        break;
      case 'a:':
        messageParts.push({ type: 'tool_result', content: content as ToolResultContent });
        break;
      case '3:':
        messageParts.push({ type: 'error', content: content as ErrorContent });
        break;
      case 'c:':
        messageParts.push({ type: 'creating_agent', content: content });
        break;
      default:
        console.warn(`Unknown prefix: ${prefix}`);
        return;
    }
    updateCallback(messageParts);
  } catch (error) {
    console.error(`Error parsing ${prefix} content:`, error, `Raw: ${jsonText}`);
  }
}

/**
 * Processes a streaming response from ChatProtocolEnqueuer
 * Reads stream, buffers lines, and delegates to processLine
 */
async function processStreamingResponse(
  body: ReadableStream<Uint8Array>,
  updateCallback: (parts: MessagePart[]) => void,
  abortControllerRef: { current: AbortController | null }
): Promise<void> {
  const reader = body.getReader();
  const decoder = new TextDecoder();
  const messageParts: MessagePart[] = [];
  let buffer = '';

  try {
    if (abortControllerRef.current?.signal.aborted) {
      throw new Error('AbortError');
    }

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        processLine(line, messageParts, updateCallback);
      }
    }

    if (buffer.trim()) {
      processLine(buffer, messageParts, updateCallback);
    }
  } catch (error) {
    if (error instanceof Error && error.name !== 'AbortError') {
      throw error;
    }
  } finally {
    reader.releaseLock();
  }
}

export { processLine, processStreamingResponse };
export type { MessagePart };
