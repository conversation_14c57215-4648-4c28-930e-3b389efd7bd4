import { useCallback } from 'react';
import { v4 } from 'uuid';
import { supabase } from 'lib/supabase';
import { Message, MessagePart, ToolResultContent, UserMessage } from './protocol';
import { processStreamingResponse } from 'chat/streamProcessor';
import { format } from 'date-fns';

interface UseAppendProps {
  id: string;
  isNew: boolean;
  mode: 'agent' | 'task';
  messages: Message[]; // Add current messages
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  setStatus: React.Dispatch<React.SetStateAction<'ready' | 'streaming' | 'submitted' | 'error'>>;
  setError: React.Dispatch<React.SetStateAction<Error | null>>;
  abortControllerRef: React.MutableRefObject<AbortController | null>;
  setCurrentTaskflowId: React.Dispatch<React.SetStateAction<string | null>>;
}

type AppendAction =
  | Partial<Message>
  | { confirmedTools: Record<string, boolean> }
  | { toolResults: ToolResultContent[] };

function useAppend({
  id,
  isNew,
  mode,
  messages,
  setMessages,
  setStatus,
  setError,
  abortControllerRef,
  setCurrentTaskflowId,
}: UseAppendProps) {
  const append = useCallback(
    async (action: AppendAction) => {
      const isMessage = 'role' in action;
      const baseParts = !isMessage ? messages[messages.length - 1].parts : [];

      const prompt = isMessage
        ? ({
            id: action.id || v4(),
            role: 'user',
            content: action.content,
            createdAt: action.createdAt || new Date(),
            parts: [{ type: 'text', content: action.content || '' }],
          } as UserMessage)
        : undefined;

      if (prompt) {
        setMessages(prev => [
          ...prev,
          prompt,
          {
            id: v4(),
            role: 'assistant',
            createdAt: new Date(),
            parts: [],
          },
        ]);
      }

      // Call the API to get the assistant's response
      try {
        setStatus('submitted');
        abortControllerRef.current = new AbortController();

        // For streaming responses, we need to use the raw fetch API
        // because supabase.functions.invoke doesn't support streaming responses directly
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session?.access_token) {
          throw new Error('No valid session token');
        }

        const response = await fetch(`/api/submit-prompt`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({
            id,
            message: prompt,
            mode,
            isNew,
            confirmedTools: 'confirmedTools' in action ? action.confirmedTools : undefined,
            toolResults: 'toolResults' in action ? action.toolResults : undefined,
            dateTime: format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok || !response.body) {
          throw new Error('Failed to get streaming response');
        }

        setStatus('streaming');

        // Process the streaming response
        await processStreamingResponse(
          response.body,
          (parts: MessagePart[]) => {
            setMessages(prev => {
              const updated = [...prev];
              const lastIndex = updated.length - 1;
              if (lastIndex >= 0 && updated[lastIndex].role === 'assistant') {
                updated[lastIndex] = {
                  ...updated[lastIndex],
                  parts: [...baseParts, ...parts],
                };
              }
              return updated;
            });

            const lastPart = parts[parts.length - 1];
            if (lastPart.type === 'taskflow') {
              setCurrentTaskflowId(lastPart.content.id);
            }
          },
          abortControllerRef
        );

        setStatus('ready');
      } catch (e: any) {
        if (e.name !== 'AbortError') {
          console.error('Error in streaming response:', e);
          setError(e as Error);
          setStatus('error');
        }
      } finally {
        abortControllerRef.current = null;
      }
    },
    [id, isNew, mode, messages, setMessages, setStatus, setError, abortControllerRef]
  );

  return append;
}

export { useAppend };
