import { useEffect, useRef } from 'react';
import { supabase } from 'lib/supabase';

/**
 * Custom hook to handle realtime subscription for conversation updates
 */
function useConversationSubcription({
  id,
  setCurrentTaskflowId,
}: {
  id: string;
  setCurrentTaskflowId: React.Dispatch<React.SetStateAction<string | null>>;
}) {
  const subscriptionRef = useRef<any>(null);
  // Setup realtime subscription for conversation updates
  useEffect(() => {
    if (!id) return;

    // Subscribe to changes in the conversation
    const subscription = supabase
      .channel(`conversation-${id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'conversations',
          filter: `id=eq.${id}`,
        },
        payload => {
          if (payload.new && payload.new.currentTaskflowId !== undefined) {
            setCurrentTaskflowId(payload.new.currentTaskflowId);
          }
        }
      )
      .subscribe();

    subscriptionRef.current = subscription;

    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
      }
    };
  }, [id]);
}

export { useConversationSubcription };
