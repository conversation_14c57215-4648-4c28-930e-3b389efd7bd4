import { Message } from './protocol';

export interface UseChatHelpers {
  messages: Message[];
  input: string;
  setInput: (input: string) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  setMessages: (messages: Message[] | ((messages: Message[]) => Message[])) => void;
  append: (
    message: Partial<Message> | { confirmedTools: Record<string, boolean> } | { toolResults: any[] }
  ) => Promise<void>;
  reload: () => Promise<void>;
  stop: () => void;
  status: 'ready' | 'streaming' | 'submitted' | 'error';
  error: Error | null;
  id: string; // This will always be a string because we generate one if not provided
  mode: 'agent' | 'task';
  setMode: (mode: 'agent' | 'task') => void;
  currentTaskflowId: string | null;
}

export type ChatContextType = UseChatHelpers & {};
