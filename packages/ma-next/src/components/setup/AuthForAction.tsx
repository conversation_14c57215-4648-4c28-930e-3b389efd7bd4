import { useAuth } from 'hooks/useAuth';
import { useModal } from 'hooks/useModal';
import { FormattedMessage } from 'react-intl';

type AuthForActionProps = {
  id: string;
};

function AuthForAction({ id }: AuthForActionProps) {
  const { isAuthenticated } = useAuth();
  const { openModal } = useModal();

  if (isAuthenticated) {
    return null;
  }

  return (
    <div className="p-4 my-3 border border-blue-100 bg-blue-50 rounded-md dark:bg-gray-800 dark:border-gray-700">
      <h3 className="text-md font-medium text-gray-900 dark:text-white mb-2">
        Sign in to continue
      </h3>

      <p className="text-sm text-gray-600 mb-4 dark:text-gray-300">
        You need to sign in or create an account to perform this action.
      </p>

      <div className="flex gap-2 whitespace-nowrap">
        <button
          onClick={() => openModal('login')}
          className="px-3 sm:px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-500 whitespace-nowrap"
        >
          <FormattedMessage id="auth.login" defaultMessage="Log in" />
        </button>
        <button
          onClick={() => openModal('register')}
          className="px-3 sm:px-4 py-2 text-sm font-medium border border-gray-200 rounded-md hover:bg-gray-50
                     dark:border-gray-700 dark:hover:bg-gray-800 whitespace-nowrap"
        >
          <FormattedMessage id="auth.signup" defaultMessage="Sign up" />
        </button>
      </div>
    </div>
  );
}

export { AuthForAction };
