import { useEffect, useRef } from 'react';
import { useAuth } from 'hooks/useAuth';
import { useTaskflowField } from 'hooks/useTaskflowField';
import { useModal } from 'hooks/useModal';
import { FormattedMessage } from 'react-intl';
import { SetupStep } from '../mode-agent/agent-setup/SetupStep/SetupStep';
import { StepInterface } from '../mode-agent/agent-setup/agentSetupTypes';

function AuthSetupStep(props: StepInterface) {
  const { isAuthenticated } = useAuth();
  const { onChange } = useTaskflowField<boolean>(`steps[${props.stepIndex}].completed`);
  const { openModal } = useModal();

  // Track previous auth state to prevent unnecessary updates
  const prevAuthState = useRef(isAuthenticated);

  // Handle auth step state changes
  useEffect(() => {
    if (prevAuthState.current !== isAuthenticated) {
      onChange(isAuthenticated);
    }
    prevAuthState.current = isAuthenticated;
  }, [isAuthenticated, onChange]);

  return (
    <SetupStep
      {...props}
      text={<FormattedMessage id="agentSetup.auth.setup" />}
      inlineContent={
        <div className="flex gap-2 whitespace-nowrap">
          <button
            onClick={() => openModal('login')}
            className="px-3 sm:px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-full hover:bg-indigo-500 whitespace-nowrap"
          >
            <FormattedMessage id="agentSetup.auth.login" />
          </button>
          <button
            onClick={() => openModal('register')}
            className="px-3 sm:px-4 py-2 text-sm font-medium border border-gray-200 rounded-full hover:bg-gray-50
                       dark:border-gray-700 dark:hover:bg-gray-800 whitespace-nowrap"
          >
            <FormattedMessage id="agentSetup.auth.signup" />
          </button>
        </div>
      }
    />
  );
}

export { AuthSetupStep };
