import { PROVIDER_CONFIGS } from 'src/config/integrationUI';
import { useConnections } from 'hooks/useConnections';
import { useModal } from 'hooks/useModal';
import { ConnectionModal } from 'components/modals/ConnectionModal/ConnectionModal';

type ConnectProviderForActionProps = {
  providerKey: string;
  id: string;
};

function ConnectProviderForAction({ providerKey, id }: ConnectProviderForActionProps) {
  const { connections } = useConnections();
  const { openModal, closeModal, isOpen } = useModal();
  const providerConfig = PROVIDER_CONFIGS[providerKey as keyof typeof PROVIDER_CONFIGS];

  const openConnect = () => {
    openModal(`connection-${providerKey}-${id}`);
  };

  if (connections.find(c => c.providerKey === providerKey)) {
    return (
      <div className="p-4 my-4 border border-green-200 bg-green-50 rounded-md">
        <p className="text-sm text-green-800">Successfully connected to {providerConfig?.name}.</p>
      </div>
    );
  }

  if (!providerConfig) {
    return (
      <div className="p-4 my-4 border border-yellow-200 bg-yellow-50 rounded-md">
        <p className="text-sm text-yellow-800">
          You need to connect a provider to use this action, but the provider configuration was not
          found.
        </p>
      </div>
    );
  }

  const Icon = providerConfig.icon;

  return (
    <>
      <ConnectionModal
        isOpen={isOpen(`connection-${providerKey}-${id}`)}
        onClose={() => {
          closeModal();
        }}
        provider={providerKey}
        onConnected={() => {
          closeModal();
        }}
        onDisconnected={() => {
          closeModal();
        }}
      />

      <div className="p-4 my-3 border border-blue-100 bg-blue-50 rounded-md dark:bg-gray-800 dark:border-gray-700">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 bg-white rounded-md shadow-sm dark:bg-gray-700">
            <Icon className="w-5 h-5" />
          </div>
          <h3 className="text-md font-medium text-gray-900 dark:text-white">
            Connect {providerConfig.name}
          </h3>
        </div>

        <p className="text-sm text-gray-600 mb-4 dark:text-gray-300">
          To perform this action, you need to connect your {providerConfig.name} account.
        </p>

        <button
          onClick={openConnect}
          className="whitespace-nowrap flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 shadow-sm dark:bg-blue-700 dark:hover:bg-blue-600"
        >
          <Icon className="w-4 h-4" />
          Connect {providerConfig.name}
        </button>
      </div>
    </>
  );
}

export { ConnectProviderForAction };
