import { useEffect, useState } from 'react';
import { Tab } from '@headlessui/react';
import clsx from 'clsx';
import { useConnections } from 'hooks/useConnections';
import { connectProvider, deleteConnection, primeNango } from 'lib/nango';
import { useConnectionSyncs } from 'hooks/useConnectionSyncs';
import { connectionSyncsStore } from 'stores/connectionSyncs';
import { PROVIDER_CONFIGS } from '../../config/integrationUI';
import { CheckSquare, Loader2, Plus } from 'lucide-react';
import { AgentsUsingConnection } from './AgentsUsingConnection';

interface ConnectionProps {
  provider: string;
  showBackButton?: boolean;
  onBack?: () => void;
  onConnected?: () => void;
  onDisconnected?: () => void;
  requiredSyncs?: Record<string, any>;
}

function Connection({
  provider,
  showBackButton,
  onBack,
  onConnected,
  onDisconnected,
  requiredSyncs,
}: ConnectionProps) {
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const { connections } = useConnections();
  const hasConnection = connections.some(conn => conn.providerKey === provider);
  const connection = connections.find(c => c.providerKey === provider);
  const config = PROVIDER_CONFIGS[provider as keyof typeof PROVIDER_CONFIGS];

  useEffect(() => {
    primeNango();
  }, []);

  useEffect(() => {
    if (hasConnection) {
      connectionSyncsStore.load(provider);
    }
  }, [hasConnection, provider]);

  // Monitor connection status changes
  useEffect(() => {
    if (hasConnection && isConnecting) {
      setIsConnecting(false);
      if (requiredSyncs) {
        Object.entries(requiredSyncs).forEach(([name, scope]) => {
          connectionSyncsStore.start(provider, { name, metadata: scope });
        });
      }
      onConnected?.();
    }
  }, [hasConnection, isConnecting, onConnected, provider, requiredSyncs]);

  if (!config) return null;

  const Icon = config.icon;
  const headerColor = config.headerColor || 'bg-indigo-600';

  const handleConnect = async () => {
    setIsConnecting(true);
    const [error] = await connectProvider(provider);
    if (error) {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!connection) return;
    setIsDisconnecting(true);
    try {
      await deleteConnection(connection.id, provider);
    } finally {
      setTimeout(() => {
        setIsDisconnecting(false);
        onDisconnected?.();
      }, 600);
    }
  };

  return (
    <div className="flex flex-col min-h-[400px] bg-white dark:bg-gray-800 pb-12">
      {/* Header Banner */}
      <div className={clsx('relative h-24 rounded-t-lg', headerColor)}>
        <div className="absolute -bottom-3 left-6 flex items-center gap-4">
          <div
            className={clsx('p-4 bg-white rounded-lg shadow-md', {
              'dark:bg-black': config.invertInModal,
            })}
          >
            <Icon className="w-14 h-14" />
          </div>
          <div className="text-white -mt-6">
            <h2 className="text-2xl font-semibold">{config.name}</h2>
            <p className="text-sm text-white/80 mt-1">{config.title}</p>
          </div>
        </div>
        <ConnectButton
          hasConnection={hasConnection}
          isConnecting={isConnecting}
          handleConnect={handleConnect}
        />
      </div>

      {/* Content */}
      <div className="mt-10 px-6 flex-1">
        <Tab.Group>
          <Tab.List className="flex gap-6 border-b border-gray-200 dark:border-gray-700">
            <Tab
              className={({ selected }) =>
                clsx(
                  'px-1 py-3 text-sm font-medium border-b-2 focus:outline-none',
                  selected
                    ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                )
              }
            >
              Overview
            </Tab>
            <Tab
              className={({ selected }) =>
                clsx(
                  'px-1 py-3 text-sm font-medium border-b-2 focus:outline-none',
                  selected
                    ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                )
              }
            >
              Configuration
            </Tab>
          </Tab.List>

          <Tab.Panels>
            <Tab.Panel className="py-6">
              <div className="space-y-6">
                <p className="text-gray-600 dark:text-gray-300">{config.description}</p>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">Features:</h3>
                  <ul className="space-y-2">
                    {config.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300"
                      >
                        <span className="w-1.5 h-1.5 bg-indigo-500 rounded-full" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </Tab.Panel>

            <Tab.Panel className="py-6">
              <div className="space-y-12">
                {hasConnection && <SyncList provider={provider} />}
                {hasConnection && <AgentsUsingConnection provider={provider} />}

                <div className="space-y-3">
                  {hasConnection ? (
                    <>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                        Connection Status
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                        Disconnecting will deactivate any agents using this connection
                      </p>
                    </>
                  ) : (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                      Connect to let agents utilize {config.name}
                    </p>
                  )}

                  {!hasConnection ? (
                    <button
                      onClick={handleConnect}
                      disabled={isConnecting}
                      className={clsx(
                        'px-8 py-3 text-sm font-medium rounded-sm inline-flex items-center gap-2',
                        isConnecting
                          ? 'bg-indigo-300 text-white cursor-not-allowed'
                          : 'text-white bg-indigo-600 hover:bg-indigo-500'
                      )}
                    >
                      {isConnecting && <Loader2 className="w-4 h-4 animate-spin" />}
                      {isConnecting ? 'Connecting...' : 'Connect'}
                    </button>
                  ) : (
                    <button
                      onClick={handleDisconnect}
                      disabled={isDisconnecting}
                      className={clsx(
                        'px-6 py-2.5 text-sm font-medium border rounded-sm inline-flex items-center gap-2',
                        isDisconnecting
                          ? 'border-gray-300 text-gray-400 cursor-not-allowed dark:border-gray-600 dark:text-gray-500'
                          : 'border-red-700 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300'
                      )}
                    >
                      {isDisconnecting && <Loader2 className="w-4 h-4 animate-spin" />}
                      {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
                    </button>
                  )}
                </div>
              </div>
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </div>

      {showBackButton && (
        <div className="absolute bottom-1 -left-0.5 mt-auto px-6 py-4 border-gray-200 dark:border-gray-700">
          <button
            onClick={onBack}
            className="text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ← Back to connections
          </button>
        </div>
      )}
    </div>
  );
}

function ConnectButton({
  hasConnection,
  isConnecting,
  handleConnect,
}: {
  hasConnection: boolean;
  isConnecting: boolean;
  handleConnect: () => void;
  mobile?: boolean;
}) {
  return (
    <button
      onClick={hasConnection ? undefined : handleConnect}
      disabled={hasConnection || isConnecting}
      className={clsx(
        'absolute right-6 top-1/2 -translate-y-1/2 text-sm font-medium transition-colors sm:px-8 sm:py-3.5 px-3 py-3 sm:rounded-sm rounded-lg sm: ',
        {
          'bg-white/20 text-white cursor-default': hasConnection,
          'bg-white/80 text-gray-500 cursor-wait': isConnecting,
          'bg-white text-gray-900 hover:bg-gray-50': !hasConnection && !isConnecting,
        }
      )}
    >
      <span className="hidden sm:inline">
        {hasConnection ? 'Connected' : isConnecting ? 'Connecting...' : 'Connect'}
      </span>
      <span className="sm:hidden">
        {hasConnection ? (
          <CheckSquare className="w-5 h-5" />
        ) : isConnecting ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          <Plus className="w-5 h-5" />
        )}
      </span>
    </button>
  );
}

function SyncList({ provider }: { provider: string }) {
  const { syncs, start, pause } = useConnectionSyncs(provider);
  const config = PROVIDER_CONFIGS[provider as keyof typeof PROVIDER_CONFIGS];

  if (!syncs.length || !config?.syncs) return null;

  return (
    <div className="space-y-4 mt-4 mb-10">
      <h3 className="text-sm font-medium text-gray-900 dark:text-white">Syncs</h3>

      {syncs.map(sync => {
        const isActive = sync.status !== 'PAUSED' && sync.status !== 'STOPPED';
        const toggle = () => (isActive ? pause(sync.name) : start(sync.name));
        const meta = config.syncs?.[sync.name as keyof typeof config.syncs];
        return (
          <div key={sync.name} className="flex items-start gap-4">
            <button
              onClick={toggle}
              className={clsx(
                'relative inline-flex h-7 w-12 items-center rounded-full transition-colors focus:outline-none',
                {
                  'bg-green-500': isActive,
                  'bg-gray-200 dark:bg-gray-700': !isActive,
                }
              )}
            >
              <span
                className={clsx(
                  'inline-block h-5 w-5 transform rounded-full bg-white transition-transform',
                  {
                    'translate-x-6': isActive,
                    'translate-x-1': !isActive,
                  }
                )}
              />
            </button>
            <div>
              <div className="text-sm text-gray-800 dark:text-gray-200 font-medium">
                {meta?.label || sync.name}
              </div>
              {meta?.description && (
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                  {meta.description}
                </p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}

export { Connection };
