import { connectionsStore } from 'stores/connections';
import { SetupStep } from '../mode-agent/agent-setup/SetupStep/SetupStep';
import { StepInterface } from '../mode-agent/agent-setup/agentSetupTypes';
import { ConnectionModal } from 'components/modals/ConnectionModal/ConnectionModal';
import { PROVIDER_CONFIGS } from 'src/config/integrationUI';
import { Pencil } from 'lucide-react';
import { useTaskflowField } from 'hooks/useTaskflowField';
import { useModal } from 'hooks/useModal';

function ConnectProvider(props: StepInterface) {
  const { step, stepIndex } = props;
  const provider = step.provider!;
  const providerConfig = PROVIDER_CONFIGS[provider as keyof typeof PROVIDER_CONFIGS];
  const hasConnection = connectionsStore.useHasConnection(provider);
  const { onChange } = useTaskflowField<boolean>(`steps[${stepIndex}].completed`);
  const { openModal, closeModal, isOpen } = useModal();

  const openConnect = () => {
    openModal(`connection-${provider}`);
  };

  return (
    <>
      <ConnectionModal
        isOpen={isOpen(`connection-${provider}`)}
        onClose={() => {
          closeModal();
        }}
        provider={provider}
        requiredSyncs={step.syncScopes as Record<string, any> | undefined}
        onConnected={() => {
          closeModal();
          onChange(true);
        }}
        onDisconnected={() => {
          closeModal();
          onChange(false);
        }}
      />

      <SetupStep
        {...props}
        text={
          hasConnection
            ? `You are connected to ${providerConfig.name}`
            : `Connect your ${providerConfig.name} account`
        }
        actions={
          hasConnection ? (
            <button
              onClick={openConnect}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
            >
              <Pencil className="w-4 h-4" />
            </button>
          ) : (
            <button
              onClick={openConnect}
              className="whitespace-nowrap flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-full hover:bg-gray-50 border border-gray-300 shadow-sm dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700 dark:hover:bg-gray-700"
            >
              <providerConfig.icon className="w-4 h-4" />
              Connect {providerConfig.name}
            </button>
          )
        }
      />
    </>
  );
}

export { ConnectProvider };
