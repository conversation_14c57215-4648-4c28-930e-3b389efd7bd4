import { Bot, ExternalLink, Database } from 'lucide-react';
import { useAgentsStore } from 'stores/agents';
import { formatSyncScope } from 'utils/formatSyncScope';
import { Conversation } from 'src/types';

interface AgentsUsingConnectionProps {
  provider: string;
}

function AgentsUsingConnection({ provider }: AgentsUsingConnectionProps) {
  const [{ agents }] = useAgentsStore();

  const usingAgents = agents.filter(
    conv => conv.agent?.active && conv.agent.triggers.some(t => t.providerKey === provider)
  );

  if (usingAgents.length === 0) return null;

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
          Active Agents ({usingAgents.length})
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          These agents are currently using this connection.
        </p>
      </div>

      <div className="space-y-3">
        {usingAgents.map(conv => {
          const summaries = conv
            .agent!.triggers.filter(t => t.providerKey === provider)
            .map(t => t.syncScope && formatSyncScope(t.syncScope))
            .filter(Boolean);

          const scopeText = summaries.length ? summaries.join('; ') : 'All available records';

          return <AgentCard key={conv.id} agent={conv} scopeText={scopeText} />;
        })}
      </div>
    </div>
  );
}

interface AgentCardProps {
  agent: Conversation;
  scopeText: string;
}

function AgentCard({ agent, scopeText }: AgentCardProps) {
  return (
    <div className="group bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-indigo-300 dark:hover:border-indigo-600 transition-colors">
      <div className="flex items-center justify-between">
        {/* Left side - Agent info */}
        <div className="flex items-center gap-4 flex-1 min-w-0">
          <div className="relative flex-shrink-0">
            <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900/50 rounded-full flex items-center justify-center">
              <Bot className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
            </div>
            {/* Green active dot */}
            <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
          </div>
          <div className="flex-1 min-w-0">
            <a
              href={`/chat/${agent.id}`}
              className="text-sm text-gray-900 dark:text-white hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors truncate block"
            >
              {agent.title || 'Untitled Agent'}
            </a>
          </div>
        </div>

        {/* Center - Data scope */}
        <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-md text-xs text-gray-600 dark:text-gray-400">
          <Database className="w-3.5 h-3.5 text-gray-400 dark:text-gray-500" />
          <span className="truncate max-w-32">{scopeText}</span>
        </div>

        {/* Right side - Action */}
        <div className="flex-shrink-0 ml-4">
          <a
            href={`/chat/${agent.id}`}
            className="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-md transition-colors"
          >
            Open
            <ExternalLink className="w-3 h-3" />
          </a>
        </div>
      </div>
    </div>
  );
}

export { AgentsUsingConnection };
