import { useState, useEffect } from 'react';
import { Modal } from './Modal';
import { useAuth } from 'hooks/useAuth';
import { useProfile } from 'hooks/useProfile';
import { M } from 'intl';
import { useModal } from 'hooks/useModal';

function ProfileModal() {
  const { user } = useAuth();
  const { profile, isLoading, updateProfile } = useProfile();
  const [localProfile, setLocalProfile] = useState({
    firstName: '',
    lastName: '',
  });
  const [isSaving, setIsSaving] = useState(false);
  const { isOpen: getIsOpen, closeModal } = useModal();
  const isOpen = getIsOpen('profile');

  // Update local state when profile is loaded
  useEffect(() => {
    if (profile) {
      setLocalProfile({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
      });
    }
  }, [profile]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsSaving(true);
    try {
      await updateProfile({
        firstName: localProfile.firstName,
        lastName: localProfile.lastName,
      });
      closeModal();
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal isOpen={isOpen} title={<M id="profile.title" />}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2">
            <M id="profile.email" />
          </label>
          <input
            type="email"
            value={user?.email || ''}
            disabled
            className="w-full px-3 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            <M id="profile.firstName" />
          </label>
          <input
            type="text"
            value={localProfile.firstName}
            onChange={e => setLocalProfile(prev => ({ ...prev, firstName: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            <M id="profile.lastName" />
          </label>
          <input
            type="text"
            value={localProfile.lastName}
            onChange={e => setLocalProfile(prev => ({ ...prev, lastName: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        <div className="flex justify-end gap-3">
          <button
            type="button"
            onClick={closeModal}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            <M id="common.cancel" />
          </button>
          <button
            type="submit"
            disabled={isSaving}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-500 disabled:bg-indigo-400"
          >
            {isSaving ? <M id="common.saving" /> : <M id="common.save" />}
          </button>
        </div>
      </form>
    </Modal>
  );
}

export { ProfileModal };
