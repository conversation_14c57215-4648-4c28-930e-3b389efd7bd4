import { useState, useEffect } from 'react';
import { Modal } from './Modal';
import { Mail, Clock, Globe, Search } from 'lucide-react';
import { usePrompt } from 'hooks/usePrompt';

interface AgentBrowseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

function AgentBrowseModal({ isOpen, onClose }: AgentBrowseModalProps) {
  const { setText } = usePrompt();
  const [searchTerm, setSearchTerm] = useState('');

  // Reset search term when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm('');
    }
  }, [isOpen]);

  const agents = [
    {
      id: 'email-assistant',
      name: 'Email Assistant',
      description:
        'Automatically draft responses to incoming emails. Save time on routine email responses while maintaining control over what gets sent.',
      prompt: 'Create an email assistant that automatically drafts responses to my incoming emails',
      icons: [
        <Mail key="mail-1" className="w-6 h-6 text-blue-500" />,
        <Mail key="mail-2" className="w-6 h-6 text-indigo-600" />,
      ],
    },
    {
      id: 'weather-report',
      name: 'Weather Report',
      description:
        "Get a daily email with the weather forecast for your location. Stay prepared for the day's weather conditions without checking manually.",
      prompt: 'Create a daily weather report agent that emails me the forecast every morning',
      icons: [
        <Clock key="clock" className="w-6 h-6 text-amber-500" />,
        <Mail key="mail" className="w-6 h-6 text-indigo-600" />,
      ],
    },
    {
      id: 'website-monitor',
      name: 'Website Monitor',
      description:
        'Monitor websites for changes and receive notifications. Perfect for tracking price changes, content updates, or availability.',
      prompt: 'Create an agent that monitors a website for changes and sends me notifications',
      icons: [
        <Globe key="globe" className="w-6 h-6 text-green-500" />,
        <Mail key="mail" className="w-6 h-6 text-indigo-600" />,
      ],
    },
  ];

  // Filter agents based on search term
  const filteredAgents = agents.filter(
    agent =>
      agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectAgent = (prompt: string) => {
    setText(prompt);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Browse Agents" mediumSize closeOnOutsideClick>
      <div className="mb-4 relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search agents..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
      </div>

      {filteredAgents.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">No agents match your search</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredAgents.map(agent => (
            <div
              key={agent.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-5 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleSelectAgent(agent.prompt)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <div className="flex space-x-1">
                    {agent.icons.map((icon, index) => (
                      <span key={index}>{icon}</span>
                    ))}
                  </div>
                  <h3 className="font-medium text-gray-900 dark:text-white ml-3">{agent.name}</h3>
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 pr-16 relative">
                {agent.description}
                <button
                  className="absolute right-0 top-1/2 -translate-y-1/2 text-xs font-medium text-white bg-indigo-600 hover:bg-indigo-500 dark:bg-indigo-500 dark:hover:bg-indigo-400 px-3 py-1 rounded-full"
                  onClick={e => {
                    e.stopPropagation();
                    handleSelectAgent(agent.prompt);
                  }}
                >
                  Make
                </button>
              </p>
            </div>
          ))}
        </div>
      )}
    </Modal>
  );
}

export { AgentBrowseModal };
