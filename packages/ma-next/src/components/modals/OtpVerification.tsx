import React, { useState, useRef, useEffect } from 'react';
import { M, useIntl } from 'intl';
import clsx from 'clsx';
import { useAuth } from 'hooks/useAuth';

interface OtpVerificationProps {
  onVerify: (code: string) => void;
  onResend: () => void;
}

const EMPTY_CODE = ['', '', '', '', '', ''];

function OtpVerification({ onVerify, onResend }: OtpVerificationProps) {
  const [code, setCode] = useState(EMPTY_CODE);
  const refs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];
  const { t } = useIntl();
  const { error } = useAuth();

  useEffect(() => {
    // Focus first input on mount
    refs[0].current?.focus();
  }, []);

  const handleInput = (index: number, value: string) => {
    if (value.length > 1) {
      // Handle paste
      const chars = value.split('').slice(0, 6);
      const newCode = [...code];
      chars.forEach((char, i) => {
        if (i + index < 6) {
          newCode[i + index] = char;
        }
      });
      setCode(newCode);

      // Focus last input or next empty input
      const nextEmptyIndex = newCode.findIndex(c => !c);
      if (nextEmptyIndex === -1) {
        refs[5].current?.focus();
        onVerify(newCode.join(''));
      } else {
        refs[nextEmptyIndex].current?.focus();
      }
    } else {
      // Handle single character
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      if (value && index < 5) {
        refs[index + 1].current?.focus();
      }

      if (newCode.every(c => c)) {
        onVerify(newCode.join(''));
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      refs[index - 1].current?.focus();
    }
  };

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-600 dark:text-gray-400">{t('auth.otpInstructions')}</p>

      <div className="flex justify-center gap-2">
        {code.map((digit, i) => (
          <input
            key={i}
            ref={refs[i]}
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            maxLength={6}
            value={digit}
            onChange={e => handleInput(i, e.target.value)}
            onKeyDown={e => handleKeyDown(i, e)}
            className={clsx(
              'w-12 h-12 text-center text-xl border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent',
              error ? 'border-red-300' : 'border-gray-300 dark:border-gray-600',
              'dark:bg-gray-700 dark:text-white'
            )}
          />
        ))}
      </div>

      {error && (
        <p className="text-sm text-red-600 dark:text-red-400 text-center">
          <M id={error} />
        </p>
      )}

      <button
        onClick={() => {
          onResend();
          setCode(EMPTY_CODE);
        }}
        className="w-full text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300"
      >
        {t('auth.resendCode')}
      </button>
    </div>
  );
}
export { OtpVerification };
