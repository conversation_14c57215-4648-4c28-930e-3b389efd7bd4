import { useEffect } from 'react';
import { getCurrentUserId } from '../../../lib/supabase';
import { agentsStore } from '../../../stores/agents';

/**
 * Hook to optimistically load conversations when the app loads
 * This hook doesn't return any data - it just loads conversations in the background
 * to improve performance when opening the Manage Agents modal
 */
function useOptimisticConversations() {
  useEffect(() => {
    let isMounted = true;

    // Function to load conversations
    const loadConversations = async () => {
      try {
        if (!(await getCurrentUserId())) return;

        // In ma-next, we can directly call the loadAgents method
        if (isMounted && agentsStore) {
          await agentsStore.loadAgents();
        }
      } catch (error) {
        console.error('Failed to optimistically load conversations:', error);
      }
    };

    // Load conversations with low priority
    if ('requestIdleCallback' in window) {
      // Use requestIdleCallback if available (modern browsers)
      window.requestIdleCallback(() => {
        if (isMounted) loadConversations();
      });
    } else {
      // Fallback to setTimeout with a delay
      setTimeout(() => {
        if (isMounted) loadConversations();
      }, 1000);
    }

    return () => {
      isMounted = false;
    };
  }, []);
}

export { useOptimisticConversations };
