import { Bo<PERSON> } from 'lucide-react';
import { Conversation } from '../../../types';
import { AgentCard } from './AgentCard';
import { EmptyState } from './EmptyState';

interface AgentsListProps {
  conversations: Conversation[];
  isLoading: boolean;
  searchTerm: string;
  onSelectConversation: (id: string) => void;
  onDeleteConversation: (id: string) => void;
  onEditConversation: (id: string, title: string) => void;
  onCreateNewConversation: () => void;
}

function AgentsList({
  conversations,
  isLoading,
  searchTerm,
  onSelectConversation,
  onDeleteConversation,
  onEditConversation,
  onCreateNewConversation,
}: AgentsListProps) {
  const filteredConversations = conversations.filter(conversation =>
    conversation.title?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-pulse">
          <Bot className="w-12 h-12 text-indigo-500 opacity-20" />
        </div>
      </div>
    );
  }

  if (filteredConversations.length === 0) {
    return <EmptyState searchTerm={searchTerm} onCreateNewConversation={onCreateNewConversation} />;
  }

  return (
    <div className="overflow-y-auto max-h-[500px]">
      {filteredConversations.map(conversation => (
        <AgentCard
          key={conversation.id}
          conversation={conversation}
          onSelect={onSelectConversation}
          onDelete={onDeleteConversation}
          onEdit={onEditConversation}
        />
      ))}
    </div>
  );
}

export { AgentsList };
