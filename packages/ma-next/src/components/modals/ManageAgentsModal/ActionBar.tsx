import { SearchBar } from './SearchBar';

interface ActionBarProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  includeConversations: boolean;
  setIncludeConversations: (include: boolean) => void;
  showActiveOnly: boolean;
  setShowActiveOnly: (show: boolean) => void;
}

function ActionBar({
  searchTerm,
  setSearchTerm,
  includeConversations,
  setIncludeConversations,
  showActiveOnly,
  setShowActiveOnly,
}: ActionBarProps) {
  return (
    <div className="mb-6 space-y-3">
      <SearchBar searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 px-1">
        <label className="flex items-center text-sm text-gray-600 dark:text-gray-400 cursor-pointer min-h-[40px]">
          <div className="w-[40px] h-[40px] flex items-center justify-center">
            <input
              type="checkbox"
              checked={includeConversations}
              onChange={e => setIncludeConversations(e.target.checked)}
              className="w-5 h-5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700"
            />
          </div>
          <span>Include conversations without agents</span>
        </label>
        <label className="flex items-center text-sm text-gray-600 dark:text-gray-400 cursor-pointer min-h-[40px]">
          <div className="w-[40px] h-[40px] flex items-center justify-center">
            <input
              type="checkbox"
              checked={showActiveOnly}
              onChange={e => setShowActiveOnly(e.target.checked)}
              className="w-5 h-5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700"
            />
          </div>
          <span>Active only</span>
        </label>
      </div>
    </div>
  );
}

export { ActionBar };
