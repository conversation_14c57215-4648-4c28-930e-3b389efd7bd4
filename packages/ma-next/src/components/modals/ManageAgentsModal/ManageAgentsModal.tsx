import { Modal } from '../Modal';
import { ActionBar } from './ActionBar';
import { AgentsList } from './AgentsList';
import { useAgentManagement } from './useAgentManagement';
import { useOptimisticConversations } from './useOptimisticConversations';
import { useModal } from 'hooks/useModal';

function ManageAgentsModal() {
  const { isOpen } = useModal();
  const {
    conversations,
    isLoading,
    searchTerm,
    setSearchTerm,
    includeConversations,
    setIncludeConversations,
    showActiveOnly,
    setShowActiveOnly,
    handleSelectConversation,
    handleCreateNewConversation,
    handleDeleteConversation,
    handleEditConversation,
  } = useAgentManagement();

  useOptimisticConversations();

  return (
    <Modal isOpen={isOpen('manageAgents')} mediumSize hideHeader closeOnOutsideClick>
      <div className="flex flex-col h-full min-h-[500px] pt-4 px-2 sm:px-4">
        <ActionBar
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          includeConversations={includeConversations}
          setIncludeConversations={setIncludeConversations}
          showActiveOnly={showActiveOnly}
          setShowActiveOnly={setShowActiveOnly}
        />

        <AgentsList
          conversations={conversations}
          isLoading={isLoading}
          searchTerm={searchTerm}
          onSelectConversation={handleSelectConversation}
          onDeleteConversation={handleDeleteConversation}
          onEditConversation={handleEditConversation}
          onCreateNewConversation={handleCreateNewConversation}
        />
      </div>
    </Modal>
  );
}

export { ManageAgentsModal };
