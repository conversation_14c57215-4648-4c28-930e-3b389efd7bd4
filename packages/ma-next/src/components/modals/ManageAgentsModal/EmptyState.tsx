import { Bot } from 'lucide-react';
import { FormattedMessage } from 'react-intl';

interface EmptyStateProps {
  searchTerm: string;
  onCreateNewConversation: () => void;
}

function EmptyState({ searchTerm, onCreateNewConversation }: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
      <Bot className="w-16 h-16 mb-4 opacity-20" />
      {searchTerm ? (
        <FormattedMessage id="manageAgents.noMatches" />
      ) : (
        <>
          <p className="text-xl font-medium mb-4">
            <FormattedMessage id="manageAgents.noAgents" />
          </p>
          <button
            onClick={onCreateNewConversation}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500 transition-colors"
          >
            <FormattedMessage id="manageAgents.createNew" defaultMessage="Create New" />
          </button>
        </>
      )}
    </div>
  );
}

export { EmptyState };
