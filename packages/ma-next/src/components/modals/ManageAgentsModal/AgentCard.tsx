import { Trash2, <PERSON><PERSON><PERSON>re, Pencil, X } from 'lucide-react';
import { Bot } from 'lucide-react';
import { Conversation } from '../../../types';
import { M, FormattedRelativeTime } from 'intl';
import { useState, useRef, useEffect } from 'react';
import clsx from 'clsx';

interface AgentCardProps {
  conversation: Conversation;
  onSelect: (id: string) => void;
  onDelete: (id: string) => void;
  onEdit: (id: string, title: string) => void;
}

function AgentCard({ conversation, onSelect, onDelete, onEdit }: AgentCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(conversation.title || '');
  const inputRef = useRef<HTMLInputElement>(null);
  const hasAgent = conversation.currentTaskflowId !== null && conversation.mode === 'agent';
  const isAgentActive = conversation.agent?.active;

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isDeleting) {
      onDelete(conversation.id);
      setIsDeleting(false);
    } else {
      setIsDeleting(true);
    }
  };

  const handleCancelDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleting(false);
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  const handleTitleSubmit = () => {
    if (title.trim() !== conversation.title) {
      onEdit(conversation.id, title.trim());
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleTitleSubmit();
    } else if (e.key === 'Escape') {
      setTitle(conversation.title || '');
      setIsEditing(false);
    }
  };

  return (
    <div
      key={conversation.id}
      onClick={() => !isEditing && onSelect(conversation.id)}
      className="flex items-center justify-between p-2 sm:p-4 mb-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow cursor-pointer"
    >
      <div className="flex items-center">
        <div className="relative flex-shrink-0 w-10 h-10">
          <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
            {hasAgent ? (
              <Bot className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
            ) : (
              <MessageSquare className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            )}
          </div>
          {hasAgent && (
            <div
              className={clsx(
                'absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800',
                isAgentActive ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-400 dark:bg-gray-500'
              )}
            />
          )}
        </div>
        <div className="ml-3 overflow-hidden min-w-0">
          {isEditing ? (
            <input
              ref={inputRef}
              type="text"
              value={title}
              onChange={handleTitleChange}
              onBlur={handleTitleSubmit}
              onKeyDown={handleKeyDown}
              className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-indigo-500 dark:border-indigo-400 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
              onClick={e => e.stopPropagation()}
            />
          ) : (
            <h3 className="font-medium text-gray-900 dark:text-white truncate">
              {conversation.title || <M id="manageAgents.untitled" />}
            </h3>
          )}
          <p className="text-xs text-gray-500 dark:text-gray-400">
            <M
              id="manageAgents.updated"
              values={{
                date: <FormattedRelativeTime value={conversation.updatedAt} />,
              }}
            />
          </p>
        </div>
      </div>

      <div className="flex items-center ml-2">
        {/* Mail icons commented out as requested
        {hasAgent && (
          <div className="hidden sm:flex items-center mr-4">
            <Mail className="w-5 h-5 text-gray-400 mr-1" />
            <Mail className="w-5 h-5 text-gray-400" />
          </div>
        )}
        */}

        <div className="flex items-center">
          {isDeleting ? (
            <>
              <button
                className="w-[40px] h-[40px] flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                onClick={handleCancelDelete}
              >
                <X className="w-4 h-4" />
              </button>
              <button
                className="w-[40px] h-[40px] flex items-center justify-center text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg"
                onClick={handleDelete}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </>
          ) : (
            <>
              {!isEditing && (
                <button
                  className="w-[40px] h-[40px] flex items-center justify-center text-gray-400 hover:text-indigo-500 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                  onClick={handleEditClick}
                >
                  <Pencil className="w-4 h-4" />
                </button>
              )}
              <button
                className="w-[40px] h-[40px] flex items-center justify-center text-gray-400 hover:text-red-500 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                onClick={handleDelete}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export { AgentCard };
