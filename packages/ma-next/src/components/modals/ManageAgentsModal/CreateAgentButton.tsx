import { Bot } from 'lucide-react';
import { FormattedMessage } from 'react-intl';

interface CreateAgentButtonProps {
  onClick: () => void;
}

function CreateAgentButton({ onClick }: CreateAgentButtonProps) {
  return (
    <button
      onClick={onClick}
      className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500"
    >
      <Bot className="w-5 h-5" />
      <FormattedMessage id="navbar.newAgent" />
    </button>
  );
}

export { CreateAgentButton };
