import React, { useState } from 'react';
import { Modal } from './Modal';
import { M, useIntl } from 'intl';
import { OtpVerification } from './OtpVerification';
import clsx from 'clsx';
import { useModal } from 'hooks/useModal';
import { useAuth } from 'hooks/useAuth';
import { Spinner } from 'components/Spinner';
type AuthModals = 'login' | 'register' | 'reset' | 'otc' | 'setPassword';

const TITLES = {
  login: <M id="auth.loginTitle" />,
  register: <M id="auth.signupTitle" />,
  reset: <M id="auth.resetPassword" />,
  otc: <M id="auth.verifyEmail" />,
  setPassword: <M id="auth.setPassword" />,
};

function AuthModal() {
  const { t } = useIntl();
  const {
    register,
    resetPassword,
    verifyOtp,
    error,
    verificationMode,
    resetLoginForm,
    clearError,
  } = useAuth();
  const { closeModal, activeModal } = useModal();

  const [modalScreen, setModalScreen] = useState<AuthModals>(activeModal as 'login' | 'register');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const { onSubmit, isSubmitting } = useSubmission({
    setModalScreen,
    closeModal,
    email,
    password,
    modalScreen,
  });

  const handleModeSwitch = (newMode: AuthModals) => {
    setModalScreen(newMode);
    if (newMode !== 'reset') {
      setEmail('');
    }
    setPassword('');
    clearError();
  };

  return (
    <Modal
      isOpen
      onClose={() => {
        resetLoginForm();
        closeModal();
      }}
      closeOnOutsideClick={!email && !password && !['otc', 'setPassword'].includes(modalScreen)}
      title={TITLES[modalScreen]}
      modalType="auth"
    >
      {verificationMode ? (
        <OtpVerification
          onVerify={async code => {
            const [error, nextStep] = await verifyOtp(code);
            if (!error && !nextStep) {
              closeModal();
            }

            if (!error && nextStep === 'setPassword') {
              setModalScreen('setPassword');
            }
          }}
          onResend={() => {
            if (verificationMode === 'signup') {
              register(email, password);
            } else if (verificationMode === 'reset') {
              resetPassword(email);
            }
          }}
        />
      ) : (
        <form onSubmit={onSubmit} className="space-y-4">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-600">
              <M id={error} />
            </div>
          )}

          {modalScreen !== 'setPassword' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <M id="auth.email" />
              </label>
              <input
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                className={clsx(
                  'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent',
                  'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
                  error && 'border-red-300'
                )}
                placeholder={t('auth.emailPlaceholder')}
                required
                disabled={isSubmitting}
              />
            </div>
          )}

          {modalScreen !== 'reset' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <M id="auth.password" />
              </label>
              <input
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                className={clsx(
                  'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent',
                  'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
                  error && 'border-red-300'
                )}
                placeholder={t('auth.passwordPlaceholder')}
                required
                disabled={isSubmitting}
              />
            </div>
          )}

          <button
            type="submit"
            disabled={isSubmitting}
            className={clsx(
              'w-full px-4 py-2 text-white rounded-lg font-medium transition-colors',
              isSubmitting
                ? 'bg-indigo-400 cursor-not-allowed'
                : 'bg-indigo-600 hover:bg-indigo-500'
            )}
          >
            {isSubmitting ? (
              <span className="inline-flex items-center">
                <Spinner className="w-4 h-4 mr-2 -ml-1" />
                <M id="auth.submitting" />
              </span>
            ) : (
              <>
                {modalScreen === 'login' && <M id="auth.login" />}
                {modalScreen === 'register' && <M id="auth.createAccount" />}
                {modalScreen === 'reset' && <M id="auth.resetPassword" />}
                {modalScreen === 'setPassword' && <M id="auth.submitSetPassword" />}
              </>
            )}
          </button>

          {modalScreen === 'register' && (
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center pt-4">
              By continuing, you agree to MakeAgent&apos;s{' '}
              <a
                href="/terms"
                target="_blank"
                rel="noopener noreferrer"
                className="underline hover:text-indigo-600 dark:hover:text-indigo-400"
              >
                Terms of Service
              </a>{' '}
              and{' '}
              <a
                href="/privacy-policy"
                target="_blank"
                rel="noopener noreferrer"
                className="underline hover:text-indigo-600 dark:hover:text-indigo-400"
              >
                Privacy Policy
              </a>
              .
            </p>
          )}

          <div className="flex justify-between text-sm">
            {modalScreen !== 'reset' && (
              <button
                type="button"
                onClick={() => handleModeSwitch(modalScreen === 'login' ? 'register' : 'login')}
                className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500"
                disabled={isSubmitting}
              >
                {modalScreen === 'login' ? <M id="auth.createAccount" /> : <M id="auth.login" />}
              </button>
            )}

            {modalScreen === 'login' && (
              <button
                type="button"
                onClick={() => handleModeSwitch('reset')}
                className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500"
                disabled={isSubmitting}
              >
                <M id="auth.forgotPassword" />
              </button>
            )}

            {modalScreen === 'reset' && (
              <button
                type="button"
                onClick={() => handleModeSwitch('login')}
                className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500"
                disabled={isSubmitting}
              >
                <M id="auth.backToLogin" />
              </button>
            )}
          </div>
        </form>
      )}
    </Modal>
  );
}

type UseOnSubmitInterface = {
  setModalScreen: (modalScreen: AuthModals) => void;
  closeModal: () => void;
  email: string;
  password: string;
  modalScreen: AuthModals;
};

/**
 * Custom hook to handle form submission logic for authentication forms.
 * Manages submission state and performs different actions based on the current modal screen:
 * - login: Attempts to log in the user
 * - register: Registers a new user
 * - reset: Initiates password reset flow
 * - setPassword: Sets a new password
 */
function useSubmission({
  setModalScreen,
  closeModal,
  email,
  password,
  modalScreen,
}: UseOnSubmitInterface) {
  const { login, register, resetPassword, setNewPassword } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  return {
    isSubmitting,
    onSubmit: async (e: React.FormEvent) => {
      e.preventDefault();
      setIsSubmitting(true);
      try {
        switch (modalScreen) {
          case 'login': {
            const result = await login(email, password);
            if (!result?.verificationPending) {
              closeModal();
            } else {
              setModalScreen('otc');
            }
            break;
          }
          case 'register':
            await register(email, password);
            break;
          case 'reset':
            await resetPassword(email);
            setModalScreen('otc');
            break;
          case 'setPassword': {
            await setNewPassword(password, true);
            closeModal();
            break;
          }
          case 'otc':
            // handled by onVerify in OtpVerification
            break;
        }
      } finally {
        setIsSubmitting(false);
      }
    },
  };
}

function AuthModalProvider() {
  const { isOpen } = useModal();

  return (isOpen('login') || isOpen('register')) && <AuthModal />;
}

export { AuthModalProvider as AuthModal };
