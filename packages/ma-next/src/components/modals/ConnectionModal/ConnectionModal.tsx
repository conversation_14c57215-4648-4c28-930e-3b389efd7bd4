import { Modal } from '../Modal';
import { Connection } from 'components/setup/Connection';

interface ConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  provider: string;
  onConnected: () => void;
  onDisconnected: () => void;
  requiredSyncs?: Record<string, any>;
}

function ConnectionModal({
  isOpen,
  onClose,
  onConnected,
  onDisconnected,
  provider,
  requiredSyncs,
}: ConnectionModalProps) {
  return (
    <Modal isOpen={isOpen} onClose={onClose} hideHeader mediumSize>
      <Connection
        provider={provider}
        onConnected={onConnected}
        onDisconnected={onDisconnected}
        requiredSyncs={requiredSyncs}
      />
    </Modal>
  );
}

export { ConnectionModal };
