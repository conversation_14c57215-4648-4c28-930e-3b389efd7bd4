import React, { useRef, useEffect } from 'react';
import { X } from 'lucide-react';
import clsx from 'clsx';
import { useModal } from 'hooks/useModal';

interface ModalProps {
  isOpen: boolean;
  // @deprecated
  onClose?: () => void;
  onClosed?: () => void;
  title?: string | React.ReactNode;
  onSubmit?: () => void;
  submitText?: string;
  children?: React.ReactNode;
  fullScreen?: boolean;
  mediumSize?: boolean;
  hideHeader?: boolean;
  closeOnOutsideClick?: boolean;
  modalType?: 'default' | 'auth' | 'example';
}

function Modal({
  isOpen,
  onClosed,
  title,
  children,
  fullScreen = false,
  mediumSize = false,
  hideHeader = false,
  closeOnOutsideClick = true,
  modalType = 'default',
}: ModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const { closeModal } = useModal();
  useCloseOnOutsideClicks({ isOpen, modalRef, closeOnOutsideClick });

  useEffect(() => {
    if (!isOpen) {
      onClosed?.();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // Special case for auth and example modals
  if (modalType === 'auth' || modalType === 'example') {
    return (
      <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
        <div
          ref={modalRef}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-4xl mx-4 absolute top-1/3"
        >
          <button
            onClick={closeModal}
            className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="w-5 h-5" />
          </button>
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">{title}</h2>
          {children}
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 sm:p-6">
      <div
        ref={modalRef}
        className={clsx('bg-white relative dark:bg-gray-800 w-full', {
          'h-full': fullScreen,
          'h-[98vh] sm:h-auto md:max-h-[85vh] rounded-xl w-full lg:w-2/3 lg:max-w-4xl': mediumSize,
          'rounded-xl p-4 max-w-4xl': !fullScreen && !mediumSize,
        })}
      >
        {!hideHeader && title ? (
          <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{title}</h2>
            <button
              onClick={closeModal}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        ) : (
          <div className="block sm:hidden absolute bottom-5 right-5">
            <button
              onClick={closeModal}
              className="text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Close
            </button>
          </div>
        )}
        <div
          className={clsx('overflow-auto', {
            'h-[calc(100%-2rem)] p-6': (fullScreen || mediumSize) && !hideHeader,
            'h-[100vh] md:h-full p-6': (fullScreen || mediumSize) && hideHeader,
            'max-h-[calc(90vh-8rem)] sm:max-h-[85vh-8rem)]': mediumSize,
          })}
        >
          {children}
        </div>
      </div>
    </div>
  );
}

function useCloseOnOutsideClicks({
  isOpen,
  modalRef,
  closeOnOutsideClick,
}: {
  isOpen: boolean;
  modalRef: React.RefObject<HTMLDivElement>;
  closeOnOutsideClick: boolean;
}) {
  const { closeModal } = useModal();

  useEffect(() => {
    if (!closeOnOutsideClick) return;

    function handleClickOutside(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        closeModal();
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, closeOnOutsideClick, modalRef]);
}

export { Modal };
