import { useState } from 'react';
import { Modal } from '../Modal';
import { Connection } from 'components/setup/Connection';
import { PROVIDER_CONFIGS } from 'src/config/integrationUI';
import { useConnections } from 'hooks/useConnections';
import { useModal } from 'hooks/useModal';
import { Search } from 'lucide-react';

interface ConnectionsListModalProps {
  isOpen: boolean;
}

function ConnectionsListModal({ isOpen }: ConnectionsListModalProps) {
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { connections } = useConnections();
  const { openModal, closeModal } = useModal();

  const handleManageAgents = () => {
    closeModal();
    openModal('agentBrowse');
  };

  const handleShowExamples = () => {
    closeModal();
    openModal('agentBrowse');
  };

  if (selectedProvider) {
    return (
      <Modal
        isOpen={isOpen}
        onClosed={() => {
          setSelectedProvider(null);
          setSearchTerm('');
        }}
        mediumSize
        hideHeader
      >
        <Connection
          provider={selectedProvider}
          showBackButton
          onBack={() => setSelectedProvider(null)}
        />
      </Modal>
    );
  }

  const connectedProviders = Object.entries(PROVIDER_CONFIGS).filter(([key]) =>
    connections.some(conn => conn.providerKey === key)
  );

  const unconnectedProviders = Object.entries(PROVIDER_CONFIGS)
    .filter(([key]) => !connections.some(conn => conn.providerKey === key))
    .filter(
      ([, config]) =>
        config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        config.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

  return (
    <Modal isOpen={isOpen} title="Connections" mediumSize={!!selectedProvider}>
      <div className="space-y-8 px-2 sm:px-4 py-2 max-h-[calc(100vh-8rem)] overflow-y-auto">
        {/* Search Bar - Sticky on Mobile */}
        <div className="sticky top-0 bg-white dark:bg-gray-800 pt-4 pb-2 z-10">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="w-full pl-9 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        {/* Connected Services */}
        <div className="pt-2 pb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Connected Services
          </h3>

          {connectedProviders.length === 0 ? (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              No connected services yet. Connect a service to get started.
            </p>
          ) : (
            <div className="space-y-3">
              {connectedProviders.map(([key, config]) => {
                const Icon = config.icon;
                return (
                  <div
                    key={key}
                    className="flex flex-col sm:flex-row sm:items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-center gap-4 flex-1 min-w-0 mb-4 sm:mb-0">
                      <Icon className="w-10 h-10 flex-shrink-0" />
                      <div className="min-w-0">
                        <h3 className="font-medium text-gray-900 dark:text-white">{config.name}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 truncate pr-4">
                          {config.descriptionShort}
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
                      {/* Agents button commented out as requested
                      <button
                        onClick={handleManageAgents}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 text-center"
                      >
                        Agents
                      </button>
                      */}
                      <button
                        onClick={() => setSelectedProvider(key)}
                        className="px-4 py-2 text-sm font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 text-center"
                      >
                        Manage
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Available Services */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Create agents with...
          </h3>

          <div className="space-y-3">
            {unconnectedProviders.map(([key, config]) => {
              const Icon = config.icon;
              return (
                <div
                  key={key}
                  className="flex flex-col sm:flex-row sm:items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
                >
                  <div className="flex items-center gap-4 flex-1 min-w-0 mb-4 sm:mb-0">
                    <Icon className="w-10 h-10 flex-shrink-0" />
                    <div className="min-w-0">
                      <h3 className="font-medium text-gray-900 dark:text-white">{config.name}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate pr-4">
                        {config.descriptionShort}
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
                    {/* Examples button commented out as requested
                    <button
                      onClick={handleShowExamples}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 text-center"
                    >
                      Examples
                    </button>
                    */}
                    {config.available ? (
                      <button
                        onClick={() => setSelectedProvider(key)}
                        className="px-4 py-2.5 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-500 rounded-lg shadow-sm dark:bg-indigo-500 dark:hover:bg-indigo-400 text-center flex-1 sm:flex-initial"
                      >
                        Connect
                      </button>
                    ) : (
                      <span className="px-4 py-2 text-sm font-medium text-gray-400 dark:text-gray-500 text-center">
                        Coming soon
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </Modal>
  );
}

export { ConnectionsListModal };
