import { useAuth } from 'hooks/useAuth';
import { useModal } from 'hooks/useModal';
import { NavbarLogo } from './NavbarLogo';
import { AuthButtons } from './AuthButtons';
import { PenSquare, LayoutGrid } from 'lucide-react';
import { UserMenu } from './UserMenu';
import { NavbarActionButton } from './NavbarActionButton';
import { useChatContext } from 'providers/ChatContext';
import { useRouter } from 'next/navigation';
import { ROUTES } from 'src/config';
import { ManageAgentsModal } from 'components/modals';

function Navbar() {
  const { isAuthenticated, isAnonymous } = useAuth();
  const { openModal } = useModal();
  const { messages } = useChatContext();
  const router = useRouter();

  const handleLogin = () => openModal('login');
  const handleRegister = () => openModal('register');

  return (
    <>
      <ManageAgentsModal />

      <nav className="fixed top-0 left-0 right-0 px-3 sm:px-6 py-4 flex justify-between items-center bg-slate-50 dark:bg-gray-900 z-10">
        <NavbarLogo />

        <div className="flex items-center gap-2 sm:gap-3">
          {Boolean(messages?.length) && (
            <NavbarActionButton
              icon={<PenSquare className="w-5 h-5" />}
              messageId="navbar.newAgent"
              onClick={() => {
                router.push(ROUTES.home);
              }}
            />
          )}
          {(isAuthenticated || isAnonymous) && (
            <NavbarActionButton
              icon={<LayoutGrid className="w-5 h-5" />}
              messageId="navbar.manageAgents"
              onClick={() => openModal('manageAgents')}
            />
          )}
          {isAuthenticated && <UserMenu onManageAgents={() => openModal('manageAgents')} />}
          {!isAuthenticated && <AuthButtons onLogin={handleLogin} onRegister={handleRegister} />}
        </div>
      </nav>
    </>
  );
}

export { Navbar };
