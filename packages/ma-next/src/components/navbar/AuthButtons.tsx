import { FormattedMessage } from 'react-intl';

interface AuthButtonsProps {
  onLogin: () => void;
  onRegister: () => void;
}

function AuthButtons({ onLogin, onRegister }: AuthButtonsProps) {
  return (
    <div className="flex gap-2 sm:gap-3">
      <button
        onClick={onLogin}
        className="px-3 sm:px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap
          bg-indigo-600 hover:bg-indigo-500 text-white
          dark:bg-gray-100 dark:hover:bg-white dark:text-gray-900"
      >
        <FormattedMessage id="auth.login" />
      </button>
      <button
        onClick={onRegister}
        className="px-3 sm:px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap
          bg-white hover:bg-gray-50 text-gray-900 border border-gray-200
          dark:bg-transparent dark:hover:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
      >
        <FormattedMessage id="auth.signup" />
      </button>
    </div>
  );
}

export { AuthButtons };
