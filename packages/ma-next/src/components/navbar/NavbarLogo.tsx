import { useRouter } from 'next/navigation';
import { FormattedMessage } from 'react-intl';
import { ROUTES } from 'src/config';

function NavbarLogo() {
  const router = useRouter();
  return (
    <button
      onClick={() => router.push(ROUTES.home)}
      className="text-xl font-semibold text-gray-900 dark:text-white"
    >
      <FormattedMessage id="app.title" />
    </button>
  );
}

export { NavbarLogo };
