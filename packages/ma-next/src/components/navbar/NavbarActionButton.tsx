import React from 'react';
import { M } from 'src/intl';

interface NavbarActionButtonProps {
  icon: React.ReactNode;
  messageId: string;
  onClick: () => void;
  active?: boolean;
}

function NavbarActionButton({ icon, messageId, onClick, active = false }: NavbarActionButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`p-2 rounded-lg ${
        active ? 'bg-gray-200 dark:bg-gray-700' : 'hover:bg-gray-100 dark:hover:bg-gray-800'
      } group relative`}
    >
      {icon}
      <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 whitespace-nowrap z-20">
        <M id={messageId} />
      </span>
    </button>
  );
}

export { NavbarActionButton };
