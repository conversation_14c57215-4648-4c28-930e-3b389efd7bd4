import { M, useIntl } from 'intl';
import { useChatContext } from 'providers/ChatContext';

// Agent mode suggestions
const AGENT_SUGGESTION_INTL_IDS = [
  'prompt.suggestions.emailDrafts',
  'prompt.suggestions.seoWriter',
  'prompt.suggestions.weather',
  'prompt.suggestions.monitor',
  'prompt.suggestions.calendar',
];

// Task mode suggestions
const TASK_SUGGESTION_INTL_IDS = [
  'prompt.tasks.gmailDraft',
  'prompt.tasks.slackMessage',
  'prompt.tasks.calendarEvents',
  'prompt.tasks.notionPage',
  'prompt.tasks.githubFile',
  'prompt.tasks.searchDropbox',
  'prompt.tasks.uploadFile',
  'prompt.tasks.weatherReport',
];

type PromptSuggestionsProps = {
  setPromptText: (text: string) => void;
  isSubmitting: boolean;
  handleInputChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
};

function PromptSuggestions({
  setPromptText,
  isSubmitting,
  handleInputChange,
}: PromptSuggestionsProps) {
  const { t } = useIntl();
  const { mode } = useChatContext();

  const handleSuggestionClick = (suggestionText: string) => {
    // Call both functions to ensure compatibility
    setPromptText(suggestionText);

    // If handleInputChange is provided, create a synthetic event and call it
    if (handleInputChange) {
      const syntheticEvent = {
        target: { value: suggestionText },
        preventDefault: () => {},
      } as React.ChangeEvent<HTMLTextAreaElement>;

      handleInputChange(syntheticEvent);
    }
  };

  return (
    <div className="mt-8 w-full">
      <div className="flex flex-wrap gap-2 max-h-20">
        {(mode === 'agent' ? AGENT_SUGGESTION_INTL_IDS : TASK_SUGGESTION_INTL_IDS).map(
          (key, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(t(key))}
              disabled={isSubmitting}
              className="px-4 py-2 rounded-full text-sm
      bg-white hover:bg-gray-50 text-gray-600 hover:text-indigo-600 border border-gray-200
      dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:text-white
      dark:hover:border-indigo-500 dark:hover:bg-gray-700
      disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <M id={key} />
            </button>
          )
        )}
      </div>
    </div>
  );
}

export { PromptSuggestions };
