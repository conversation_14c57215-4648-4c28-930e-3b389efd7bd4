import { useEffect, useRef } from 'react';
import clsx from 'clsx';
import { usePrompt } from 'hooks/usePrompt';
import { PromptSuggestions } from './PromptSuggestions';
import { useIntl } from 'src/intl';
import { UseChatHelpers } from '@ai-sdk/react';
import { useChatContext } from 'providers/ChatContext';
import { useRouter } from 'next/navigation';
import { ModeSwitchButtons } from './ModeSwitchButtons';
import { SubmitCancelButton } from './SubmitCancelButton';

type PromptInputProps = Pick<UseChatHelpers, 'input' | 'handleInputChange' | 'status' | 'stop'> & {
  handleSubmit: (e: React.FormEvent) => Promise<void> | void;
  isNewChat?: boolean;
};

function PromptInput({
  input,
  handleInputChange,
  handleSubmit,
  status,
  isNewChat,
  stop,
}: PromptInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { setText: setPromptText } = usePrompt();
  const { t } = useIntl();
  const chat = useChatContext();
  const router = useRouter();

  useTextareaAutoResize(textareaRef, input, isNewChat);
  useFocusWhenFinishedStreaming(textareaRef, status);

  return (
    <div className="relative w-full">
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={input}
          onChange={handleInputChange}
          onKeyDown={(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              if (input.trim()) {
                handleSubmit(e);
              }
            }
          }}
          placeholder={chat.mode === 'task' ? t('prompt.taskPlaceholder') : t('prompt.placeholder')}
          className={clsx(
            'w-full p-6 pb-12 rounded-xl auto-scrollbar scrollbar-themed focus:outline-none focus:ring-2 resize-none overflow-auto bg-white focus:ring-indigo-500 focus:border-transparent text-gray-800 dark:bg-gray-800 dark:focus:ring-indigo-500 dark:text-white dark:placeholder-gray-400',
            {
              'min-h-[120px]': !isNewChat,
              'min-h-[200px]': isNewChat,
              overflow: true,
            }
          )}
          style={{
            boxShadow: `0 0 30px ${input ? 'rgba(99, 102, 241, 0.15)' : 'rgba(0, 0, 0, 0.05)'}`,
          }}
          disabled={status === 'submitted'}
        />
        <ModeSwitchButtons />
        <SubmitCancelButton input={input} handleSubmit={handleSubmit} status={status} stop={stop} />
      </div>

      {isNewChat && (
        <PromptSuggestions
          setPromptText={setPromptText}
          isSubmitting={status !== 'ready'}
          handleInputChange={handleInputChange}
        />
      )}
    </div>
  );
}

// Hook to handle textarea auto-resize
const useTextareaAutoResize = (
  textareaRef: React.RefObject<HTMLTextAreaElement | null>,
  text: string,
  isNewChat?: boolean
) => {
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = `${Math.min(isNewChat ? 600 : 300, Math.max(120, textarea.scrollHeight))}px`;
    }
  }, [text, isNewChat, textareaRef]);
};

/**
 * Custom hook to focus a textarea element when the chat streaming finishes.
 *
 * @param textareaRef - A ref object pointing to the textarea element.
 * @param status - The current status of the chat (e.g., 'streaming', 'finished').
 */
const useFocusWhenFinishedStreaming = (
  textareaRef: React.RefObject<HTMLTextAreaElement | null>,
  status: UseChatHelpers['status']
) => {
  useEffect(() => {
    if (status !== 'submitted' && status !== 'streaming') {
      textareaRef.current?.focus();
    }
  }, [status, textareaRef]);
};

export { PromptInput };
