import { ArrowUp } from 'lucide-react';
import clsx from 'clsx';
import { UseChatHelpers } from '@ai-sdk/react';

type SubmitCancelButtonProps = Pick<UseChatHelpers, 'input' | 'status' | 'stop'> & {
  handleSubmit: (e: React.FormEvent) => Promise<void> | void;
};

function SubmitCancelButton({ input, handleSubmit, status, stop }: SubmitCancelButtonProps) {
  return (
    <>
      {status === 'streaming' || status === 'submitted' ? (
        <button
          onClick={stop}
          className="absolute bottom-4 right-4 p-[10px] rounded-lg bg-gray-100 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500 text-red-500 transition-colors duration-200"
          aria-label="Stop generating"
        >
          <div className="w-4 h-4 bg-red-600 hover:bg-red-500 rounded opacity-60" />
        </button>
      ) : (
        <button
          onClick={handleSubmit}
          disabled={!input.trim()}
          className={clsx('absolute bottom-4 right-4 p-2 rounded-lg', {
            'bg-indigo-600 hover:bg-indigo-500 text-white dark:bg-indigo-500 dark:hover:bg-indigo-400':
              input.trim(),
            'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500':
              !input.trim(),
          })}
        >
          <ArrowUp className="w-5 h-5" />
        </button>
      )}
    </>
  );
}

export { SubmitCancelButton };
