import { TaskflowExecution } from 'components/mode-task/taskflow/TaskflowExecution';
import { ExecutionPart } from 'chat/protocol/parts';

type ExecutionResponsePartProps = {
  part: ExecutionPart;
};

const ExecutionResponsePart = ({ part }: ExecutionResponsePartProps) => {
  const executionId = part.content.executionId;

  if (!executionId) {
    return null;
  }

  return (
    <div className="my-4">
      <TaskflowExecution executionId={executionId} />
    </div>
  );
};

export { ExecutionResponsePart };
