import { ErrorPart } from 'chat/protocol/parts';

type ErrorResponsePartProps = {
  part: ErrorPart;
};

const ErrorResponsePart = (_: ErrorResponsePartProps) => {
  return (
    <div className="p-3 my-4 border border-red-100 bg-red-50 rounded-md dark:bg-gray-800 dark:border-gray-700">
      <div className="flex items-center gap-2 text-red-700 dark:text-red-400">
        <p className="text-sm">
          An unexpected error occured. Please start a new chat, and try again.
        </p>
      </div>
    </div>
  );
};

export { ErrorResponsePart };
