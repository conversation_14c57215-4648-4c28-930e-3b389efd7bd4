import { TaskflowDisplay } from 'components/mode-task/taskflow/TaskflowDisplay';
import { TaskflowPart } from 'chat/protocol/parts';

type TaskflowResponsePartProps = {
  part: TaskflowPart;
};

const TaskflowResponsePart = ({ part }: TaskflowResponsePartProps) => {
  const taskflowId = part.content.id;

  if (!taskflowId) {
    return null;
  }

  return (
    <div className="my-4">
      <TaskflowDisplay taskflowId={taskflowId} />
    </div>
  );
};

export { TaskflowResponsePart };
