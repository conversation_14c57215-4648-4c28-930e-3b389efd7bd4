import { forwardRef, memo } from 'react';
import { Message } from 'chat/protocol';
import { preprocessMessageParts } from 'chat/utils/preprocessMessageParts';
import { TextResponsePart } from './TextResponsePart';
import { TaskflowResponsePart } from './TaskflowResponsePart';
import { ExecutionResponsePart } from './ExecutionResponsePart';
import { ToolInvocationResponsePart } from './ToolInvocationResponsePart';
import { ErrorResponsePart } from './ErrorResponsePart';
import { CreatingAgentResponsePart } from './CreatingAgentResponsePart';

type ResponseMessageProps = {
  message: Message;
  isLatestMessage: boolean;
};

const ResponseMessage = forwardRef<HTMLDivElement, ResponseMessageProps>(
  ({ message, isLatestMessage }, ref) => {
    const processedParts = preprocessMessageParts(message.parts);

    const partsToRender = processedParts.map((part, index) => {
      switch (part.type) {
        case 'text':
          return <TextResponsePart key={index} part={part} />;
        case 'taskflow':
          return <TaskflowResponsePart key={index} part={part} />;
        case 'execution':
          return <ExecutionResponsePart key={index} part={part} />;
        case 'tool_invocation':
          return (
            <ToolInvocationResponsePart key={index} part={part} isLatestMessage={isLatestMessage} />
          );
        case 'error':
          return <ErrorResponsePart key={index} part={part} />;
        case 'creating_agent':
          return processedParts[index + 1] ? null : (
            <CreatingAgentResponsePart key={index} part={part} />
          );
        default:
          return null;
      }
    });

    return (
      <>
        <div ref={ref} className="self-start w-full max-w-full">
          {partsToRender.filter(Boolean).length ? partsToRender : <WorkingSpinner />}
        </div>
      </>
    );
  }
);

const WorkingSpinner = () => (
  <div className="p-1.5 my-1 border border-gray-200 bg-gray-50 rounded-md dark:bg-gray-800 dark:border-gray-700 inline-flex items-center">
    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-indigo-600 dark:border-indigo-400 mr-1.5"></div>
  </div>
);

ResponseMessage.displayName = 'ResponseMessage';

const MemoizedResponseMessage = memo(ResponseMessage);

export { MemoizedResponseMessage as ResponseMessage };
