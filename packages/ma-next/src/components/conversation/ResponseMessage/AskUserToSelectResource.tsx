import { useState } from 'react';
import { CheckIcon } from 'lucide-react';
import { useChatContext } from 'providers/ChatContext';
import { ResourceOption } from 'chat/protocol/tools';
import { ToolInvocationPart } from 'chat/protocol/ui';
import { ToolResultContent } from 'chat/protocol';

interface AskUserToSelectResourceProps {
  part: ToolInvocationPart;
  isLatestMessage: boolean;
}

function AskUserToSelectResource({ part, isLatestMessage }: AskUserToSelectResourceProps) {
  // If there's a result or it's not the latest message, show the result view
  if (part.content.result) {
    return (
      <SelectionResult options={part.content.args.options} result={part.content.result as string} />
    );
  }

  // Only show the selector if this is the latest message and there's no result yet
  if (isLatestMessage && !part.content.result) {
    return (
      <ResourceSelector
        options={part.content.args.options}
        toolCallId={part.content.toolCallId}
        title={part.content.args.title}
      />
    );
  }

  return null;
}

// The selector component for user interaction
function ResourceSelector({
  options,
  toolCallId,
  title = 'Please select an option:',
}: {
  options: ResourceOption[];
  toolCallId: string;
  title?: string;
}) {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const chat = useChatContext();

  const handleSelect = (value: string) => {
    setSelectedOption(value);
  };

  const handleSubmit = () => {
    if (selectedOption) {
      // Create a tool result with the selected option value
      const toolResult: ToolResultContent = {
        toolCallId,
        toolName: 'askUserToSelectResource',
        result: selectedOption,
      };

      // Submit the tool result using the toolResults parameter
      chat.append({
        toolResults: [toolResult],
      });

      setIsSubmitted(true);
    }
  };

  if (isSubmitted) {
    const selectedLabel = options.find(opt => opt.value === selectedOption)?.label;
    return (
      <div className="p-4 my-3 border border-green-100 bg-green-50 rounded-md dark:bg-gray-800 dark:border-gray-700">
        <div className="flex items-center gap-2 text-green-700 dark:text-green-400">
          <CheckIcon className="w-5 h-5" />
          <p className="font-medium">Selected: {selectedLabel}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 my-3 border border-blue-100 bg-blue-50 rounded-md dark:bg-gray-800 dark:border-gray-700">
      <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">{title}</h3>

      <div className="space-y-2 mb-4">
        {options.map(option => (
          <div
            key={option.value}
            onClick={() => handleSelect(option.value)}
            className={`p-3 rounded-md cursor-pointer flex items-center ${
              selectedOption === option.value
                ? 'bg-blue-200 dark:bg-blue-900 border-blue-300 dark:border-blue-700'
                : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 border-gray-200 dark:border-gray-600'
            } border`}
          >
            <div
              className={`w-4 h-4 rounded-full mr-3 flex-shrink-0 ${
                selectedOption === option.value
                  ? 'bg-blue-500 dark:bg-blue-400'
                  : 'border-2 border-gray-300 dark:border-gray-500'
              }`}
            >
              {selectedOption === option.value && <CheckIcon className="w-4 h-4 text-white" />}
            </div>
            <span className="text-gray-800 dark:text-gray-200">{option.label}</span>
          </div>
        ))}
      </div>

      <button
        onClick={handleSubmit}
        disabled={!selectedOption}
        className={`px-4 py-2 rounded-md text-sm font-medium ${
          selectedOption
            ? 'bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-600'
            : 'bg-gray-200 text-gray-500 cursor-not-allowed dark:bg-gray-700 dark:text-gray-400'
        }`}
      >
        Submit
      </button>
    </div>
  );
}

// The result component for displaying the selected option
function SelectionResult({ options, result }: { options: ResourceOption[]; result: string }) {
  // Find the selected option by comparing with the result value
  const selectedOption = options.find(opt => opt.value === result);

  if (!selectedOption) {
    return null;
  }

  return (
    <div className="p-3 my-2 border border-green-100 bg-green-50 rounded-md dark:bg-gray-800 dark:border-gray-700">
      <div className="flex items-center gap-2 text-green-700 dark:text-green-400">
        <p className="text-sm">Selected: {selectedOption.label}</p>
      </div>
    </div>
  );
}

export { AskUserToSelectResource };
