import { ToolInvocationPart } from 'chat/protocol/ui';
import { ActionCall } from 'components/mode-task/ActionCall';
import { ActionCallGroup } from 'components/mode-task/ActionCallGroup';
import { AskUserToSelectResource } from './AskUserToSelectResource';

type ToolInvocationResponsePartProps = {
  part: ToolInvocationPart;
  isLatestMessage: boolean;
};

const ToolInvocationResponsePart = ({ part, isLatestMessage }: ToolInvocationResponsePartProps) => {
  const { toolName } = part.content;

  switch (toolName) {
    case 'actionCall': {
      return <ActionCall part={part as any} isLatestMessage={isLatestMessage} />;
    }
    case 'actionCallGroup': {
      return <ActionCallGroup part={part as any} isLatestMessage={isLatestMessage} />;
    }
    case 'askUserToSelectResource': {
      return <AskUserToSelectResource part={part} isLatestMessage={isLatestMessage} />;
    }
    default: {
      return null;
    }
  }
};

export { ToolInvocationResponsePart };
