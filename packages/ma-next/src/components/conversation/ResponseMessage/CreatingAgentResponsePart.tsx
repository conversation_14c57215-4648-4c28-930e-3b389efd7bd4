import { Loader2 } from 'lucide-react';
import { CreatingAgentPart } from 'chat/protocol/parts';
import { useChatContext } from 'providers/ChatContext';

type CreatingAgentResponsePartProps = {
  part: CreatingAgentPart;
};

const CreatingAgentResponsePart = ({ part }: CreatingAgentResponsePartProps) => {
  const { mode } = useChatContext();

  return (
    <div className="flex items-center p-3 my-2 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-100 dark:border-indigo-800 rounded-md">
      <Loader2 className="w-4 h-4 text-indigo-600 dark:text-indigo-400 animate-spin mr-2" />
      <span className="text-indigo-700 dark:text-indigo-300 font-medium">
        Creating {mode === 'agent' ? 'agent' : 'task'}...
      </span>
    </div>
  );
};

export { CreatingAgentResponsePart };
