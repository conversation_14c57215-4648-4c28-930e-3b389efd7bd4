import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';

const PROCESSING_STEPS = [
  'Thinking...',
  'Designing your agent',
  'Identifying requirements...',
  'Making agent...',
  'Nearly there...',
];

interface ProcessingPanelProps {
  isVisible: boolean;
}

function ProcessingPanel({ isVisible }: ProcessingPanelProps) {
  const [seconds, setSeconds] = useState(0);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  useEffect(() => {
    if (!isVisible) {
      setSeconds(0);
      setCurrentStepIndex(0);
      return;
    }

    // Timer for seconds
    const timer = setInterval(() => {
      setSeconds(prev => prev + 1);
    }, 1000);

    // Timer for changing steps
    const stepTimer = setTimeout(() => {
      const nextIndex = Math.min(currentStepIndex + 1, PROCESSING_STEPS.length - 1);
      setCurrentStepIndex(nextIndex);
    }, 1000);

    return () => {
      clearInterval(timer);
      clearTimeout(stepTimer);
    };
  }, [isVisible, seconds, currentStepIndex]);

  // Progress to the last step after 5 seconds
  useEffect(() => {
    if (!isVisible) return;

    if (seconds >= 5 && currentStepIndex < PROCESSING_STEPS.length - 1) {
      setCurrentStepIndex(PROCESSING_STEPS.length - 1);
    }
  }, [seconds, currentStepIndex, isVisible]);

  if (!isVisible) return null;

  return (
    <div className="max-w-3xl mx-auto my-8">
      <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm p-6 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
        <div className="flex items-center">
          <div className="w-6 h-6 flex items-center justify-center mr-4">
            <Loader2 className="w-5 h-5 text-indigo-500/80 dark:text-indigo-400/80 animate-spin" />
          </div>
          <div className="flex-1">
            <div className="flex justify-between items-center mb-2">
              <p className="font-medium text-gray-700 dark:text-gray-300">
                {PROCESSING_STEPS[currentStepIndex]}
              </p>
              <span className="text-sm text-gray-500/80 dark:text-gray-400/80">{seconds}s</span>
            </div>
            <div className="w-full bg-gray-100 dark:bg-gray-700/50 rounded-full h-1">
              <div
                className="bg-indigo-400/70 dark:bg-indigo-500/70 h-1 rounded-full transition-all duration-300"
                style={{
                  width: `${Math.min(((currentStepIndex + 1) / PROCESSING_STEPS.length) * 100, 100)}%`,
                  boxShadow: '0 0 8px rgba(99, 102, 241, 0.4)',
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { ProcessingPanel };
