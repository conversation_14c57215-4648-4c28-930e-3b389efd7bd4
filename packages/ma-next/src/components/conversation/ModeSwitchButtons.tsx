import { useChatContext } from 'providers/ChatContext';
import { useRouter } from 'next/navigation';
import { ROUTES } from 'src/config';
import { Tooltip } from 'components/Tooltip';
import clsx from 'clsx';
import { useIntl } from 'src/intl';

function ModeSwitchButtons() {
  const chat = useChatContext();
  const router = useRouter();
  const { t } = useIntl();

  const handleModeSwitch = (mode: 'agent' | 'task') => {
    // If we have messages and we're not already in the target mode, start a new chat
    if (chat.messages.length > 0 && chat.mode !== mode) {
      // Generate a new ID and navigate to home (which will create a new chat)
      router.push(ROUTES.home);
      // Set the mode after navigation
      chat.setMode(mode);
    } else {
      // Just set the mode if we're in a new chat or already in the target mode
      chat.setMode(mode);
    }
  };

  return (
    <div className="absolute bottom-4 left-3 flex">
      <Tooltip label="Make an AI agent that runs continuously">
        <button
          onClick={() => handleModeSwitch('agent')}
          className={clsx(
            'py-1 text-xs rounded-l-full border border-r-0 w-[57px]',
            chat.mode === 'agent'
              ? 'bg-indigo-50 text-indigo-600 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-700'
              : 'bg-white/50 text-gray-500 border-gray-200 hover:bg-gray-50 dark:bg-gray-800/50 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700/50'
          )}
        >
          agent
        </button>
      </Tooltip>
      <Tooltip label="Perform a one-time task">
        <button
          onClick={() => handleModeSwitch('task')}
          className={clsx(
            'py-1 text-xs rounded-r-full border w-[57px]',
            chat.mode === 'task'
              ? 'bg-indigo-50 text-indigo-600 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-700'
              : 'bg-white/50 text-gray-500 border-gray-200 hover:bg-gray-50 dark:bg-gray-800/50 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700/50'
          )}
        >
          task
        </button>
      </Tooltip>
    </div>
  );
}

export { ModeSwitchButtons };
