import { ResponseMessage } from './ResponseMessage/ResponseMessage';
import { forwardRef, memo } from 'react';
import { Message } from 'chat/protocol';

interface MessagesProps {
  messages: Message[];
  userMessageRef: React.RefObject<HTMLDivElement | null>;
  latestMessageRef: React.RefObject<HTMLDivElement | null>;
}

const Messages = memo(({ messages, userMessageRef, latestMessageRef }: MessagesProps) => {
  return (
    <div className="max-w-3xl mx-auto pt-4">
      <div className="flex flex-col items-end space-y-8">
        {messages.map((message, index) => {
          const isAssistant = message.role === 'assistant';

          const isLastUserMessage = index === messages.length - 2 && !isAssistant;
          const isLatestMessage = index === messages.length - 1;

          return isAssistant ? (
            <ResponseMessage
              message={message}
              isLatestMessage={isLatestMessage}
              ref={isLatestMessage ? latestMessageRef : null}
              key={index}
            />
          ) : (
            <UserMessage
              key={index}
              ref={isLastUserMessage ? userMessageRef : null}
              message={message}
            />
          );
        })}
      </div>
    </div>
  );
});

Messages.displayName = 'Messages';

// User message component
const UserMessage = forwardRef<HTMLDivElement, { message: Message }>(({ message }, ref) => {
  return (
    <div ref={ref} className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
      {message.content}
    </div>
  );
});

UserMessage.displayName = 'UserMessage';

export { Messages };
