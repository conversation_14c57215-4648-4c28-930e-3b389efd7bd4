// import { useModal } from 'hooks/useModal';

function AlphaInfoPanel() {
  // const { openModal } = useModal();

  // const handleBrowseAgents = () => {
  //   openModal('agentBrowse');
  // };

  return (
    <div className="max-w-3xl mx-auto mb-6 mt-10 px-4 py-3 bg-pink-50 border border-pink-200 rounded-lg dark:bg-pink-900/20 dark:border-pink-800">
      <div className="flex flex-col items-start">
        <p className="text-sm text-pink-800 dark:text-pink-200 mb-3">
          MakeAgent is in public alpha (an early version with limited functionality).
        </p>
        {/* <button
          onClick={handleBrowseAgents}
          className="text-sm font-medium text-pink-700 bg-pink-100 hover:bg-pink-200 px-3 py-1 rounded-md transition-colors dark:bg-pink-800/30 dark:text-pink-200 dark:hover:bg-pink-800/50"
        >
          Browse agents
        </button> */}
      </div>
    </div>
  );
}

export { AlphaInfoPanel };
