import { SYNC_OUTPUTS_KEYED } from 'src/config/nangoConstants';
import { RichResultModelDisplay } from './RichResultModelDisplay';

interface RichSyncOutputsProps {
  providerKey: string;
  syncKey: string;
  output: any;
}

function RichSyncOutputs({ providerKey, syncKey, output }: RichSyncOutputsProps) {
  const modelName = SYNC_OUTPUTS_KEYED[`${providerKey}:${syncKey}` as keyof typeof SYNC_OUTPUTS_KEYED];
  if (!modelName || !RichResultModelDisplay.canDisplay(modelName)) return null;
  return <RichResultModelDisplay modelName={modelName} result={output} />;
}

RichSyncOutputs.canDisplay = (providerKey: string, syncKey: string) => {
  const modelName = SYNC_OUTPUTS_KEYED[`${providerKey}:${syncKey}` as keyof typeof SYNC_OUTPUTS_KEYED];
  return Boolean(modelName && RichResultModelDisplay.canDisplay(modelName));
};

export { RichSyncOutputs };

