import { ACTION_INPUTS_KEYED } from 'src/config/nangoConstants';
import { RichParameterModelDisplay } from './RichParameterModelDisplay';

interface RichParameterDisplayProps {
  providerKey: string;
  actionKey: string;
  actionParameters: Record<string, any>;
}

/**
 * Component that displays rich UI for specific types of parameters
 */
function RichParameterDisplay({
  providerKey,
  actionKey,
  actionParameters,
}: RichParameterDisplayProps) {
  const modelName =
    ACTION_INPUTS_KEYED[`${providerKey}:${actionKey}` as keyof typeof ACTION_INPUTS_KEYED];

  if (!modelName || !RichParameterModelDisplay.canDisplay(modelName)) return null;

  return <RichParameterModelDisplay modelName={modelName} parameters={actionParameters} />;
}

RichParameterDisplay.canDisplay = (providerKey: string, actionKey: string): boolean => {
  const modelName =
    ACTION_INPUTS_KEYED[`${providerKey}:${actionKey}` as keyof typeof ACTION_INPUTS_KEYED];
  return Boolean(modelName && RichParameterModelDisplay.canDisplay(modelName));
};

export { RichParameterDisplay };
