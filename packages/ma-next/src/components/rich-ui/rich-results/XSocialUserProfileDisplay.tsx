import React from 'react';
import { User, MapPin, Link, Lock, CheckCircle, Users } from 'lucide-react';
import { XIcon } from 'components/icons/lucide-like-social';
import { XSocialUserProfile } from 'src/config/nangoModels';

type XSocialUserProfileDisplayProps = {
  output: XSocialUserProfile;
};

/**
 * Renders a rich display of a Twitter/X user profile
 */
function XSocialUserProfileDisplay({ output }: XSocialUserProfileDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <XIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No Twitter profile data available</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <XIcon className="w-5 h-5 text-blue-500 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Twitter Profile</h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          {/* Profile Image */}
          <div className="flex-shrink-0 mr-4">
            {output.profile_image_url ? (
              <img
                src={output.profile_image_url}
                alt={`${output.name}'s profile`}
                className="w-16 h-16 rounded-full"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <User className="w-8 h-8 text-blue-500 dark:text-blue-400" />
              </div>
            )}
          </div>

          {/* Profile Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <h4 className="text-lg font-bold text-gray-900 dark:text-white">{output.name}</h4>
              {output.verified && (
                <CheckCircle className="w-4 h-4 ml-1 text-blue-500 dark:text-blue-400" />
              )}
              {output.protected && (
                <Lock className="w-4 h-4 ml-1 text-gray-500 dark:text-gray-400" />
              )}
            </div>

            <p className="text-sm text-gray-500 dark:text-gray-400">@{output.username}</p>

            {output.description && (
              <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">{output.description}</p>
            )}

            <div className="mt-3 flex flex-wrap gap-3">
              {output.location && (
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                  <MapPin className="w-3.5 h-3.5 mr-1" />
                  {output.location}
                </div>
              )}

              {output.url && (
                <div className="flex items-center text-xs text-blue-500 dark:text-blue-400">
                  <Link className="w-3.5 h-3.5 mr-1" />
                  <a
                    href={output.url.startsWith('http') ? output.url : `https://${output.url}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:underline"
                  >
                    {output.url.replace(/^https?:\/\//, '')}
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-5 grid grid-cols-3 gap-4 border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {formatNumber(output.followers_count)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Followers</div>
          </div>

          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {formatNumber(output.following_count)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Following</div>
          </div>

          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {formatNumber(output.tweet_count)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Tweets</div>
          </div>
        </div>

        {/* View on Twitter link */}
        <div className="mt-5 pt-4 border-t border-gray-200 dark:border-gray-700">
          <a
            href={`https://twitter.com/${output.username}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-blue-500 dark:text-blue-400 hover:underline"
          >
            <XIcon className="w-4 h-4 mr-1" />
            View on Twitter
          </a>
        </div>
      </div>
    </div>
  );
}

// Helper function to format numbers (e.g., 1500 -> 1.5K)
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

export { XSocialUserProfileDisplay };
