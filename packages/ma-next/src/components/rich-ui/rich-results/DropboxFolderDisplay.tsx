import React from 'react';
import { Fold<PERSON>, <PERSON> } from 'lucide-react';
import { DropboxFolder } from 'src/config/nangoModels';

interface DropboxFolderDisplayProps {
  output: DropboxFolder;
}

/**
 * Renders a rich display of a Dropbox folder
 */
function DropboxFolderDisplay({ output }: DropboxFolderDisplayProps) {
  const folder = output;

  // Check if we have valid data
  if (!folder) {
    return (
      <div className="p-6 text-center">
        <Folder className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No folder data available</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Dropbox Folder</h3>
        </div>
      </div>

      <div className="p-5 space-y-4">
        {/* Folder name */}
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
            <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h4 className="text-base font-medium text-gray-900 dark:text-white">{folder.name}</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
              Folder ID: {folder.id}
            </p>
          </div>
        </div>

        {/* Path */}
        {folder.path_display && (
          <div className="mt-4">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Path:</div>
            <div className="text-sm text-gray-800 dark:text-gray-200 break-all">
              {folder.path_display}
            </div>
          </div>
        )}

        {/* Link to Dropbox */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <a
            href={`https://www.dropbox.com/home${folder.path_display}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            <Link className="w-4 h-4 mr-1" />
            View in Dropbox
          </a>
        </div>
      </div>
    </div>
  );
}

export { DropboxFolderDisplay };
