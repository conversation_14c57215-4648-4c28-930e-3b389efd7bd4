import React from 'react';
import { User, Mail, MapPin, Bot, Shield, Crown } from 'lucide-react';
import { SlackUserInfo } from 'src/config/nangoModels';

type SlackUserInfoDisplayProps = {
  output: SlackUserInfo;
};

/**
 * Renders a rich display of Slack user information
 */
function SlackUserInfoDisplay({ output }: SlackUserInfoDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <User className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No user data available</p>
      </div>
    );
  }

  const { id, name, is_bot, is_admin, is_owner, tz, profile } = output;
  const displayName = profile?.display_name || profile?.real_name || name;
  const realName =
    profile?.real_name && profile.real_name !== displayName ? profile.real_name : null;
  const email = profile?.email;
  const profileImage = profile?.image_512 || profile?.image_original;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <User className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Slack User Profile</h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          {/* Profile Image */}
          <div className="flex-shrink-0 mr-4">
            {profileImage ? (
              <img
                src={profileImage}
                alt={`${displayName}'s profile`}
                className="w-16 h-16 rounded-full"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                <User className="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
            )}
          </div>

          {/* Profile Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">{displayName}</h4>
              {is_bot && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                  <Bot className="w-3 h-3 mr-1" />
                  Bot
                </span>
              )}
              {is_admin && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                  <Shield className="w-3 h-3 mr-1" />
                  Admin
                </span>
              )}
              {is_owner && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                  <Crown className="w-3 h-3 mr-1" />
                  Owner
                </span>
              )}
            </div>

            <p className="text-sm text-gray-500 dark:text-gray-400">@{name}</p>

            {realName && (
              <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">{realName}</p>
            )}

            <div className="mt-3 space-y-1">
              {email && (
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <Mail className="w-4 h-4 mr-2" />
                  <a
                    href={`mailto:${email}`}
                    className="hover:text-purple-600 dark:hover:text-purple-400"
                  >
                    {email}
                  </a>
                </div>
              )}

              {tz && (
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <MapPin className="w-4 h-4 mr-2" />
                  {tz}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* User ID */}
        <div className="mt-5 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400">User ID: {id}</div>
        </div>
      </div>
    </div>
  );
}

export { SlackUserInfoDisplay };
