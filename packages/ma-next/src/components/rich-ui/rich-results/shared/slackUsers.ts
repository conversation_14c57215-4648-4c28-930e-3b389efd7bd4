import React from 'react';
import { createStore } from 'stores/createStore';
import { SlackUserInfo } from 'config/nangoModels';
import { invokeFunction } from 'utils/invokeFunction';
import { useEffect } from 'react';

interface SlackUsersState {
  users: Record<string, SlackUserInfo>;
}

class SlackUsersStore {
  private static instance: SlackUsersStore;
  private store;
  private loading = new Set<string>();

  private constructor() {
    this.store = createStore<SlackUsersState>({ users: {} });
  }

  static getInstance(): SlackUsersStore {
    if (!SlackUsersStore.instance) {
      SlackUsersStore.instance = new SlackUsersStore();
    }
    return SlackUsersStore.instance;
  }

  getState() {
    return this.store.getState();
  }

  async fetchUsers(ids: string[]) {
    const missing = ids.filter(id => !this.store.getState().users[id] && !this.loading.has(id));
    if (missing.length === 0) return;

    missing.forEach(id => this.loading.add(id));

    const requests = missing.map(id =>
      invokeFunction<SlackUserInfo | { error: any }>('perform-action', {
        body: {
          providerKey: 'slack',
          actionKey: 'get-user-info',
          actionParameters: { user: id },
        },
      }).then(res => ({ id, res }))
    );

    const results = await Promise.allSettled(requests);
    const newUsers: Record<string, SlackUserInfo> = {};
    for (const r of results) {
      if (r.status === 'fulfilled') {
        const { id, res } = r.value;
        const data = res.data;
        if (data && !(data as any).error) {
          newUsers[id] = data as SlackUserInfo;
        }
      }
    }

    if (Object.keys(newUsers).length > 0) {
      this.store.setState({ users: { ...this.store.getState().users, ...newUsers } });
    }

    missing.forEach(id => this.loading.delete(id));
  }

  useSlackUsers(ids: string[]) {
    useEffect(() => {
      const missing = ids.filter(id => !this.store.getState().users[id] && !this.loading.has(id));
      if (missing.length) {
        this.fetchUsers(missing);
      }
    }, [ids.join(',')]);

    return this.store.useStoreState().users;
  }
}

const slackUsersStore = SlackUsersStore.getInstance();
export { slackUsersStore };
