import { useEffect } from 'react';
import { createStore } from 'stores/createStore';
import { SlackMessageList } from 'config/nangoModels';
import { invokeFunction } from 'utils/invokeFunction';

interface SlackFirstUserState {
  firstUsers: Record<string, string>;
}

class SlackFirstMessageUserStore {
  private static instance: SlackFirstMessageUserStore;
  private store;
  private loading = new Set<string>();

  private constructor() {
    this.store = createStore<SlackFirstUserState>({ firstUsers: {} });
  }

  static getInstance() {
    if (!SlackFirstMessageUserStore.instance) {
      SlackFirstMessageUserStore.instance = new SlackFirstMessageUserStore();
    }
    return SlackFirstMessageUserStore.instance;
  }

  getState() {
    return this.store.getState();
  }

  async fetchFirstUsers(ids: string[], invoker = invokeFunction) {
    const missing = ids.filter(id => !this.store.getState().firstUsers[id] && !this.loading.has(id));
    if (missing.length === 0) return;

    missing.forEach(id => this.loading.add(id));

    const requests = missing.map(id =>
      invoker<SlackMessageList | { error: any }>('perform-action', {
        body: {
          providerKey: 'slack',
          actionKey: 'get-channel-history',
          actionParameters: { channel: id, limit: 1 },
        },
      }).then(res => ({ id, res }))
    );

    const results = await Promise.allSettled(requests);
    const newMap: Record<string, string> = {};
    for (const r of results) {
      if (r.status === 'fulfilled') {
        const { id, res } = r.value;
        const data = res.data as SlackMessageList;
        const userId = data?.messages?.[0]?.user;
        if (userId) {
          newMap[id] = userId;
        }
      }
    }

    if (Object.keys(newMap).length > 0) {
      this.store.setState({ firstUsers: { ...this.store.getState().firstUsers, ...newMap } });
    }

    missing.forEach(id => this.loading.delete(id));
  }

  useFirstUsers(ids: string[]) {
    useEffect(() => {
      const missing = ids.filter(id => !this.store.getState().firstUsers[id] && !this.loading.has(id));
      if (missing.length) {
        this.fetchFirstUsers(missing);
      }
    }, [ids.join(',')]);

    return this.store.useStoreState().firstUsers;
  }
}

export const slackFirstMessageUserStore = SlackFirstMessageUserStore.getInstance();
