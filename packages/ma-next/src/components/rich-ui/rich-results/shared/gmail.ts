import { GmailEmail, GmailMessage, GmailHeader } from 'src/config/nangoModels';

/**
 * Normalize a GmailEmail into a GmailMessage structure so we can reuse
 * the GmailSingleMessageDisplay component.
 */
export function gmailEmailToMessage(email: GmailEmail): GmailMessage {
  const headers: GmailHeader[] = [];
  if (email.sender) headers.push({ name: 'from', value: email.sender });
  if (email.recipients)
    headers.push({ name: 'to', value: email.recipients });
  if (email.subject) headers.push({ name: 'subject', value: email.subject });
  if (email.date) headers.push({ name: 'date', value: email.date });
  if (email.cc) headers.push({ name: 'cc', value: email.cc });
  if (email.bcc) headers.push({ name: 'bcc', value: email.bcc });
  if (email.messageId)
    headers.push({ name: 'message-id', value: email.messageId });
  if (email.inReplyTo)
    headers.push({ name: 'in-reply-to', value: email.inReplyTo });
  if (email.references)
    headers.push({ name: 'references', value: email.references });

  return {
    id: email.id,
    threadId: email.threadId,
    labelIds: email.labels,
    snippet: email.snippet,
    body: email.body,
    attachments: email.attachments as any,
    headers,
  } as GmailMessage;
}
