import { strictEqual } from 'node:assert';
import { test } from 'node:test';
import { slackFirstMessageUserStore } from './slackFirstMessageUser';
// mock function returning a message with a user id
const mockInvoke = async () => ({ data: { messages: [{ user: 'U1' }] } } as any);

(test as any)('fetchFirstUsers stores first user id', async () => {
  await slackFirstMessageUserStore.fetchFirstUsers(['C1'], mockInvoke);
  strictEqual(slackFirstMessageUserStore.getState().firstUsers['C1'], 'U1');
});
