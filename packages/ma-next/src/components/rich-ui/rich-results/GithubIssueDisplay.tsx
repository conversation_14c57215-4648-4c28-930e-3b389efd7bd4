import React from 'react';
import {
  AlertCircle,
  CheckCircle,
  User,
  Calendar,
  MessageSquare,
  Tag,
  Link,
  ThumbsUp,
  ThumbsDown,
  Heart,
  Smile
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubIssue } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubIssueDisplayProps = {
  output: GithubIssue;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a GitHub issue
 */
function GithubIssueDisplay({ output }: GithubIssueDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No issue data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  let formattedClosedAt = '';
  try {
    if (output.created_at) {
      formattedCreatedAt = format(new Date(output.created_at), 'MMM d, yyyy');
    }
    if (output.updated_at) {
      formattedUpdatedAt = format(new Date(output.updated_at), 'MMM d, yyyy');
    }
    if (output.closed_at) {
      formattedClosedAt = format(new Date(output.closed_at), 'MMM d, yyyy');
    }
  } catch (e) {
    formattedCreatedAt = output.created_at || '';
    formattedUpdatedAt = output.updated_at || '';
    formattedClosedAt = output.closed_at || '';
  }

  const isOpen = output.state === 'open';
  const totalReactions = output.reactions ?
    (output.reactions['+1'] || 0) +
    (output.reactions['-1'] || 0) +
    (output.reactions.heart || 0) +
    (output.reactions.laugh || 0) +
    (output.reactions.hooray || 0) +
    (output.reactions.confused || 0) +
    (output.reactions.rocket || 0) +
    (output.reactions.eyes || 0) : 0;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            GitHub Issue #{output.number}
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            {isOpen ? (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <AlertCircle className="w-3 h-3 mr-1" />
                Open
              </span>
            ) : (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                <CheckCircle className="w-3 h-3 mr-1" />
                Closed
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Issue title and link */}
        <div className="mb-4">
          <div className="flex items-start justify-between mb-2">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
              {output.title}
            </h4>
            {output.html_url && (
              <a
                href={output.html_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
              >
                <Link className="w-4 h-4" />
              </a>
            )}
          </div>
        </div>

        {/* Author and dates */}
        <div className="flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-300">
          {output.user && (
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2" />
              <span>by {output.user.login}</span>
            </div>
          )}
          {formattedCreatedAt && (
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Created {formattedCreatedAt}</span>
            </div>
          )}
          {formattedClosedAt && (
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Closed {formattedClosedAt}</span>
            </div>
          )}
        </div>

        {/* Labels */}
        {output.labels && output.labels.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <Tag className="w-4 h-4 text-gray-500 mr-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Labels
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {output.labels.map((label, index) => (
                <span
                  key={label.id || index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  style={{
                    backgroundColor: `#${label.color}20`,
                    color: `#${label.color}`,
                    borderColor: `#${label.color}40`,
                    border: '1px solid'
                  }}
                >
                  {label.name}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Issue body preview */}
        {output.body && (
          <div className="mb-4">
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              <div className="line-clamp-4 whitespace-pre-line">
                {output.body}
              </div>
            </div>
          </div>
        )}

        {/* Stats */}
        <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
          {output.comments > 0 && (
            <div className="flex items-center">
              <MessageSquare className="w-3 h-3 mr-1" />
              {output.comments} comments
            </div>
          )}
          {totalReactions > 0 && (
            <div className="flex items-center">
              <Heart className="w-3 h-3 mr-1" />
              {totalReactions} reactions
            </div>
          )}
          {output.assignees && output.assignees.length > 0 && (
            <div className="flex items-center">
              <User className="w-3 h-3 mr-1" />
              {output.assignees.length} assignee{output.assignees.length > 1 ? 's' : ''}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { GithubIssueDisplay };
