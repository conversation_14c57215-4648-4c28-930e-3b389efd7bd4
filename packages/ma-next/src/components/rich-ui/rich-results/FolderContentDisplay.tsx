import React from 'react';
import {
  Folder,
  File,
  FileText,
  Image,
  Film,
  Music,
  Archive,
  Code,
  Database,
  Calendar,
  Link,
} from 'lucide-react';
import { format } from 'date-fns';

// Define the interfaces based on the information from nangoConstants.ts
interface GoogleDocument {
  id: string;
  name: string;
  mimeType: string;
  parents?: string[];
  modifiedTime?: string;
  createdTime?: string;
  webViewLink?: string;
  kind?: string;
}

interface FolderContent {
  files: GoogleDocument[];
  folders: GoogleDocument[];
  cursor?: string;
}

type FolderContentDisplayProps = {
  output: FolderContent;
};

/**
 * Renders a rich display of Google Drive folder contents
 */
function FolderContentDisplay({ output }: FolderContentDisplayProps) {
  if (!output || (!output.files?.length && !output.folders?.length)) {
    return (
      <div className="p-6 text-center">
        <Folder className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No files or folders found</p>
      </div>
    );
  }

  const { files, folders, cursor } = output;
  const hasMore = !!cursor;
  const totalItems = (files?.length || 0) + (folders?.length || 0);

  // Determine file icon based on mime type
  const getFileIcon = (item: GoogleDocument) => {
    const mimeType = item.mimeType || '';

    if (mimeType.includes('folder')) {
      return <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
    }

    if (mimeType.includes('document')) {
      return <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
    }

    if (mimeType.includes('spreadsheet')) {
      return <Database className="w-5 h-5 text-green-600 dark:text-green-400" />;
    }

    if (mimeType.includes('presentation')) {
      return <FileText className="w-5 h-5 text-orange-600 dark:text-orange-400" />;
    }

    if (mimeType.includes('image')) {
      return <Image className="w-5 h-5 text-purple-600 dark:text-purple-400" />;
    }

    if (mimeType.includes('video')) {
      return <Film className="w-5 h-5 text-red-600 dark:text-red-400" />;
    }

    if (mimeType.includes('audio')) {
      return <Music className="w-5 h-5 text-pink-600 dark:text-pink-400" />;
    }

    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) {
      return <Archive className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />;
    }

    if (
      mimeType.includes('code') ||
      mimeType.includes('javascript') ||
      mimeType.includes('python')
    ) {
      return <Code className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />;
    }

    return <File className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Folder Contents ({totalItems} items)
          </h3>
        </div>
      </div>

      {folders && folders.length > 0 && (
        <div>
          <div className="px-5 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400">
              Folders ({folders.length})
            </h4>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {folders.map((folder, index) => {
              // Format date
              let formattedDate = '';
              try {
                if (folder.modifiedTime) {
                  formattedDate = format(new Date(folder.modifiedTime), 'MMM d, yyyy');
                }
              } catch (e) {
                formattedDate = folder.modifiedTime || '';
              }

              return (
                <div
                  key={folder.id || index}
                  className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3">
                      <div className="w-9 h-9 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                        <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex justify-between items-baseline">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {folder.name}
                        </h4>
                        {formattedDate && (
                          <div className="ml-2 flex-shrink-0 flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <Calendar className="w-3.5 h-3.5 mr-1" />
                            {formattedDate}
                          </div>
                        )}
                      </div>

                      {folder.webViewLink && (
                        <div className="mt-2">
                          <a
                            href={folder.webViewLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:underline"
                          >
                            <Link className="w-3 h-3 mr-1" />
                            Open in Google Drive
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {files && files.length > 0 && (
        <div>
          <div className="px-5 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400">
              Files ({files.length})
            </h4>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {files.map((file, index) => {
              // Format date
              let formattedDate = '';
              try {
                if (file.modifiedTime) {
                  formattedDate = format(new Date(file.modifiedTime), 'MMM d, yyyy');
                }
              } catch (e) {
                formattedDate = file.modifiedTime || '';
              }

              return (
                <div
                  key={file.id || index}
                  className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3">
                      <div className="w-9 h-9 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                        {getFileIcon(file)}
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex justify-between items-baseline">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {file.name}
                        </h4>
                        {formattedDate && (
                          <div className="ml-2 flex-shrink-0 flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <Calendar className="w-3.5 h-3.5 mr-1" />
                            {formattedDate}
                          </div>
                        )}
                      </div>

                      <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        {file.mimeType}
                      </div>

                      {file.webViewLink && (
                        <div className="mt-2">
                          <a
                            href={file.webViewLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:underline"
                          >
                            <Link className="w-3 h-3 mr-1" />
                            Open in Google Drive
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More items available.
          </p>
        </div>
      )}
    </div>
  );
}

export { FolderContentDisplay };
