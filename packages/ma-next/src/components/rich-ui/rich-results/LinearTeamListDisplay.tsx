import React, { useState } from 'react';
import {
  User,
  Calendar,
  Users,
  Lock,
  Globe,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { LinearIcon } from 'components/icons/providers';
import { LinearTeamList } from 'src/config/nangoModels';
import { format } from 'date-fns';

type LinearTeamListDisplayProps = {
  output: LinearTeamList;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Linear team list
 */
function LinearTeamListDisplay({ output }: LinearTeamListDisplayProps) {
  if (!output || !output.teams || output.teams.length === 0) {
    return (
      <div className="p-6 text-center">
        <LinearIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No teams found</p>
      </div>
    );
  }

  // Count teams by privacy
  const privateTeams = output.teams.filter(team => team.private).length;
  const publicTeams = output.teams.length - privateTeams;
  const totalMembers = output.teams.reduce((sum, team) => sum + (team.members?.length || 0), 0);

  // Pagination state
  const [showAll, setShowAll] = useState(false);
  const INITIAL_DISPLAY_COUNT = 10;
  const shouldPaginate = output.teams.length > INITIAL_DISPLAY_COUNT;
  const displayedTeams = shouldPaginate && !showAll
    ? output.teams.slice(0, INITIAL_DISPLAY_COUNT)
    : output.teams;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <LinearIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Linear Teams
          </h3>
          <div className="ml-auto flex items-center space-x-3 text-xs">
            {publicTeams > 0 && (
              <span className="text-green-600 dark:text-green-400">
                {publicTeams} public
              </span>
            )}
            {privateTeams > 0 && (
              <span className="text-red-600 dark:text-red-400">
                {privateTeams} private
              </span>
            )}
            <span className="text-blue-600 dark:text-blue-400">
              {totalMembers} members
            </span>
          </div>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {displayedTeams.map((team, index) => {
          // Format dates
          let formattedCreatedAt = '';
          try {
            if (team.createdAt) {
              formattedCreatedAt = format(new Date(team.createdAt), 'MMM d');
            }
          } catch (e) {
            formattedCreatedAt = team.createdAt || '';
          }

          return (
            <div key={team.id || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              {/* Team header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-start min-w-0 flex-1">
                  <div className="flex-shrink-0 mr-3 mt-0.5">
                    {team.private ? (
                      <Lock className="w-4 h-4 text-red-600 dark:text-red-400" />
                    ) : (
                      <Globe className="w-4 h-4 text-green-600 dark:text-green-400" />
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center mb-1">
                      {team.color && (
                        <div 
                          className="w-3 h-3 rounded mr-2 flex-shrink-0"
                          style={{ backgroundColor: team.color }}
                        />
                      )}
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {team.name}
                      </h4>
                      <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded">
                        {team.key}
                      </span>
                    </div>

                    {/* Team description preview */}
                    {team.description && (
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                        {team.description}
                      </div>
                    )}

                    {/* Team metadata */}
                    <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                        team.private 
                          ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                          : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                      }`}>
                        {team.private ? 'Private' : 'Public'}
                      </span>
                      {team.members && team.members.length > 0 && (
                        <div className="flex items-center">
                          <Users className="w-3 h-3 mr-1" />
                          {team.members.length} member{team.members.length > 1 ? 's' : ''}
                        </div>
                      )}
                      {formattedCreatedAt && (
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {formattedCreatedAt}
                        </div>
                      )}
                    </div>

                    {/* Team members preview */}
                    {team.members && team.members.length > 0 && (
                      <div className="flex items-center mt-2">
                        <div className="flex -space-x-1 mr-2">
                          {team.members.slice(0, 3).map((member, memberIndex) => (
                            member.avatarUrl ? (
                              <img
                                key={member.id || memberIndex}
                                src={member.avatarUrl}
                                alt={member.name}
                                className="w-6 h-6 rounded-full border-2 border-white dark:border-gray-800"
                              />
                            ) : (
                              <div
                                key={member.id || memberIndex}
                                className="w-6 h-6 rounded-full bg-gray-300 dark:bg-gray-600 border-2 border-white dark:border-gray-800 flex items-center justify-center"
                              >
                                <User className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                              </div>
                            )
                          ))}
                          {team.members.length > 3 && (
                            <div className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 border-2 border-white dark:border-gray-800 flex items-center justify-center">
                              <span className="text-xs text-gray-600 dark:text-gray-400">
                                +{team.members.length - 3}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {team.members.slice(0, 2).map(m => m.displayName || m.name).join(', ')}
                          {team.members.length > 2 && ` and ${team.members.length - 2} more`}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {shouldPaginate && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            {showAll ? (
              <>
                <ChevronUp className="w-4 h-4 mr-1" />
                Show less
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4 mr-1" />
                Show {output.teams.length - INITIAL_DISPLAY_COUNT} more teams
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
}

export { LinearTeamListDisplay };
