import React from 'react';
import {
  G<PERSON><PERSON>ranch,
  CheckCircle,
  Link,
  ExternalLink
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubBranchUpdateResult } from 'src/config/nangoModels';

type GithubBranchUpdateResultDisplayProps = {
  output: GithubBranchUpdateResult;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a GitHub branch update result
 */
function GithubBranchUpdateResultDisplay({ output }: GithubBranchUpdateResultDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No branch update result available</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Branch Update Result
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              <CheckCircle className="w-3 h-3 mr-1" />
              Updated
            </span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Update status */}
        <div className="mb-4">
          <div className="flex items-center mb-2">
            <GitBranch className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              Branch Successfully Updated
            </h4>
          </div>
        </div>

        {/* Message */}
        {output.message && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Update Message
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              {output.message}
            </div>
          </div>
        )}

        {/* URL */}
        {output.url && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Reference URL
            </div>
            <div className="flex items-center">
              <a
                href={output.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm flex items-center"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                View on GitHub
              </a>
            </div>
          </div>
        )}

        {/* Additional info */}
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center">
            <CheckCircle className="w-3 h-3 mr-1 text-green-600 dark:text-green-400" />
            The pull request branch has been successfully updated with the latest changes from the base branch.
          </div>
        </div>
      </div>
    </div>
  );
}

export { GithubBranchUpdateResultDisplay };
