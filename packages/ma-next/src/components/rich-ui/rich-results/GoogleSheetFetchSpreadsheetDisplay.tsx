import React from 'react';
import { Database, Link, Table } from 'lucide-react';

// Based on the Zod schema we found in nangoConstants.ts
interface Spreadsheet {
  spreadsheetId: string;
  properties: Record<string, any>;
  sheets: Record<string, any>[];
  namedRanges: Record<string, any>[];
  spreadsheetUrl: string;
  developerMetadata: Record<string, any>[];
  dataSources: Record<string, any>[];
  dataSourceSchedules: Record<string, any>[];
}

type GoogleSheetFetchSpreadsheetDisplayProps = {
  output: Spreadsheet;
};

/**
 * Renders a rich display of a fetched Google Spreadsheet
 */
function GoogleSheetFetchSpreadsheetDisplay({ output }: GoogleSheetFetchSpreadsheetDisplayProps) {
  if (!output || !output.spreadsheetId) {
    return (
      <div className="p-6 text-center">
        <Database className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No spreadsheet information available</p>
      </div>
    );
  }

  // Extract title from properties if available
  const title = output.properties?.title || 'Untitled Spreadsheet';

  // Get sheet names if available
  const sheetNames = output.sheets?.map(sheet => sheet.properties?.title).filter(Boolean) || [];

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Database className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Google Spreadsheet</h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center">
              <Database className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h4>

            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center mb-1">
                <span className="font-medium mr-2">Spreadsheet ID:</span>
                <span className="font-mono">{output.spreadsheetId}</span>
              </div>

              <div className="flex items-center mb-1">
                <span className="font-medium mr-2">Sheets:</span>
                <span>{output.sheets?.length || 0}</span>
              </div>

              <div className="flex items-center mb-1">
                <span className="font-medium mr-2">Named Ranges:</span>
                <span>{output.namedRanges?.length || 0}</span>
              </div>
            </div>

            {sheetNames.length > 0 && (
              <div className="mt-3">
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Sheets:</h5>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3">
                  <ul className="list-disc list-inside text-xs text-gray-700 dark:text-gray-300">
                    {sheetNames.map((name, index) => (
                      <li key={index} className="mb-1">
                        <span className="flex items-center">
                          <Table className="w-3.5 h-3.5 mr-1 text-green-600 dark:text-green-400" />
                          {name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            <div className="mt-4">
              {output.spreadsheetUrl ? (
                <a
                  href={output.spreadsheetUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <Link className="w-3.5 h-3.5 mr-1" />
                  Open in Google Sheets
                </a>
              ) : (
                <a
                  href={`https://docs.google.com/spreadsheets/d/${output.spreadsheetId}/edit`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <Link className="w-3.5 h-3.5 mr-1" />
                  Open in Google Sheets
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { GoogleSheetFetchSpreadsheetDisplay };
