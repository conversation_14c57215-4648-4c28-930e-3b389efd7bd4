import React from 'react';
import { Database, Link, Table, Calendar } from 'lucide-react';
import { format } from 'date-fns';

// Since we don't have a clear definition for the JSONSpreadsheet model, we'll create a flexible interface
interface JSONSpreadsheet {
  id?: string;
  name?: string;
  mimeType?: string;
  sheets?: any[];
  properties?: Record<string, any>;
  modifiedTime?: string;
  createdTime?: string;
  webViewLink?: string;
  spreadsheetUrl?: string;
  [key: string]: any; // Allow for any other properties
}

type GoogleDriveFetchGoogleSheetDisplayProps = {
  output: JSONSpreadsheet;
};

/**
 * Renders a rich display of a fetched Google Sheet in JSON format
 */
function GoogleDriveFetchGoogleSheetDisplay({ output }: GoogleDriveFetchGoogleSheetDisplayProps) {
  if (!output || (!output.id && !output.name)) {
    return (
      <div className="p-6 text-center">
        <Database className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No spreadsheet information available</p>
      </div>
    );
  }

  // Format dates if available
  let formattedModifiedDate = '';
  let formattedCreatedDate = '';

  try {
    if (output.modifiedTime) {
      formattedModifiedDate = format(new Date(output.modifiedTime), 'MMM d, yyyy h:mm a');
    }
    if (output.createdTime) {
      formattedCreatedDate = format(new Date(output.createdTime), 'MMM d, yyyy h:mm a');
    }
  } catch (e) {
    // Use raw date strings if formatting fails
    formattedModifiedDate = output.modifiedTime || '';
    formattedCreatedDate = output.createdTime || '';
  }

  // Get sheet names if available
  const sheetNames =
    output.sheets?.map(sheet => sheet.properties?.title || sheet.name).filter(Boolean) || [];

  // Get title from properties or name
  const title = output.properties?.title || output.name || 'Untitled Spreadsheet';

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Database className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Google Sheet</h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center">
              <Database className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h4>

            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {output.id && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Spreadsheet ID:</span>
                  <span className="font-mono">{output.id}</span>
                </div>
              )}

              {output.mimeType && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Type:</span>
                  <span>{output.mimeType}</span>
                </div>
              )}

              {output.sheets && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Sheets:</span>
                  <span>{output.sheets.length}</span>
                </div>
              )}

              {formattedModifiedDate && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Modified:</span>
                  <span>{formattedModifiedDate}</span>
                </div>
              )}

              {formattedCreatedDate && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Created:</span>
                  <span>{formattedCreatedDate}</span>
                </div>
              )}
            </div>

            {sheetNames.length > 0 && (
              <div className="mt-3">
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Sheets:</h5>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3">
                  <ul className="list-disc list-inside text-xs text-gray-700 dark:text-gray-300">
                    {sheetNames.map((name, index) => (
                      <li key={index} className="mb-1">
                        <span className="flex items-center">
                          <Table className="w-3.5 h-3.5 mr-1 text-green-600 dark:text-green-400" />
                          {name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {(output.webViewLink || output.spreadsheetUrl) && (
              <div className="mt-4">
                <a
                  href={output.webViewLink || output.spreadsheetUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <Link className="w-3.5 h-3.5 mr-1" />
                  Open in Google Sheets
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export { GoogleDriveFetchGoogleSheetDisplay };
