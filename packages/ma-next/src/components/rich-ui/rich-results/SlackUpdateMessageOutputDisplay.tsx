import React from 'react';
import { MessageSquare, Check, X, Hash, <PERSON> } from 'lucide-react';
import { SlackUpdateMessageOutput } from 'src/config/nangoModels';

type SlackUpdateMessageOutputDisplayProps = {
  output: SlackUpdateMessageOutput;
};

/**
 * Renders a rich display of a Slack message update result
 */
function SlackUpdateMessageOutputDisplay({ output }: SlackUpdateMessageOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No message update data available</p>
      </div>
    );
  }

  const isSuccess = output.ok;
  const channel = output.channel || '';
  const timestamp = output.ts || '';
  const messageText = output.text || '';
  const error = output.error || '';

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Message Update
          </h3>
          {isSuccess ? (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              <Check className="w-3 h-3 mr-1" />
              Updated
            </span>
          ) : (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
              <X className="w-3 h-3 mr-1" />
              Failed
            </span>
          )}
        </div>
      </div>

      <div className="p-5 space-y-4">
        {isSuccess ? (
          <>
            {/* Channel info */}
            {channel && (
              <div className="flex items-center mb-3">
                <Hash className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-sm text-gray-800 dark:text-gray-200">{channel}</span>
              </div>
            )}

            {/* Message timestamp */}
            {timestamp && (
              <div className="mb-3 text-xs text-gray-500 dark:text-gray-400">
                Message ID: {timestamp}
              </div>
            )}

            {/* Message content preview if available */}
            {messageText && (
              <div className="mt-3">
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                  Updated Message:
                </div>
                <div className="text-sm text-gray-800 dark:text-gray-200 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700 whitespace-pre-line">
                  {messageText}
                </div>
              </div>
            )}

            {/* Link to view in Slack */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Message successfully updated in Slack
              </p>
            </div>
          </>
        ) : (
          // Error state
          <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
            <div className="flex items-center">
              <X className="w-5 h-5 text-red-500 dark:text-red-400 mr-2" />
              <div className="text-sm font-medium text-red-800 dark:text-red-300">
                Failed to update message
              </div>
            </div>
            {error && (
              <div className="mt-2 text-sm text-red-700 dark:text-red-400">Error: {error}</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export { SlackUpdateMessageOutputDisplay };
