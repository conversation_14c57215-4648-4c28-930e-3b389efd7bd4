import React from 'react';
import { Folder, FileText, File, Image, Film, Music, Archive, Code, Database } from 'lucide-react';
import { format } from 'date-fns';
import { DropboxFileList, DropboxEntry } from 'src/config/nangoModels';

interface DropboxFileListDisplayProps {
  output: DropboxFileList;
}

/**
 * Renders a rich display of Dropbox files and folders
 */
function DropboxFileListDisplay({ output }: DropboxFileListDisplayProps) {
  const data = output;
  const entries = data?.entries || [];
  const hasMore = data?.has_more || false;

  // Check if we have valid data
  if (!entries || entries.length === 0) {
    return (
      <div className="p-6 text-center">
        <Folder className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No files or folders found</p>
      </div>
    );
  }

  // Format file size
  const formatFileSize = (bytes?: number): string => {
    if (bytes === undefined) return '';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  // Determine file icon based on name/type
  const getFileIcon = (entry: DropboxEntry) => {
    if (entry.type === 'folder') {
      return <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
    }

    const extension = entry.name.split('.').pop()?.toLowerCase();

    if (!extension) return <File className="w-5 h-5 text-gray-600 dark:text-gray-400" />;

    // Images
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) {
      return <Image className="w-5 h-5 text-green-600 dark:text-green-400" />;
    }

    // Videos
    if (['mp4', 'mov', 'avi', 'mkv', 'webm'].includes(extension)) {
      return <Film className="w-5 h-5 text-purple-600 dark:text-purple-400" />;
    }

    // Audio
    if (['mp3', 'wav', 'ogg', 'flac'].includes(extension)) {
      return <Music className="w-5 h-5 text-pink-600 dark:text-pink-400" />;
    }

    // Documents
    if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
      return <FileText className="w-5 h-5 text-red-600 dark:text-red-400" />;
    }

    // Code
    if (['js', 'ts', 'py', 'java', 'c', 'cpp', 'html', 'css', 'php', 'rb'].includes(extension)) {
      return <Code className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />;
    }

    // Archives
    if (['zip', 'rar', 'tar', 'gz', '7z'].includes(extension)) {
      return <Archive className="w-5 h-5 text-orange-600 dark:text-orange-400" />;
    }

    // Data
    if (['json', 'csv', 'xml', 'sql', 'db'].includes(extension)) {
      return <Database className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />;
    }

    return <File className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
  };

  // Sort entries: folders first, then files
  const sortedEntries = [...entries].sort((a, b) => {
    if (a.type === 'folder' && b.type !== 'folder') return -1;
    if (a.type !== 'folder' && b.type === 'folder') return 1;
    return a.name.localeCompare(b.name);
  });

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Dropbox Files ({entries.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {sortedEntries.map((entry, index) => {
          // Format date if available
          let formattedDate = '';
          if (entry.server_modified) {
            try {
              formattedDate = format(new Date(entry.server_modified), 'MMM d, yyyy');
            } catch (e) {
              formattedDate = entry.server_modified;
            }
          }

          return (
            <div
              key={entry.id || index}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0 mr-3">
                  <div className="w-9 h-9 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    {getFileIcon(entry)}
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex justify-between items-baseline">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {entry.name}
                    </h4>
                    <div className="ml-2 flex-shrink-0 flex items-center text-xs text-gray-500 dark:text-gray-400">
                      {entry.type !== 'folder' && formatFileSize(entry.size)}
                      {formattedDate && <span className="ml-2">{formattedDate}</span>}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                    {entry.path_display}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More files available.
          </p>
        </div>
      )}
    </div>
  );
}

export { DropboxFileListDisplay };
