import React from 'react';
import { GmailEmail } from 'src/config/nangoModels';
import { GmailSingleMessageDisplay } from './GmailSingleMessageDisplay';
import { gmailEmailToMessage } from './shared/gmail';

type GmailEmailDisplayProps = {
  output: GmailEmail;
};

/**
 * Display a GmailEmail using the existing GmailSingleMessageDisplay component.
 */
function GmailEmailDisplay({ output }: GmailEmailDisplayProps) {
  return <GmailSingleMessageDisplay output={gmailEmailToMessage(output)} />;
}

export { GmailEmailDisplay };
