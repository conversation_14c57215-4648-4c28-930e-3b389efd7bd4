import React, { useState } from 'react';
import {
  User,
  Calendar,
  Tag,
  Link,
  Users,
  Folder,
  ArrowUp,
  ArrowDown,
  Minus,
  Circle,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { LinearIcon } from 'components/icons/providers';
import { LinearIssueList } from 'src/config/nangoModels';
import { format } from 'date-fns';

type LinearIssueListDisplayProps = {
  output: LinearIssueList;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Linear issue list
 */
function LinearIssueListDisplay({ output, actionParameters }: LinearIssueListDisplayProps) {
  if (!output || !output.issues || output.issues.length === 0) {
    return (
      <div className="p-6 text-center">
        <LinearIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No issues found</p>
      </div>
    );
  }

  // Count issues by state type
  const issuesByState = output.issues.reduce((acc, issue) => {
    const stateType = issue.state.type;
    acc[stateType] = (acc[stateType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Extract context from action parameters
  const teamName = actionParameters?.teamId ? 
    output.issues.find(issue => issue.team.id === actionParameters.teamId)?.team.name : null;
  const projectName = actionParameters?.projectId ?
    output.issues.find(issue => issue.project?.id === actionParameters.projectId)?.project?.name : null;

  const contextTitle = teamName ? `${teamName} Issues` : 
                      projectName ? `${projectName} Issues` : 
                      'Linear Issues';

  // Pagination state
  const [showAll, setShowAll] = useState(false);
  const INITIAL_DISPLAY_COUNT = 10;
  const shouldPaginate = output.issues.length > INITIAL_DISPLAY_COUNT;
  const displayedIssues = shouldPaginate && !showAll
    ? output.issues.slice(0, INITIAL_DISPLAY_COUNT)
    : output.issues;

  // Priority icon and color
  const getPriorityDisplay = (priority: number) => {
    switch (priority) {
      case 1:
        return { icon: ArrowUp, color: 'text-red-600 dark:text-red-400' };
      case 2:
        return { icon: ArrowUp, color: 'text-orange-600 dark:text-orange-400' };
      case 3:
        return { icon: Minus, color: 'text-yellow-600 dark:text-yellow-400' };
      case 4:
        return { icon: ArrowDown, color: 'text-blue-600 dark:text-blue-400' };
      default:
        return { icon: Circle, color: 'text-gray-600 dark:text-gray-400' };
    }
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <LinearIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {contextTitle}
          </h3>
          <div className="ml-auto flex items-center space-x-3 text-xs">
            {issuesByState.started && (
              <span className="text-yellow-600 dark:text-yellow-400">
                {issuesByState.started} in progress
              </span>
            )}
            {issuesByState.completed && (
              <span className="text-green-600 dark:text-green-400">
                {issuesByState.completed} completed
              </span>
            )}
            {(issuesByState.backlog || issuesByState.unstarted) && (
              <span className="text-blue-600 dark:text-blue-400">
                {(issuesByState.backlog || 0) + (issuesByState.unstarted || 0)} pending
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {displayedIssues.map((issue, index) => {
          // Format dates
          let formattedCreatedAt = '';
          try {
            if (issue.createdAt) {
              formattedCreatedAt = format(new Date(issue.createdAt), 'MMM d');
            }
          } catch (e) {
            formattedCreatedAt = issue.createdAt || '';
          }

          const priorityDisplay = getPriorityDisplay(issue.priority);
          const PriorityIcon = priorityDisplay.icon;

          return (
            <div key={issue.id || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              {/* Issue header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-start min-w-0 flex-1">
                  <div className="flex-shrink-0 mr-3 mt-0.5">
                    <PriorityIcon className={`w-4 h-4 ${priorityDisplay.color}`} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center mb-1">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {issue.title}
                      </h4>
                      <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
                        {issue.team.key}-{issue.number}
                      </span>
                      {issue.url && (
                        <a
                          href={issue.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
                        >
                          <Link className="w-3 h-3" />
                        </a>
                      )}
                    </div>

                    {/* Issue metadata */}
                    <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                      <span
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                        style={{ backgroundColor: `${issue.state.color}20`, color: issue.state.color }}
                      >
                        {issue.state.name}
                      </span>
                      {issue.assignee && (
                        <div className="flex items-center">
                          <User className="w-3 h-3 mr-1" />
                          {issue.assignee.displayName || issue.assignee.name}
                        </div>
                      )}
                      <div className="flex items-center">
                        <Users className="w-3 h-3 mr-1" />
                        {issue.team.name}
                      </div>
                      {issue.project && (
                        <div className="flex items-center">
                          <Folder className="w-3 h-3 mr-1" />
                          {issue.project.name}
                        </div>
                      )}
                      {formattedCreatedAt && (
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {formattedCreatedAt}
                        </div>
                      )}
                    </div>

                    {/* Labels */}
                    {issue.labels && issue.labels.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {issue.labels.slice(0, 3).map((label, labelIndex) => (
                          <span
                            key={label.id || labelIndex}
                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                            style={{
                              backgroundColor: `${label.color}20`,
                              color: label.color,
                              borderColor: `${label.color}40`,
                              border: '1px solid'
                            }}
                          >
                            {label.name}
                          </span>
                        ))}
                        {issue.labels.length > 3 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            +{issue.labels.length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {shouldPaginate && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            {showAll ? (
              <>
                <ChevronUp className="w-4 h-4 mr-1" />
                Show less
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4 mr-1" />
                Show {output.issues.length - INITIAL_DISPLAY_COUNT} more issues
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
}

export { LinearIssueListDisplay };
