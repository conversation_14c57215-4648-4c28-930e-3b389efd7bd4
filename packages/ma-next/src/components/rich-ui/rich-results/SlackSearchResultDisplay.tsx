import React from 'react';
import { Search, MessageSquare, User, Hash, Lock } from 'lucide-react';
import { SlackSearchResultList } from 'src/config/nangoModels';

interface SlackSearchResultDisplayProps {
  output: SlackSearchResultList;
}

/**
 * Renders a rich display of Slack search results
 */
function SlackSearchResultDisplay({ output }: SlackSearchResultDisplayProps) {
  const data = output;

  // Check if we have valid data
  if (!data || !data.ok) {
    return (
      <div className="p-6 text-center">
        <Search className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">
          {data?.error || 'No search results available'}
        </p>
      </div>
    );
  }

  // Extract messages from the response
  const messages = data.messages?.matches || [];
  const query = data.query || '';

  // If no messages, show empty state
  if (!messages || messages.length === 0) {
    return (
      <div className="p-6 text-center">
        <Search className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No messages found for "{query}"</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Search className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Search Results for "{query}" ({messages.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {messages.map((message, index) => {
          // Extract channel info
          const channelName = message.channel?.name || 'Unknown channel';
          const isPrivate = message.channel?.is_private;

          return (
            <div
              key={message.iid || index}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  <div className="w-9 h-9 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                    <MessageSquare className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  {/* Channel and user info */}
                  <div className="flex items-center mb-1">
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      {isPrivate ? (
                        <Lock className="w-3 h-3 mr-1" />
                      ) : (
                        <Hash className="w-3 h-3 mr-1" />
                      )}
                      <span className="font-medium">{channelName}</span>
                    </div>
                    {message.username && (
                      <div className="flex items-center ml-3 text-xs text-gray-500 dark:text-gray-400">
                        <User className="w-3 h-3 mr-1" />
                        <span>{message.username}</span>
                      </div>
                    )}
                  </div>

                  {/* Message text */}
                  <p className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line">
                    {message.text}
                  </p>

                  {/* Permalink */}
                  {message.permalink && (
                    <div className="mt-2">
                      <a
                        href={message.permalink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-purple-600 dark:text-purple-400 hover:underline"
                      >
                        View in Slack
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export { SlackSearchResultDisplay };
