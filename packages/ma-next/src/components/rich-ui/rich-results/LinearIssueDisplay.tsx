import React from 'react';
import {
  AlertCircle,
  CheckCircle,
  User,
  Calendar,
  Tag,
  Link,
  Users,
  Folder,
  ArrowUp,
  ArrowDown,
  Minus,
  Circle
} from 'lucide-react';
import { LinearIcon } from 'components/icons/providers';
import { LinearIssue } from 'src/config/nangoModels';
import { format } from 'date-fns';

type LinearIssueDisplayProps = {
  output: LinearIssue;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Linear issue
 */
function LinearIssueDisplay({ output }: LinearIssueDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <LinearIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No issue data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  try {
    if (output.createdAt) {
      formattedCreatedAt = format(new Date(output.createdAt), 'MMM d, yyyy');
    }
    if (output.updatedAt) {
      formattedUpdatedAt = format(new Date(output.updatedAt), 'MMM d, yyyy');
    }
  } catch (e) {
    formattedCreatedAt = output.createdAt || '';
    formattedUpdatedAt = output.updatedAt || '';
  }

  // Priority icon and color
  const getPriorityDisplay = (priority: number) => {
    switch (priority) {
      case 1:
        return { icon: ArrowUp, color: 'text-red-600 dark:text-red-400', label: 'Urgent' };
      case 2:
        return { icon: ArrowUp, color: 'text-orange-600 dark:text-orange-400', label: 'High' };
      case 3:
        return { icon: Minus, color: 'text-yellow-600 dark:text-yellow-400', label: 'Medium' };
      case 4:
        return { icon: ArrowDown, color: 'text-blue-600 dark:text-blue-400', label: 'Low' };
      default:
        return { icon: Circle, color: 'text-gray-600 dark:text-gray-400', label: 'None' };
    }
  };

  const priorityDisplay = getPriorityDisplay(output.priority);
  const PriorityIcon = priorityDisplay.icon;

  // State color based on type
  const getStateColor = (state: any) => {
    switch (state.type) {
      case 'backlog':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'unstarted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'started':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'canceled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <LinearIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Linear Issue {output.team.key}-{output.number}
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStateColor(output.state)}`}
              style={{ backgroundColor: `${output.state.color}20`, color: output.state.color }}
            >
              {output.state.name}
            </span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Issue title and link */}
        <div className="mb-4">
          <div className="flex items-start justify-between mb-2">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
              {output.title}
            </h4>
            {output.url && (
              <a
                href={output.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
              >
                <Link className="w-4 h-4" />
              </a>
            )}
          </div>
        </div>

        {/* Priority and metadata */}
        <div className="flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-300">
          <div className="flex items-center">
            <PriorityIcon className={`w-4 h-4 mr-2 ${priorityDisplay.color}`} />
            <span>{priorityDisplay.label} Priority</span>
          </div>
          {output.assignee && (
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2" />
              <span>Assigned to {output.assignee.displayName || output.assignee.name}</span>
            </div>
          )}
          <div className="flex items-center">
            <Users className="w-4 h-4 mr-2" />
            <span>{output.team.name} ({output.team.key})</span>
          </div>
          {output.project && (
            <div className="flex items-center">
              <Folder className="w-4 h-4 mr-2" />
              <span>{output.project.name}</span>
            </div>
          )}
        </div>

        {/* Dates */}
        <div className="flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-300">
          {formattedCreatedAt && (
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Created {formattedCreatedAt}</span>
            </div>
          )}
          {formattedUpdatedAt && formattedUpdatedAt !== formattedCreatedAt && (
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Updated {formattedUpdatedAt}</span>
            </div>
          )}
        </div>

        {/* Labels */}
        {output.labels && output.labels.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <Tag className="w-4 h-4 text-gray-500 mr-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Labels
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {output.labels.map((label, index) => (
                <span
                  key={label.id || index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  style={{
                    backgroundColor: `${label.color}20`,
                    color: label.color,
                    borderColor: `${label.color}40`,
                    border: '1px solid'
                  }}
                >
                  {label.name}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Issue description preview */}
        {output.description && (
          <div className="mb-4">
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              <div className="line-clamp-4 whitespace-pre-line">
                {output.description}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export { LinearIssueDisplay };
