import React from 'react';
import { MessageSquare, Check, X, Hash } from 'lucide-react';
import { SlackSendMessageOutput } from 'src/config/nangoModels';

interface SlackSendMessageResultDisplayProps {
  output: SlackSendMessageOutput;
}

/**
 * Renders a rich display of Slack send message results
 */
function SlackSendMessageResultDisplay({ output }: SlackSendMessageResultDisplayProps) {
  const data = output;

  // Check if we have valid data
  if (!data) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No message data available</p>
      </div>
    );
  }

  const isSuccess = data.ok;
  const channel = data.channel || '';
  const timestamp = data.ts || '';
  const messageText = data.message_text || '';

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Message Result
          </h3>
          {isSuccess ? (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              <Check className="w-3 h-3 mr-1" />
              Sent
            </span>
          ) : (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
              <X className="w-3 h-3 mr-1" />
              Failed
            </span>
          )}
        </div>
      </div>

      <div className="p-4">
        {/* Channel info */}
        {channel && (
          <div className="flex items-center mb-3">
            <Hash className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
            <span className="text-sm text-gray-800 dark:text-gray-200">{channel}</span>
          </div>
        )}

        {/* Message timestamp */}
        {timestamp && (
          <div className="mb-3 text-xs text-gray-500 dark:text-gray-400">
            Message ID: {timestamp}
          </div>
        )}

        {/* Message content preview if available */}
        {messageText && (
          <div className="mt-3">
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
              Message Preview:
            </div>
            <div className="text-sm text-gray-800 dark:text-gray-200 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700 whitespace-pre-line">
              {messageText}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export { SlackSendMessageResultDisplay };
