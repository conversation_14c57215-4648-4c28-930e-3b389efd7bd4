import React from 'react';
import { FileText, Link, Calendar } from 'lucide-react';

// Since we don't have a clear definition for the Document model, we'll create a flexible interface
interface Document {
  id?: string;
  documentId?: string;
  title?: string;
  body?: any;
  content?: string;
  url?: string;
  [key: string]: any; // Allow for any other properties
}

type GoogleDocsFetchDocumentDisplayProps = {
  output: Document;
};

/**
 * Renders a rich display of a fetched Google Docs document
 */
function GoogleDocsFetchDocumentDisplay({ output }: GoogleDocsFetchDocumentDisplayProps) {
  if (!output || (!output.id && !output.documentId)) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No document information available</p>
      </div>
    );
  }

  const documentId = output.id || output.documentId;
  const title = output.title || 'Untitled Document';

  // Determine if we have content to display
  const hasContent = output.content || (output.body && typeof output.body === 'string');
  const content = output.content || (typeof output.body === 'string' ? output.body : null);

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Google Docs Document
          </h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h4>

            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center mb-1">
                <span className="font-medium mr-2">Document ID:</span>
                <span className="font-mono">{documentId}</span>
              </div>

              {/* Display any other relevant metadata */}
              {Object.entries(output).map(([key, value]) => {
                // Skip certain properties we don't want to display
                if (['id', 'documentId', 'title', 'body', 'content'].includes(key)) return null;
                // Skip complex objects
                if (typeof value === 'object') return null;

                return (
                  <div key={key} className="flex items-center mb-1">
                    <span className="font-medium mr-2">
                      {key.charAt(0).toUpperCase() + key.slice(1)}:
                    </span>
                    <span>{String(value)}</span>
                  </div>
                );
              })}
            </div>

            {hasContent && (
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Document Content Preview:
                </h5>
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <div
                    className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line max-h-60 overflow-y-auto"
                    dangerouslySetInnerHTML={{ __html: content || '' }}
                  />
                </div>
              </div>
            )}

            <div className="mt-4">
              <a
                href={output.url || `https://docs.google.com/document/d/${documentId}/edit`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Link className="w-3.5 h-3.5 mr-1" />
                Open in Google Docs
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { GoogleDocsFetchDocumentDisplay };
