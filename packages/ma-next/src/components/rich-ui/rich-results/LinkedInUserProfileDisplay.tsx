import React from 'react';
import { User, <PERSON>, Link } from 'lucide-react';
import { LinkedinIcon } from 'components/icons/lucide-like-social';
import { LinkedInUserProfile } from 'src/config/nangoModels';

type LinkedInUserProfileDisplayProps = {
  output: LinkedInUserProfile;
};

/**
 * Renders a rich display of a LinkedIn user profile
 */
function LinkedInUserProfileDisplay({ output }: LinkedInUserProfileDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <LinkedinIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No LinkedIn profile data available</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <LinkedinIcon className="w-5 h-5 text-blue-700 dark:text-blue-500 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">LinkedIn Profile</h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          {/* Profile Image */}
          <div className="flex-shrink-0 mr-4">
            {output.picture ? (
              <img
                src={output.picture}
                alt={`${output.name}'s profile`}
                className="w-16 h-16 rounded-full"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <User className="w-8 h-8 text-blue-700 dark:text-blue-500" />
              </div>
            )}
          </div>

          {/* Profile Info */}
          <div className="flex-1 min-w-0">
            <h4 className="text-lg font-bold text-gray-900 dark:text-white">{output.name}</h4>

            {(output.given_name || output.family_name) && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {output.given_name} {output.family_name}
              </p>
            )}

            {output.email && (
              <div className="mt-3 flex items-center text-sm text-gray-500 dark:text-gray-400">
                <Mail className="w-4 h-4 mr-1.5" />
                <a
                  href={`mailto:${output.email}`}
                  className="hover:text-blue-700 dark:hover:text-blue-500"
                >
                  {output.email}
                </a>
              </div>
            )}

            {output.locale && typeof output.locale === 'object' && output.locale.country && (
              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Location: {output.locale.country}
              </div>
            )}
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-5 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <div>User ID: {output.sub}</div>
            {output.email_verified && (
              <div className="mt-1">
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  Email Verified
                </span>
              </div>
            )}
          </div>
        </div>

        {/* View on LinkedIn link */}
        <div className="mt-5 pt-4 border-t border-gray-200 dark:border-gray-700">
          <a
            href="https://www.linkedin.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-blue-700 dark:text-blue-500 hover:underline"
          >
            <LinkedinIcon className="w-4 h-4 mr-1" />
            View on LinkedIn
          </a>
        </div>
      </div>
    </div>
  );
}

export { LinkedInUserProfileDisplay };
