import React, { useState } from 'react';
import {
  User,
  Calendar,
  Link,
  Users,
  Circle,
  CheckCircle,
  Clock,
  Archive,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { LinearIcon } from 'components/icons/providers';
import { LinearProjectList } from 'src/config/nangoModels';
import { format } from 'date-fns';

type LinearProjectListDisplayProps = {
  output: LinearProjectList;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Linear project list
 */
function LinearProjectListDisplay({ output }: LinearProjectListDisplayProps) {
  if (!output || !output.projects || output.projects.length === 0) {
    return (
      <div className="p-6 text-center">
        <LinearIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No projects found</p>
      </div>
    );
  }

  // Count projects by state
  const projectsByState = output.projects.reduce((acc, project) => {
    const state = project.state.toLowerCase();
    acc[state] = (acc[state] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Pagination state
  const [showAll, setShowAll] = useState(false);
  const INITIAL_DISPLAY_COUNT = 10;
  const shouldPaginate = output.projects.length > INITIAL_DISPLAY_COUNT;
  const displayedProjects = shouldPaginate && !showAll
    ? output.projects.slice(0, INITIAL_DISPLAY_COUNT)
    : output.projects;

  // Project state display
  const getStateDisplay = (state: string) => {
    switch (state.toLowerCase()) {
      case 'planned':
        return { icon: Clock, color: 'text-blue-600 dark:text-blue-400' };
      case 'started':
        return { icon: Circle, color: 'text-yellow-600 dark:text-yellow-400' };
      case 'completed':
        return { icon: CheckCircle, color: 'text-green-600 dark:text-green-400' };
      case 'canceled':
        return { icon: Archive, color: 'text-red-600 dark:text-red-400' };
      default:
        return { icon: Circle, color: 'text-gray-600 dark:text-gray-400' };
    }
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <LinearIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Linear Projects
          </h3>
          <div className="ml-auto flex items-center space-x-3 text-xs">
            {projectsByState.started && (
              <span className="text-yellow-600 dark:text-yellow-400">
                {projectsByState.started} active
              </span>
            )}
            {projectsByState.completed && (
              <span className="text-green-600 dark:text-green-400">
                {projectsByState.completed} completed
              </span>
            )}
            {projectsByState.planned && (
              <span className="text-blue-600 dark:text-blue-400">
                {projectsByState.planned} planned
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {displayedProjects.map((project, index) => {
          // Format dates
          let formattedCreatedAt = '';
          try {
            if (project.createdAt) {
              formattedCreatedAt = format(new Date(project.createdAt), 'MMM d');
            }
          } catch (e) {
            formattedCreatedAt = project.createdAt || '';
          }

          const stateDisplay = getStateDisplay(project.state);
          const StateIcon = stateDisplay.icon;

          return (
            <div key={project.id || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              {/* Project header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-start min-w-0 flex-1">
                  <div className="flex-shrink-0 mr-3 mt-0.5">
                    <StateIcon className={`w-4 h-4 ${stateDisplay.color}`} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center mb-1">
                      {project.color && (
                        <div 
                          className="w-3 h-3 rounded mr-2 flex-shrink-0"
                          style={{ backgroundColor: project.color }}
                        />
                      )}
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {project.name}
                      </h4>
                      {project.url && (
                        <a
                          href={project.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
                        >
                          <Link className="w-3 h-3" />
                        </a>
                      )}
                    </div>

                    {/* Project description preview */}
                    {project.description && (
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                        {project.description}
                      </div>
                    )}

                    {/* Project metadata */}
                    <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
                        {project.state}
                      </span>
                      {project.lead && (
                        <div className="flex items-center">
                          <User className="w-3 h-3 mr-1" />
                          {project.lead.name}
                        </div>
                      )}
                      {project.teams && project.teams.length > 0 && (
                        <div className="flex items-center">
                          <Users className="w-3 h-3 mr-1" />
                          {project.teams.length} team{project.teams.length > 1 ? 's' : ''}
                        </div>
                      )}
                      {formattedCreatedAt && (
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {formattedCreatedAt}
                        </div>
                      )}
                    </div>

                    {/* Teams */}
                    {project.teams && project.teams.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {project.teams.slice(0, 3).map((team, teamIndex) => (
                          <span
                            key={team.id || teamIndex}
                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                          >
                            {team.name}
                          </span>
                        ))}
                        {project.teams.length > 3 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            +{project.teams.length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {shouldPaginate && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            {showAll ? (
              <>
                <ChevronUp className="w-4 h-4 mr-1" />
                Show less
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4 mr-1" />
                Show {output.projects.length - INITIAL_DISPLAY_COUNT} more projects
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
}

export { LinearProjectListDisplay };
