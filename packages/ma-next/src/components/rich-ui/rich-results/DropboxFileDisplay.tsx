import React from 'react';
import { FileText, Calendar, Link, Download } from 'lucide-react';
import { format } from 'date-fns';
import { DropboxFile } from 'src/config/nangoModels';

interface DropboxFileDisplayProps {
  output: DropboxFile;
}

/**
 * Renders a rich display of a Dropbox file
 */
function DropboxFileDisplay({ output }: DropboxFileDisplayProps) {
  const file = output;

  // Check if we have valid data
  if (!file) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No file data available</p>
      </div>
    );
  }

  // Format date if available
  let formattedDate = '';
  if (file.server_modified) {
    try {
      formattedDate = format(new Date(file.server_modified), 'MMM d, yyyy h:mm a');
    } catch (e) {
      formattedDate = file.server_modified;
    }
  }

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  // Determine file icon based on mime type
  const getFileIcon = () => {
    return <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Dropbox File</h3>
        </div>
      </div>

      <div className="p-5 space-y-4">
        {/* File name and info */}
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
            {getFileIcon()}
          </div>
          <div>
            <h4 className="text-base font-medium text-gray-900 dark:text-white">{file.name}</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
              {formatFileSize(file.size)}
              {file.content_type && ` • ${file.content_type}`}
            </p>
          </div>
        </div>

        {/* Path */}
        {file.path_display && (
          <div className="mt-3">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Path:</div>
            <div className="text-sm text-gray-800 dark:text-gray-200 break-all">
              {file.path_display}
            </div>
          </div>
        )}

        {/* Last modified */}
        {formattedDate && (
          <div className="flex items-start mt-3">
            <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Modified:</div>
              <div className="text-sm text-gray-800 dark:text-gray-200">{formattedDate}</div>
            </div>
          </div>
        )}

        {/* File ID and hash */}
        <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
          <div>ID: {file.id}</div>
          {file.content_hash && <div className="mt-1">Hash: {file.content_hash}</div>}
        </div>

        {/* Actions */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex space-x-4">
          {/* Link to Dropbox */}
          <a
            href={`https://www.dropbox.com/home${file.path_display}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            <Link className="w-4 h-4 mr-1" />
            View in Dropbox
          </a>

          {/* Download link if available */}
          {file.download_url && (
            <a
              href={file.download_url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
            >
              <Download className="w-4 h-4 mr-1" />
              Download
            </a>
          )}
        </div>
      </div>
    </div>
  );
}

export { DropboxFileDisplay };
