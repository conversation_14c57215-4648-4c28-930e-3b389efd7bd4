import React from 'react';
import {
  GitMerge,
  CheckCircle,
  XCircle,
  Link
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubMergeResult } from 'src/config/nangoModels';

type GithubMergeResultDisplayProps = {
  output: GithubMergeResult;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a GitHub merge result
 */
function GithubMergeResultDisplay({ output, actionParameters }: GithubMergeResultDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No merge result data available</p>
      </div>
    );
  }

  // Extract PR info from action parameters
  const repositoryName = actionParameters?.owner && actionParameters?.repo
    ? `${actionParameters.owner}/${actionParameters.repo}`
    : null;
  const prNumber = actionParameters?.pull_number;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {repositoryName && prNumber
              ? `${repositoryName} PR #${prNumber} Merge Result`
              : 'Pull Request Merge Result'}
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            {output.merged ? (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <CheckCircle className="w-3 h-3 mr-1" />
                Merged
              </span>
            ) : (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                <XCircle className="w-3 h-3 mr-1" />
                Failed
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Merge status */}
        <div className="mb-4">
          <div className="flex items-center mb-2">
            <GitMerge className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              {output.merged ? 'Successfully Merged' : 'Merge Failed'}
            </h4>
          </div>
        </div>

        {/* Message */}
        {output.message && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Message
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              {output.message}
            </div>
          </div>
        )}

        {/* Commit SHA */}
        {output.sha && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Merge Commit
            </div>
            <div className="flex items-center">
              <span className="font-mono text-sm bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-3 py-2 rounded border border-gray-300 dark:border-gray-600">
                {output.sha}
              </span>
              <button
                onClick={() => navigator.clipboard.writeText(output.sha)}
                className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-xs"
              >
                Copy
              </button>
            </div>
          </div>
        )}

        {/* Additional info */}
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {output.merged ? (
            <div className="flex items-center">
              <CheckCircle className="w-3 h-3 mr-1 text-green-600 dark:text-green-400" />
              The pull request has been successfully merged into the target branch.
            </div>
          ) : (
            <div className="flex items-center">
              <XCircle className="w-3 h-3 mr-1 text-red-600 dark:text-red-400" />
              The merge operation could not be completed. Check for conflicts or permissions.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { GithubMergeResultDisplay };
