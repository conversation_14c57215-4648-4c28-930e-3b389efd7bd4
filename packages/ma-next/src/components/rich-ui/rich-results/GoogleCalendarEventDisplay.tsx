import { Calendar, Clock, MapPin, User } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { GoogleCalendarEventList, GoogleCalendarEventOutput } from 'src/config/nangoModels';

interface GoogleCalendarEventDisplayProps {
  output: GoogleCalendarEventList;
}

/**
 * Renders a rich display of Google Calendar events
 */
function GoogleCalendarEventDisplay({ output }: GoogleCalendarEventDisplayProps) {
  // Handle the case where data doesn't match expected format
  const events = output?.events || [];

  if (!Array.isArray(events)) {
    return (
      <div className="p-4 text-gray-500 dark:text-gray-400 text-center">
        No events to display or invalid data format
      </div>
    );
  }

  // If no events, show empty state
  if (events.length === 0) {
    return (
      <div className="p-6 text-center">
        <Calendar className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No events found</p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {events.map((event, index: number) => {
        // Format dates
        let startDate = '';
        let endDate = '';
        let isAllDay = false;

        if (event?.start) {
          if (event.start.date) {
            // All-day event
            isAllDay = true;
            try {
              startDate = format(parseISO(event.start.date), 'MMM d, yyyy');
            } catch (e) {
              startDate = event.start.date;
            }
          } else if (event.start.dateTime) {
            try {
              startDate = format(parseISO(event.start.dateTime), 'MMM d, yyyy h:mm a');
            } catch (e) {
              startDate = event.start.dateTime;
            }
          }
        }

        if (event?.end) {
          if (event.end.date) {
            try {
              // For all-day events, the end date is exclusive, so subtract one day for display
              const endDateObj = parseISO(event.end.date);
              const adjustedDate = new Date(endDateObj);
              adjustedDate.setDate(adjustedDate.getDate() - 1);
              endDate = format(adjustedDate, 'MMM d, yyyy');
            } catch (e) {
              endDate = event.end.date;
            }
          } else if (event.end.dateTime) {
            try {
              endDate = format(parseISO(event.end.dateTime), 'MMM d, yyyy h:mm a');
            } catch (e) {
              endDate = event.end.dateTime;
            }
          }
        }

        // Determine if the event spans multiple days
        const isMultiDay = startDate !== endDate && endDate !== '';

        return (
          <div
            key={event.id || index}
            className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-4">
                <div className="w-11 h-11 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
              </div>
              <div className="min-w-0 flex-1">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  {event?.summary || 'Untitled Event'}
                </h4>

                <div className="flex flex-col gap-1.5 text-xs text-gray-500 dark:text-gray-400">
                  {/* Time information */}
                  <div className="flex items-center">
                    <Clock className="w-3.5 h-3.5 mr-1.5 flex-shrink-0" />
                    {isAllDay ? (
                      <span>
                        All day{isMultiDay ? ` · ${startDate} - ${endDate}` : ` · ${startDate}`}
                      </span>
                    ) : (
                      <span>
                        {startDate}
                        {endDate ? ` - ${endDate}` : ''}
                      </span>
                    )}
                  </div>

                  {/* Location if available */}
                  {event?.location && (
                    <div className="flex items-center">
                      <MapPin className="w-3.5 h-3.5 mr-1.5 flex-shrink-0" />
                      <span className="truncate">{event.location}</span>
                    </div>
                  )}

                  {/* Organizer if available */}
                  {event?.organizer && event.organizer.email && (
                    <div className="flex items-center">
                      <User className="w-3.5 h-3.5 mr-1.5 flex-shrink-0" />
                      <span>{event.organizer.displayName || event.organizer.email}</span>
                    </div>
                  )}
                </div>

                {/* Description preview if available */}
                {event?.description && (
                  <p className="mt-3 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                    {event.description.replace(/<[^>]*>/g, '')}
                  </p>
                )}

                {/* Attendees if available */}
                {event?.attendees && event.attendees.length > 0 && (
                  <div className="mt-3">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {event.attendees.length} attendee{event.attendees.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

export { GoogleCalendarEventDisplay };
