import React from 'react';
import { Calendar, Clock, MapPin, Link, User, Users, Check } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { GoogleCalendarEventOutput } from 'src/config/nangoModels';

type GoogleCalendarEventOutputDisplayProps = {
  output: GoogleCalendarEventOutput;
};

/**
 * Renders a rich display of a Google Calendar event creation/update result
 */
function GoogleCalendarEventOutputDisplay({ output }: GoogleCalendarEventOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <Calendar className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No event data available</p>
      </div>
    );
  }

  // Format dates
  let createdDate = '';
  let updatedDate = '';
  let startDate = '';
  let endDate = '';

  try {
    if (output.created) {
      createdDate = format(parseISO(output.created), 'MMM d, yyyy h:mm a');
    }

    if (output.updated) {
      updatedDate = format(parseISO(output.updated), 'MMM d, yyyy h:mm a');
    }

    if (output.start) {
      if (output.start.dateTime) {
        startDate = format(parseISO(output.start.dateTime), 'MMM d, yyyy h:mm a');
      } else if (output.start.date) {
        startDate = format(parseISO(output.start.date), 'MMM d, yyyy');
      }
    }

    if (output.end) {
      if (output.end.dateTime) {
        endDate = format(parseISO(output.end.dateTime), 'MMM d, yyyy h:mm a');
      } else if (output.end.date) {
        endDate = format(parseISO(output.end.date), 'MMM d, yyyy');
      }
    }
  } catch (e) {
    // Fallback to raw dates if parsing fails
    createdDate = output.created || '';
    updatedDate = output.updated || '';
    startDate = output.start?.dateTime || output.start?.date || '';
    endDate = output.end?.dateTime || output.end?.date || '';
  }

  // Determine if it's an all-day event
  const isAllDay = !!output.start?.date;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Calendar Event {output.status === 'confirmed' ? 'Created' : output.status}
          </h3>
        </div>
      </div>

      <div className="p-5 space-y-4">
        {/* Event title and status */}
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
            <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="text-base font-medium text-gray-900 dark:text-white truncate">
              {output.summary}
            </h4>
            {output.status && (
              <div className="mt-1">
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  <Check className="w-3 h-3 mr-1" />
                  {output.status.charAt(0).toUpperCase() + output.status.slice(1)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Event details */}
        <div className="mt-4 space-y-3">
          {/* Time */}
          {(startDate || endDate) && (
            <div className="flex items-start">
              <Clock className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <div className="text-sm text-gray-800 dark:text-gray-200">
                  {isAllDay ? 'All day: ' : ''}
                  {startDate}
                  {endDate && startDate !== endDate && ` - ${endDate}`}
                </div>
                {output.start?.timeZone && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                    Timezone: {output.start.timeZone}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Location */}
          {output.location && (
            <div className="flex items-start">
              <MapPin className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <div className="text-sm text-gray-800 dark:text-gray-200">{output.location}</div>
            </div>
          )}

          {/* Organizer */}
          {output.organizer && (
            <div className="flex items-start">
              <User className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <div className="text-sm text-gray-800 dark:text-gray-200">
                Organizer: {output.organizer.email}
                {output.organizer.self && ' (you)'}
              </div>
            </div>
          )}

          {/* Attendees */}
          {output.attendees && output.attendees.length > 0 && (
            <div className="flex items-start">
              <Users className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Attendees ({output.attendees.length}):
                </div>
                <div className="space-y-1">
                  {output.attendees.map((attendee, index) => (
                    <div
                      key={index}
                      className="text-sm text-gray-600 dark:text-gray-400 flex items-center"
                    >
                      <span className="truncate">{attendee.displayName || attendee.email}</span>
                      {attendee.responseStatus && (
                        <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          {attendee.responseStatus}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Description */}
          {output.description && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description:
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                {output.description}
              </div>
            </div>
          )}
        </div>

        {/* Event link */}
        {output.htmlLink && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <a
              href={output.htmlLink}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
            >
              <Link className="w-4 h-4 mr-1" />
              View in Google Calendar
            </a>
          </div>
        )}
      </div>
    </div>
  );
}

export { GoogleCalendarEventOutputDisplay };
