import React from 'react';
import {
  GitBranch,
  Star,
  GitFork,
  Eye,
  Lock,
  Globe,
  Calendar,
  Tag,
  AlertCircle,
  Link,
  Code
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubRepository } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubRepositoryDisplayProps = {
  output: GithubRepository;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a GitHub repository
 */
function GithubRepositoryDisplay({ output }: GithubRepositoryDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No repository data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  try {
    if (output.created_at) {
      formattedCreatedAt = format(new Date(output.created_at), 'MMM d, yyyy');
    }
    if (output.updated_at) {
      formattedUpdatedAt = format(new Date(output.updated_at), 'MMM d, yyyy');
    }
  } catch (e) {
    formattedCreatedAt = output.created_at || '';
    formattedUpdatedAt = output.updated_at || '';
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            GitHub Repository
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            {output.private ? (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                <Lock className="w-3 h-3 mr-1" />
                Private
              </span>
            ) : (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <Globe className="w-3 h-3 mr-1" />
                Public
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Repository name and description */}
        <div className="mb-4">
          <div className="flex items-center mb-2">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              {output.full_name}
            </h4>
            {output.html_url && (
              <a
                href={output.html_url}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              >
                <Link className="w-4 h-4" />
              </a>
            )}
          </div>
          {output.description && (
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
              {output.description}
            </p>
          )}
        </div>

        {/* Repository stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="flex items-center">
            <Star className="w-4 h-4 text-yellow-500 mr-2" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {output.stargazers_count || 0} stars
            </span>
          </div>
          <div className="flex items-center">
            <GitFork className="w-4 h-4 text-blue-500 mr-2" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {output.forks || 0} forks
            </span>
          </div>
          <div className="flex items-center">
            <Eye className="w-4 h-4 text-green-500 mr-2" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {output.watchers || 0} watchers
            </span>
          </div>
          <div className="flex items-center">
            <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {output.open_issues || 0} issues
            </span>
          </div>
        </div>

        {/* Language and default branch */}
        <div className="flex flex-wrap items-center gap-4 mb-4">
          {output.language && (
            <div className="flex items-center">
              <Code className="w-4 h-4 text-purple-500 mr-2" />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {output.language}
              </span>
            </div>
          )}
          {output.default_branch && (
            <div className="flex items-center">
              <GitBranch className="w-4 h-4 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {output.default_branch}
              </span>
            </div>
          )}
        </div>

        {/* Topics */}
        {output.topics && output.topics.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <Tag className="w-4 h-4 text-gray-500 mr-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Topics
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {output.topics.map((topic, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                >
                  {topic}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Dates */}
        <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
          {formattedCreatedAt && (
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              Created {formattedCreatedAt}
            </div>
          )}
          {formattedUpdatedAt && (
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              Updated {formattedUpdatedAt}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { GithubRepositoryDisplay };
