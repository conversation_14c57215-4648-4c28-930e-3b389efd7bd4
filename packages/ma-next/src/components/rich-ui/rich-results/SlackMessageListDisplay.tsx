import React from 'react';
import { MessageSquare, User, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { SlackMessageList, SlackUserInfo } from 'src/config/nangoModels';
import { useMemo } from 'react';
import { slackUsersStore } from 'components/rich-ui/rich-results/shared/slackUsers';

// Extract all user IDs mentioned in a Slack formatted text string
function extractUserIds(text: string): string[] {
  const ids: string[] = [];
  const regex = /<@([A-Z0-9]+)(?:\|[^>]+)?>/g;
  let match;
  while ((match = regex.exec(text))) {
    ids.push(match[1]);
  }
  return ids;
}

// Replace user mention IDs in a Slack text string with display names
function replaceUserMentions(text: string, userMap: Record<string, SlackUserInfo>): string {
  return text.replace(/<@([A-Z0-9]+)(?:\|[^>]+)?>/g, (_full, id) => {
    const info = userMap[id];
    const name = info?.profile?.display_name || info?.profile?.real_name || info?.name || id;
    return `@${name}`;
  });
}

interface SlackMessageListDisplayProps {
  output: SlackMessageList;
}

/**
 * Renders a rich display of Slack channel message history
 */
function SlackMessageListDisplay({ output }: SlackMessageListDisplayProps) {
  const data = output;

  const idsFromUserField =
    data?.messages?.map(m => m.user).filter((id): id is string => Boolean(id)) || [];
  const idsFromText = data?.messages?.flatMap(m => (m.text ? extractUserIds(m.text) : [])) || [];
  const uniqueIds = useMemo(
    () => Array.from(new Set([...idsFromUserField, ...idsFromText])),
    [output]
  );

  const users = slackUsersStore.useSlackUsers(uniqueIds);

  // Check if we have valid data
  if (!data || !data.messages || data.messages.length === 0) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No messages found</p>
      </div>
    );
  }

  const messages = data.messages;
  const hasMore = data.has_more;
  const nextCursor = data.response_metadata?.next_cursor;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Messages ({messages.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {messages.map((message, index) => {
          const userInfo = message.user ? users[message.user] : undefined;
          const displayName =
            userInfo?.profile?.display_name ||
            userInfo?.profile?.real_name ||
            userInfo?.name ||
            message.user;
          const avatar = userInfo?.profile?.image_512 || userInfo?.profile?.image_original;
          // Format timestamp
          let formattedTime = '';
          try {
            // Slack timestamps are in seconds, JS Date expects milliseconds
            const timestamp = parseFloat(message.ts) * 1000;
            formattedTime = format(new Date(timestamp), 'MMM d, yyyy h:mm a');
          } catch (e) {
            formattedTime = message.ts;
          }

          return (
            <div
              key={message.ts || index}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  {avatar ? (
                    <img src={avatar} alt={displayName} className="w-9 h-9 rounded-full" />
                  ) : (
                    <div className="w-9 h-9 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                      <User className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                  )}
                </div>
                <div className="min-w-0 flex-1">
                  {/* User and timestamp */}
                  <div className="flex items-center mb-1">
                    {displayName && (
                      <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mr-2">
                        {displayName}
                      </div>
                    )}
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <Clock className="w-3 h-3 mr-1" />
                      <span>{formattedTime}</span>
                    </div>
                    {message.thread_ts && message.reply_count && (
                      <div className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                        {message.reply_count} {message.reply_count === 1 ? 'reply' : 'replies'}
                      </div>
                    )}
                  </div>

                  {/* Message text */}
                  <p className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line">
                    {replaceUserMentions(message.text, users)}
                  </p>

                  {/* Thread indicator */}
                  {message.thread_ts && !message.reply_count && (
                    <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">In thread</div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More messages available. {nextCursor ? '' : ''}
          </p>
        </div>
      )}
    </div>
  );
}

export { SlackMessageListDisplay };
