import React from 'react';
import { FileText, Database, Calendar, Link, Archive, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { NotionPageOrDatabase, NotionRichText } from 'src/config/nangoModels';

type NotionPageOrDatabaseDisplayProps = {
  output: NotionPageOrDatabase;
};

/**
 * Renders a rich display of a Notion page or database
 */
function NotionPageOrDatabaseDisplay({ output }: NotionPageOrDatabaseDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No Notion data available</p>
      </div>
    );
  }

  // Extract data
  const isPage = output.object === 'page';
  const isDatabase = output.object === 'database';
  const title = getTitleText(output.title);
  const description = getRichTextContent(output.description);
  const isArchived = output.archived || output.in_trash;

  // Format dates
  let createdDate = '';
  let lastEditedDate = '';

  if (output.created_time) {
    try {
      createdDate = format(new Date(output.created_time), 'MMM d, yyyy h:mm a');
    } catch (e) {
      createdDate = output.created_time;
    }
  }

  if (output.last_edited_time) {
    try {
      lastEditedDate = format(new Date(output.last_edited_time), 'MMM d, yyyy h:mm a');
    } catch (e) {
      lastEditedDate = output.last_edited_time;
    }
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          {isPage ? (
            <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          ) : (
            <Database className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          )}
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Notion {isPage ? 'Page' : isDatabase ? 'Database' : 'Object'}
          </h3>

          {isArchived && (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
              <Archive className="w-3 h-3 mr-1" />
              Archived
            </span>
          )}
        </div>
      </div>

      <div className="p-5 space-y-4">
        {/* Title */}
        <div className="flex items-start">
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {title || 'Untitled'}
            </h4>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">ID: {output.id}</p>
          </div>
        </div>

        {/* Description */}
        {description && (
          <div className="mt-3 text-sm text-gray-700 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
            {description}
          </div>
        )}

        {/* Dates */}
        <div className="flex flex-col space-y-2 mt-4">
          {createdDate && (
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <Calendar className="w-3.5 h-3.5 mr-1.5" />
              <span>Created: {createdDate}</span>
            </div>
          )}

          {lastEditedDate && (
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <Calendar className="w-3.5 h-3.5 mr-1.5" />
              <span>Last edited: {lastEditedDate}</span>
            </div>
          )}
        </div>

        {/* URL */}
        {output.url && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <a
              href={output.url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
            >
              <Link className="w-4 h-4 mr-1" />
              Open in Notion
            </a>
          </div>
        )}
      </div>
    </div>
  );
}

// Helper function to extract text from Notion rich text array
function getTitleText(richTextArray?: NotionRichText[]): string {
  if (!richTextArray || richTextArray.length === 0) return '';

  return richTextArray.map(rt => rt.plain_text || rt.text?.content || '').join('');
}

// Helper function to get content from rich text
function getRichTextContent(richTextArray?: NotionRichText[]): string {
  if (!richTextArray || richTextArray.length === 0) return '';

  return richTextArray.map(rt => rt.plain_text || rt.text?.content || '').join('');
}

export { NotionPageOrDatabaseDisplay };
