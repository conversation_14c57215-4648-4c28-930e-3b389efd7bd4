import React from 'react';
import { MessageSquare, Hash, Lock, User } from 'lucide-react';
import { SlackConversationsList, SlackUserInfo } from 'src/config/nangoModels';
import { slackUsersStore } from './shared/slackUsers';
import { slackFirstMessageUserStore } from './shared/slackFirstMessageUser';

interface SlackConversationListDisplayProps {
  output: SlackConversationsList;
}

/**
 * Renders a rich display of Slack channel list results
 */
function SlackConversationListDisplay({ output }: SlackConversationListDisplayProps) {
  const data = output;
  const channels = data?.channels || [];
  const nextCursor = data?.response_metadata?.next_cursor;

  const imUserIds = channels
    .filter(c => c.is_im && c.user)
    .map(c => c.user as string);

  const mpimIds = channels.filter(c => c.is_mpim && c.id).map(c => c.id as string);

  const firstUsers = slackFirstMessageUserStore.useFirstUsers(mpimIds);
  const mpimUserIds = Object.values(firstUsers);

  const users = slackUsersStore.useSlackUsers([...imUserIds, ...mpimUserIds]);

  // If no channels, show empty state
  if (!channels || channels.length === 0) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No channels found</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Channels ({channels.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {channels.map((channel, index) => {
          const isPublic = channel.is_channel && !channel.is_private;
          const displayName = channel.is_im && channel.user
            ? (
                users[channel.user]?.profile?.display_name ||
                users[channel.user]?.profile?.real_name ||
                users[channel.user]?.name ||
                channel.user
              )
            : channel.name;

          let userInfo: SlackUserInfo | undefined;
          if (channel.is_im && channel.user) {
            userInfo = users[channel.user];
          } else if (channel.is_mpim && channel.id) {
            const firstId = firstUsers[channel.id];
            if (firstId) {
              userInfo = users[firstId];
            }
          }

          const avatar = userInfo?.profile?.image_512 || userInfo?.profile?.image_original;

          return (
            <div
              key={channel.id || index}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center">
                {channel.is_private || channel.is_im || channel.is_mpim ? (
                  <Lock className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                ) : (
                  <Hash className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                )}
                {(channel.is_im || channel.is_mpim) && (
                  <div className="relative mr-2">
                    {avatar ? (
                      <img src={avatar} alt="user" className="w-5 h-5 rounded-sm" />
                    ) : (
                      <div className="w-5 h-5 rounded-sm bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                        <User className="w-3 h-3 text-purple-600 dark:text-purple-400" />
                      </div>
                    )}
                    {channel.is_mpim && channel.num_members !== undefined && (
                      <span className="absolute -top-1 -right-1 bg-purple-600 text-white rounded px-1 text-[9px] leading-none">
                        {channel.num_members}
                      </span>
                    )}
                  </div>
                )}
                <div className="min-w-0 flex-1">
                  <div className="flex justify-between items-baseline">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {displayName}
                    </h4>
                    {isPublic && channel.num_members !== undefined && (
                      <span className="text-xs text-gray-500 dark:text-gray-400 ml-3 flex-shrink-0">
                        {channel.num_members} members
                      </span>
                    )}
                  </div>
                  {isPublic && (
                    <div className="flex mt-1">
                      <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                        {channel.is_member ? (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            Member
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            Not a member
                          </span>
                        )}
                      </span>
                    </div>
                  )}
                  {channel.is_private && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                      Private
                    </span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {nextCursor && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More channels available.
          </p>
        </div>
      )}
    </div>
  );
}

export { SlackConversationListDisplay };
