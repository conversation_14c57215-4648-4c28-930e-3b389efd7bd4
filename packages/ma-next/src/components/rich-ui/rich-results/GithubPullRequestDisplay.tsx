import React from 'react';
import {
  GitPullRequest,
  GitMerge,
  User,
  Calendar,
  MessageSquare,
  FileText,
  Plus,
  Minus,
  Link,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubPullRequest } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubPullRequestDisplayProps = {
  output: GithubPullRequest;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a GitHub pull request
 */
function GithubPullRequestDisplay({ output }: GithubPullRequestDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No pull request data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  let formattedMergedAt = '';
  try {
    if (output.created_at) {
      formattedCreatedAt = format(new Date(output.created_at), 'MMM d, yyyy');
    }
    if (output.updated_at) {
      formattedUpdatedAt = format(new Date(output.updated_at), 'MMM d, yyyy');
    }
    if (output.merged_at) {
      formattedMergedAt = format(new Date(output.merged_at), 'MMM d, yyyy');
    }
  } catch (e) {
    formattedCreatedAt = output.created_at || '';
    formattedUpdatedAt = output.updated_at || '';
    formattedMergedAt = output.merged_at || '';
  }

  const getStatusIcon = () => {
    if (output.merged) {
      return <GitMerge className="w-3 h-3 mr-1" />;
    } else if (output.state === 'closed') {
      return <XCircle className="w-3 h-3 mr-1" />;
    } else {
      return <GitPullRequest className="w-3 h-3 mr-1" />;
    }
  };

  const getStatusColor = () => {
    if (output.merged) {
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    } else if (output.state === 'closed') {
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    } else {
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    }
  };

  const getStatusText = () => {
    if (output.merged) return 'Merged';
    else if (output.state === 'closed') return 'Closed';
    else return 'Open';
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Pull Request #{output.number}
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor()}`}>
              {getStatusIcon()}
              {getStatusText()}
            </span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* PR title and link */}
        <div className="mb-4">
          <div className="flex items-start justify-between mb-2">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
              {output.title}
            </h4>
            {output.html_url && (
              <a
                href={output.html_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
              >
                <Link className="w-4 h-4" />
              </a>
            )}
          </div>
        </div>

        {/* Author and dates */}
        <div className="flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-300">
          {output.user && (
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2" />
              <span>by {output.user.login}</span>
            </div>
          )}
          {formattedCreatedAt && (
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Created {formattedCreatedAt}</span>
            </div>
          )}
          {formattedMergedAt && (
            <div className="flex items-center">
              <GitMerge className="w-4 h-4 mr-2" />
              <span>Merged {formattedMergedAt}</span>
            </div>
          )}
        </div>

        {/* Branch info */}
        {output.head && output.base && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-600 dark:text-gray-300">
              <span className="font-mono text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 px-2 py-1 rounded">
                {output.head.ref}
              </span>
              <span className="mx-2">→</span>
              <span className="font-mono text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300 px-2 py-1 rounded">
                {output.base.ref}
              </span>
            </div>
          </div>
        )}

        {/* PR body preview */}
        {output.body && (
          <div className="mb-4">
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              <div className="line-clamp-4 whitespace-pre-line">
                {output.body}
              </div>
            </div>
          </div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          {output.comments > 0 && (
            <div className="flex items-center">
              <MessageSquare className="w-4 h-4 text-blue-500 mr-2" />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {output.comments} comments
              </span>
            </div>
          )}
          {output.commits > 0 && (
            <div className="flex items-center">
              <FileText className="w-4 h-4 text-purple-500 mr-2" />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {output.commits} commits
              </span>
            </div>
          )}
          {output.additions > 0 && (
            <div className="flex items-center">
              <Plus className="w-4 h-4 text-green-500 mr-2" />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                +{output.additions}
              </span>
            </div>
          )}
          {output.deletions > 0 && (
            <div className="flex items-center">
              <Minus className="w-4 h-4 text-red-500 mr-2" />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                -{output.deletions}
              </span>
            </div>
          )}
        </div>

        {/* Additional info */}
        <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
          {output.changed_files > 0 && (
            <span>{output.changed_files} files changed</span>
          )}
          {output.review_comments > 0 && (
            <span>{output.review_comments} review comments</span>
          )}
          {output.mergeable !== null && (
            <span className={output.mergeable ? 'text-green-600' : 'text-red-600'}>
              {output.mergeable ? 'Mergeable' : 'Conflicts'}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

export { GithubPullRequestDisplay };
