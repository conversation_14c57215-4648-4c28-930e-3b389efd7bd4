import React from 'react';
import { FileText, Link, Calendar } from 'lucide-react';
import { format } from 'date-fns';

// Since we don't have a clear definition for the JSONDocument model, we'll create a flexible interface
interface JSONDocument {
  id?: string;
  name?: string;
  mimeType?: string;
  content?: string;
  body?: any;
  modifiedTime?: string;
  createdTime?: string;
  webViewLink?: string;
  [key: string]: any; // Allow for any other properties
}

type GoogleDriveFetchGoogleDocDisplayProps = {
  output: JSONDocument;
};

/**
 * Renders a rich display of a fetched Google Doc in JSON format
 */
function GoogleDriveFetchGoogleDocDisplay({ output }: GoogleDriveFetchGoogleDocDisplayProps) {
  if (!output || (!output.id && !output.name)) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No document information available</p>
      </div>
    );
  }

  // Format dates if available
  let formattedModifiedDate = '';
  let formattedCreatedDate = '';

  try {
    if (output.modifiedTime) {
      formattedModifiedDate = format(new Date(output.modifiedTime), 'MMM d, yyyy h:mm a');
    }
    if (output.createdTime) {
      formattedCreatedDate = format(new Date(output.createdTime), 'MMM d, yyyy h:mm a');
    }
  } catch (e) {
    // Use raw date strings if formatting fails
    formattedModifiedDate = output.modifiedTime || '';
    formattedCreatedDate = output.createdTime || '';
  }

  // Determine if we have content to display
  const hasContent = output.content || (output.body && typeof output.body === 'string');
  const content = output.content || (typeof output.body === 'string' ? output.body : null);

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Google Doc</h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {output.name || 'Untitled Document'}
            </h4>

            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {output.id && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Document ID:</span>
                  <span className="font-mono">{output.id}</span>
                </div>
              )}

              {output.mimeType && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Type:</span>
                  <span>{output.mimeType}</span>
                </div>
              )}

              {formattedModifiedDate && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Modified:</span>
                  <span>{formattedModifiedDate}</span>
                </div>
              )}

              {formattedCreatedDate && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Created:</span>
                  <span>{formattedCreatedDate}</span>
                </div>
              )}
            </div>

            {hasContent && (
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Document Content Preview:
                </h5>
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <div
                    className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line max-h-60 overflow-y-auto"
                    dangerouslySetInnerHTML={{ __html: content || '' }}
                  />
                </div>
              </div>
            )}

            {output.webViewLink && (
              <div className="mt-4">
                <a
                  href={output.webViewLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Link className="w-3.5 h-3.5 mr-1" />
                  Open in Google Docs
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export { GoogleDriveFetchGoogleDocDisplay };
