import React from 'react';
import { FileText, Link } from 'lucide-react';

// Based on the Zod schema we found in nangoConstants.ts
interface Anonymous_googledrive_action_fetchdocument_output {
  output: string;
}

type GoogleDriveFetchDocumentDisplayProps = {
  output: Anonymous_googledrive_action_fetchdocument_output;
};

/**
 * Renders a rich display of a fetched Google Drive document content
 */
function GoogleDriveFetchDocumentDisplay({ output }: GoogleDriveFetchDocumentDisplayProps) {
  if (!output || !output.output) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No document content available</p>
      </div>
    );
  }

  // Determine if the output is HTML content
  const isHtml = output.output.trim().startsWith('<') && output.output.includes('</');

  // For plain text, limit preview to first 500 characters
  const previewText =
    !isHtml && output.output.length > 500 ? output.output.substring(0, 500) + '...' : output.output;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Google Drive Document Content
          </h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">Document Content</h4>

            <div className="mt-3 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
              <div className="p-3 max-h-60 overflow-y-auto">
                {isHtml ? (
                  <div
                    className="prose prose-sm max-w-none dark:prose-invert"
                    dangerouslySetInnerHTML={{ __html: output.output }}
                  />
                ) : (
                  <pre className="text-xs font-mono text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                    {previewText}
                  </pre>
                )}
              </div>
            </div>

            <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center">
                <span className="font-medium mr-2">Content Length:</span>
                <span>{output.output.length} characters</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { GoogleDriveFetchDocumentDisplay };
