import React from 'react';
import { MessageCir<PERSON>, Heart, Repeat, Share } from 'lucide-react';
import { XIcon } from 'components/icons/lucide-like-social';
import { XSocialPostOutput, XSocialUserProfile } from 'src/config/nangoModels';
import { format } from 'date-fns';

type XSocialPostOutputDisplayProps = {
  output: XSocialPostOutput;
  userProfile?: XSocialUserProfile;
};

/**
 * Renders a rich display of a posted X (Twitter)
 * Can optionally display user profile information if available
 */
function XSocialPostOutputDisplay({ output, userProfile }: XSocialPostOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <XIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No post data available</p>
      </div>
    );
  }

  // Format the post date
  let formattedDate = '';
  try {
    if (output.created_at) {
      formattedDate = format(new Date(output.created_at), 'MMM d, yyyy');
    }
  } catch (e) {
    formattedDate = output.created_at || '';
  }

  const postUrl = `https://x.com/i/web/status/${output.id}`;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <h3 className="text-sm font-medium text-gray-900 flex space-x-2 items-center dark:text-white">
            X Post
          </h3>
        </div>
      </div>

      <div className="p-5">
        {/* Post content with user info */}
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            {userProfile?.profile_image_url ? (
              <img
                src={userProfile.profile_image_url}
                alt={`${userProfile.name}'s profile`}
                className="w-10 h-10 rounded-full"
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <XIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            )}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <span className="font-bold text-gray-900 dark:text-white">
                {userProfile?.name || 'User'}
              </span>
              <span className="ml-1 text-gray-500 dark:text-gray-400">
                @{userProfile?.username || 'username'}
              </span>
              <span className="mx-1 text-gray-500 dark:text-gray-400">·</span>
              <span className="text-gray-500 dark:text-gray-400 text-sm">{formattedDate}</span>
            </div>

            {/* Post text */}
            <div className="mt-2 text-base text-gray-800 dark:text-gray-200 whitespace-pre-line">
              {output.text}
            </div>

            {/* Post actions */}
            <div className="mt-3 flex justify-between max-w-md">
              <a
                href={postUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400"
              >
                <MessageCircle className="w-4 h-4" />
                <span className="ml-1 text-xs">0</span>
              </a>
              <a
                href={postUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-gray-500 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400"
              >
                <Repeat className="w-4 h-4" />
                <span className="ml-1 text-xs">0</span>
              </a>
              <a
                href={postUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400"
              >
                <Heart className="w-4 h-4" />
                <span className="ml-1 text-xs">0</span>
              </a>
              <a
                href={postUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400"
              >
                <Share className="w-4 h-4" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { XSocialPostOutputDisplay };
