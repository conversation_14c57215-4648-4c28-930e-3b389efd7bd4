import React from 'react';
import {
  FileText,
  File,
  Image,
  Film,
  Music,
  Archive,
  Database,
  Calendar,
  Link,
} from 'lucide-react';
import { format } from 'date-fns';
import { GoogleDriveDocumentList, GoogleDriveDocument } from 'src/config/nangoModels';

type GoogleDriveDocumentListDisplayProps = {
  output: GoogleDriveDocumentList;
};

/**
 * Renders a rich display of Google Drive document list
 */
function GoogleDriveDocumentListDisplay({ output }: GoogleDriveDocumentListDisplayProps) {
  console.log('GoogleDriveDocumentListDisplay', output);

  if (!output || !output.documents || output.documents.length === 0) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No documents found</p>
      </div>
    );
  }

  const documents = output.documents;
  const hasMore = !!output.nextPageToken;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Google Drive Documents ({documents.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {documents.map((document, index) => {
          // Format date
          let formattedDate = '';
          try {
            formattedDate = format(new Date(document.modifiedTime), 'MMM d, yyyy');
          } catch (e) {
            formattedDate = document.modifiedTime;
          }

          return (
            <div
              key={document.id || index}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  <div className="w-9 h-9 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    {getFileIcon(document)}
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex justify-between items-baseline">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {document.name}
                    </h4>
                    <div className="ml-2 flex-shrink-0 flex items-center text-xs text-gray-500 dark:text-gray-400">
                      {formattedDate}
                    </div>
                  </div>

                  <div className="mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <span className="truncate">{getFileTypeLabel(document.mimeType)}</span>
                    {document.size && <span className="ml-2">{formatFileSize(document.size)}</span>}
                  </div>

                  {document.webViewLink && (
                    <div className="mt-2">
                      <a
                        href={document.webViewLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        <Link className="w-3 h-3 mr-1" />
                        Open in Google Drive
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More documents available. Use nextPageToken for pagination.
          </p>
        </div>
      )}
    </div>
  );
}

// Helper function to get file icon based on mime type
function getFileIcon(document: GoogleDriveDocument) {
  const mimeType = document.mimeType;

  if (mimeType.includes('folder')) {
    return <File className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
  }

  if (mimeType.includes('document')) {
    return <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
  }

  if (mimeType.includes('spreadsheet')) {
    return <Database className="w-5 h-5 text-green-600 dark:text-green-400" />;
  }

  if (mimeType.includes('presentation')) {
    return <FileText className="w-5 h-5 text-orange-600 dark:text-orange-400" />;
  }

  if (mimeType.includes('image')) {
    return <Image className="w-5 h-5 text-purple-600 dark:text-purple-400" />;
  }

  if (mimeType.includes('video')) {
    return <Film className="w-5 h-5 text-red-600 dark:text-red-400" />;
  }

  if (mimeType.includes('audio')) {
    return <Music className="w-5 h-5 text-pink-600 dark:text-pink-400" />;
  }

  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) {
    return <Archive className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />;
  }

  return <File className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
}

// Helper function to get readable file type
function getFileTypeLabel(mimeType: string): string {
  if (mimeType.includes('folder')) return 'Folder';
  if (mimeType.includes('document')) return 'Google Document';
  if (mimeType.includes('spreadsheet')) return 'Google Spreadsheet';
  if (mimeType.includes('presentation')) return 'Google Presentation';
  if (mimeType.includes('image')) return 'Image';
  if (mimeType.includes('video')) return 'Video';
  if (mimeType.includes('audio')) return 'Audio';

  // Extract the subtype from the MIME type
  const parts = mimeType.split('/');
  if (parts.length === 2) {
    return parts[1].toUpperCase();
  }

  return mimeType;
}

// Helper function to format file size
function formatFileSize(sizeStr: string): string {
  const size = parseInt(sizeStr, 10);
  if (isNaN(size)) return sizeStr;

  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
}

export { GoogleDriveDocumentListDisplay };
