import React from 'react';
import { Folder, Calendar, <PERSON> } from 'lucide-react';
import { format } from 'date-fns';
import { GoogleDriveFolderList, GoogleDriveFolder } from 'src/config/nangoModels';

type GoogleDriveFolderListDisplayProps = {
  output: GoogleDriveFolderList;
};

/**
 * Renders a rich display of Google Drive folders
 */
function GoogleDriveFolderListDisplay({ output }: GoogleDriveFolderListDisplayProps) {
  if (!output || !output.folders || output.folders.length === 0) {
    return (
      <div className="p-6 text-center">
        <Folder className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No folders found</p>
      </div>
    );
  }

  const folders = output.folders;
  const hasMore = !!output.nextPageToken;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Google Drive Folders ({folders.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {folders.map((folder, index) => {
          // Format date
          let formattedDate = '';
          try {
            formattedDate = format(new Date(folder.modifiedTime), 'MMM d, yyyy');
          } catch (e) {
            formattedDate = folder.modifiedTime;
          }

          return (
            <div
              key={folder.id || index}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  <div className="w-9 h-9 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex justify-between items-baseline">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {folder.name}
                    </h4>
                    <div className="ml-2 flex-shrink-0 flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <Calendar className="w-3.5 h-3.5 mr-1" />
                      {formattedDate}
                    </div>
                  </div>

                  <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    ID: {folder.id}
                  </div>

                  {folder.webViewLink && (
                    <div className="mt-2">
                      <a
                        href={folder.webViewLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        <Link className="w-3 h-3 mr-1" />
                        Open in Google Drive
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More folders available. Use nextPageToken for pagination.
          </p>
        </div>
      )}
    </div>
  );
}

export { GoogleDriveFolderListDisplay };
