import React from 'react';
import {
  User,
  Calendar,
  Link,
  Users,
  FileText,
  Circle,
  CheckCircle,
  Clock,
  Archive
} from 'lucide-react';
import { LinearIcon } from 'components/icons/providers';
import { LinearProject } from 'src/config/nangoModels';
import { format } from 'date-fns';

type LinearProjectDisplayProps = {
  output: LinearProject;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Linear project
 */
function LinearProjectDisplay({ output }: LinearProjectDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <LinearIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No project data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  try {
    if (output.createdAt) {
      formattedCreatedAt = format(new Date(output.createdAt), 'MMM d, yyyy');
    }
    if (output.updatedAt) {
      formattedUpdatedAt = format(new Date(output.updatedAt), 'MMM d, yyyy');
    }
  } catch (e) {
    formattedCreatedAt = output.createdAt || '';
    formattedUpdatedAt = output.updatedAt || '';
  }

  // Project state display
  const getStateDisplay = (state: string) => {
    switch (state.toLowerCase()) {
      case 'planned':
        return { icon: Clock, color: 'text-blue-600 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-900', text: 'text-blue-800 dark:text-blue-300' };
      case 'started':
        return { icon: Circle, color: 'text-yellow-600 dark:text-yellow-400', bg: 'bg-yellow-100 dark:bg-yellow-900', text: 'text-yellow-800 dark:text-yellow-300' };
      case 'completed':
        return { icon: CheckCircle, color: 'text-green-600 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-900', text: 'text-green-800 dark:text-green-300' };
      case 'canceled':
        return { icon: Archive, color: 'text-red-600 dark:text-red-400', bg: 'bg-red-100 dark:bg-red-900', text: 'text-red-800 dark:text-red-300' };
      default:
        return { icon: Circle, color: 'text-gray-600 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-900', text: 'text-gray-800 dark:text-gray-300' };
    }
  };

  const stateDisplay = getStateDisplay(output.state);
  const StateIcon = stateDisplay.icon;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <LinearIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Linear Project
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${stateDisplay.bg} ${stateDisplay.text}`}>
              <StateIcon className="w-3 h-3 mr-1" />
              {output.state}
            </span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Project title and link */}
        <div className="mb-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center">
              {output.color && (
                <div 
                  className="w-4 h-4 rounded mr-3 flex-shrink-0"
                  style={{ backgroundColor: output.color }}
                />
              )}
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
                {output.name}
              </h4>
            </div>
            {output.url && (
              <a
                href={output.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
              >
                <Link className="w-4 h-4" />
              </a>
            )}
          </div>
        </div>

        {/* Project description */}
        {output.description && (
          <div className="mb-4">
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              <div className="flex items-center mb-2">
                <FileText className="w-4 h-4 text-gray-500 mr-2" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Description
                </span>
              </div>
              <div className="whitespace-pre-line">
                {output.description}
              </div>
            </div>
          </div>
        )}

        {/* Project lead */}
        {output.lead && (
          <div className="mb-4">
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <User className="w-4 h-4 mr-2" />
              <span>Project Lead: {output.lead.name}</span>
              {output.lead.email && (
                <span className="ml-2 text-gray-500">({output.lead.email})</span>
              )}
            </div>
          </div>
        )}

        {/* Teams */}
        {output.teams && output.teams.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <Users className="w-4 h-4 text-gray-500 mr-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Teams ({output.teams.length})
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {output.teams.map((team, index) => (
                <span
                  key={team.id || index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                >
                  {team.name} ({team.key})
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Dates */}
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
          {formattedCreatedAt && (
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Created {formattedCreatedAt}</span>
            </div>
          )}
          {formattedUpdatedAt && formattedUpdatedAt !== formattedCreatedAt && (
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Updated {formattedUpdatedAt}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { LinearProjectDisplay };
