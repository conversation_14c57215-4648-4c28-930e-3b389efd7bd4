import React from 'react';
import { Calendar, Clock, MapPin, Star } from 'lucide-react';
import { GoogleCalendarList } from 'src/config/nangoModels';

type GoogleCalendarListDisplayProps = {
  output: GoogleCalendarList;
};

/**
 * Renders a rich display of Google Calendar list
 */
function GoogleCalendarListDisplay({ output }: GoogleCalendarListDisplayProps) {
  if (!output || !output.calendars || output.calendars.length === 0) {
    return (
      <div className="p-6 text-center">
        <Calendar className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No calendars found</p>
      </div>
    );
  }

  const calendars = output.calendars;
  const hasMore = !!output.nextPageToken;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Google Calendars ({calendars.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {calendars.map((calendar, index) => (
          <div
            key={calendar.id || index}
            className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-3">
                <div
                  className="w-9 h-9 rounded-full flex items-center justify-center"
                  style={{
                    backgroundColor: calendar.backgroundColor || '#4285F4',
                    color: calendar.foregroundColor || 'white',
                  }}
                >
                  <Calendar className="w-5 h-5" />
                </div>
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-baseline">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {calendar.summary}
                  </h4>
                  {calendar.primary && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                      <Star className="w-3 h-3 mr-1" />
                      Primary
                    </span>
                  )}
                </div>

                {calendar.description && (
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {calendar.description}
                  </p>
                )}

                <div className="mt-2 flex flex-wrap gap-2">
                  {calendar.timeZone && (
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <Clock className="w-3 h-3 mr-1" />
                      {calendar.timeZone}
                    </div>
                  )}

                  {calendar.location && (
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <MapPin className="w-3 h-3 mr-1" />
                      {calendar.location}
                    </div>
                  )}

                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    Access: {formatAccessRole(calendar.accessRole)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More calendars available. Use nextPageToken for pagination.
          </p>
        </div>
      )}
    </div>
  );
}

// Helper function to format access role
function formatAccessRole(role: string): string {
  switch (role) {
    case 'owner':
      return 'Owner';
    case 'writer':
      return 'Can edit';
    case 'reader':
      return 'Read-only';
    case 'freeBusyReader':
      return 'Free/Busy only';
    default:
      return role;
  }
}

export { GoogleCalendarListDisplay };
