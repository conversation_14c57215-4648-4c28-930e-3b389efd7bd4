import React from 'react';
import { FileText, Link, Upload, Calendar } from 'lucide-react';
import { format } from 'date-fns';

// Interface for GoogleDocument based on what we found in FolderContentDisplay.tsx
interface GoogleDocument {
  id: string;
  name: string;
  mimeType: string;
  parents?: string[];
  modifiedTime?: string;
  createdTime?: string;
  webViewLink?: string;
  kind?: string;
}

type GoogleDriveUploadDocumentDisplayProps = {
  output: GoogleDocument;
};

/**
 * Renders a rich display of an uploaded Google Drive document
 */
function GoogleDriveUploadDocumentDisplay({ output }: GoogleDriveUploadDocumentDisplayProps) {
  if (!output || !output.id) {
    return (
      <div className="p-6 text-center">
        <Upload className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No upload information available</p>
      </div>
    );
  }

  // Format dates if available
  let formattedModifiedDate = '';
  let formattedCreatedDate = '';

  try {
    if (output.modifiedTime) {
      formattedModifiedDate = format(new Date(output.modifiedTime), 'MMM d, yyyy h:mm a');
    }
    if (output.createdTime) {
      formattedCreatedDate = format(new Date(output.createdTime), 'MMM d, yyyy h:mm a');
    }
  } catch (e) {
    // Use raw date strings if formatting fails
    formattedModifiedDate = output.modifiedTime || '';
    formattedCreatedDate = output.createdTime || '';
  }

  // Get file icon based on mime type
  const getFileIcon = () => {
    const mimeType = output.mimeType || '';

    if (mimeType.includes('document')) {
      return <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />;
    }

    if (mimeType.includes('spreadsheet')) {
      return <FileText className="w-6 h-6 text-green-600 dark:text-green-400" />;
    }

    if (mimeType.includes('presentation')) {
      return <FileText className="w-6 h-6 text-orange-600 dark:text-orange-400" />;
    }

    return <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />;
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Upload className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Document Uploaded to Google Drive
          </h3>
        </div>
      </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              {getFileIcon()}
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {output.name || 'Untitled Document'}
            </h4>

            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center mb-1">
                <span className="font-medium mr-2">Document ID:</span>
                <span className="font-mono">{output.id}</span>
              </div>

              <div className="flex items-center mb-1">
                <span className="font-medium mr-2">Type:</span>
                <span>{output.mimeType}</span>
              </div>

              {output.parents && output.parents.length > 0 && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Parent Folder:</span>
                  <span className="font-mono">{output.parents[0]}</span>
                </div>
              )}

              {formattedCreatedDate && (
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Created:</span>
                  <span>{formattedCreatedDate}</span>
                </div>
              )}
            </div>

            {output.webViewLink && (
              <div className="mt-4">
                <a
                  href={output.webViewLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Link className="w-3.5 h-3.5 mr-1" />
                  Open in Google Drive
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export { GoogleDriveUploadDocumentDisplay };
