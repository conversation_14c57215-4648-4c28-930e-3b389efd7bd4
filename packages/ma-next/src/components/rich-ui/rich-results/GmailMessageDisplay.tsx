import { Mail } from 'lucide-react';
import { format } from 'date-fns';
import { GmailMessageList, GmailBasicMessageDetails } from 'src/config/nangoModels';

interface GmailMessageDisplayProps {
  output: GmailMessageList;
}

/**
 * Renders a rich display of Gmail messages
 */
function GmailMessageDisplay({ output }: GmailMessageDisplayProps) {
  // Handle the case where data doesn't match expected format
  const messages = output?.messages || [];

  if (!Array.isArray(messages)) {
    return (
      <div className="p-4 text-gray-500 dark:text-gray-400 text-center">
        No messages to display or invalid data format
      </div>
    );
  }

  // If no messages, show empty state
  if (messages.length === 0) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No messages found</p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {messages.map((message: GmailBasicMessageDetails, index: number) => {
        // Format date if available
        let formattedDate = '';
        if (message?.date) {
          try {
            formattedDate = format(new Date(message.date), 'MMM d, yyyy h:mm a');
          } catch (e) {
            formattedDate = message.date;
          }
        }

        return (
          <div
            key={message.id || index}
            className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-4">
                <div className="w-11 h-11 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                  <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex justify-between items-baseline mb-2">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {message?.subject || 'No Subject'}
                  </h4>
                  {formattedDate && (
                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-3 flex-shrink-0">
                      {formattedDate}
                    </span>
                  )}
                </div>
                {message?.snippet && (
                  <p
                    className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2 mb-2"
                    dangerouslySetInnerHTML={{ __html: message.snippet }}
                  />
                )}
                {message?.labelIds && message?.labelIds.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1.5">
                    {message?.labelIds.map((label: string) => (
                      <span
                        key={label}
                        className="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                      >
                        {label.replace('CATEGORY_', '').toLowerCase()}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

export { GmailMessageDisplay };
