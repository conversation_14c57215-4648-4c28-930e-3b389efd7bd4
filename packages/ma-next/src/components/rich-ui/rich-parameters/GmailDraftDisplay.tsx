import { GmailDraftInput } from 'src/config/nangoModels';
import { GmailComposeFields } from './shared/GmailComposeFields';

/**
 * Renders a rich display of Gmail draft parameters
 */
function GmailDraftDisplay({ parameters }: { parameters: GmailDraftInput }) {
  const recipient = parameters?.recipient || '';
  const subject = parameters?.subject || '';
  const body = parameters?.body || '';

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      <GmailComposeFields address={recipient} subject={subject} body={body} />
    </div>
  );
}

export { GmailDraftDisplay };
