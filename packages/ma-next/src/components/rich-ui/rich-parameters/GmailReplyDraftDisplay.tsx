import React from 'react';
import { GmailReplyDraftInput } from 'src/config/nangoModels';
import { GmailComposeFields } from './shared/GmailComposeFields';

function GmailReplyDraftDisplay({ parameters }: { parameters: GmailReplyDraftInput }) {
  const to = parameters?.sender || '';
  const subject = parameters?.subject || '';
  const body = parameters?.replyBody || '';

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      <GmailComposeFields address={to} subject={subject} body={body} />
    </div>
  );
}

export { GmailReplyDraftDisplay };
