import React from 'react';
import { Search, SortAsc, SortDesc, Filter } from 'lucide-react';
import { NotionSearchInput } from 'src/config/nangoModels';

interface NotionSearchDisplayProps {
  parameters: NotionSearchInput;
}

/**
 * Renders a rich display of Notion search parameters
 */
function NotionSearchDisplay({ parameters }: NotionSearchDisplayProps) {
  if (!parameters) {
    return null;
  }

  const { query, sort, filter, page_size } = parameters;

  // Determine sort direction and field
  const sortDirection = sort?.direction || '';
  const sortField = sort?.timestamp || '';

  // Determine filter type and value
  const filterType = filter?.property || '';
  const filterValue = filter?.value || '';

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      <div className="p-5 space-y-4">
        {/* Header with icon */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-900 flex items-center justify-center mr-3">
            <Search className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </div>
          <h3 className="text-base font-medium text-gray-900 dark:text-white">Notion Search</h3>
        </div>

        {/* Search query */}
        <div className="p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Search className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
            <span className="text-gray-800 dark:text-gray-200">
              {query ? `"${query}"` : 'All pages and databases'}
            </span>
          </div>
        </div>

        {/* Filters and sorting */}
        <div className="flex flex-wrap gap-3">
          {/* Sort */}
          {sortDirection && sortField && (
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
              {sortDirection === 'ascending' ? (
                <SortAsc className="w-3 h-3 mr-1" />
              ) : (
                <SortDesc className="w-3 h-3 mr-1" />
              )}
              Sort: {sortField.replace('_', ' ')} ({sortDirection})
            </div>
          )}

          {/* Filter */}
          {filterType && filterValue && (
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
              <Filter className="w-3 h-3 mr-1" />
              Filter: {filterType} = {filterValue}
            </div>
          )}

          {/* Page size */}
          {page_size && (
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
              Limit: {page_size} results
            </div>
          )}
        </div>

        {/* Empty state for no filters */}
        {!sortDirection && !filterType && !page_size && (
          <div className="text-sm text-gray-500 dark:text-gray-400 italic">
            No filters or sorting applied
          </div>
        )}

        {/* Help text */}
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
          <p>Searching Notion for {query ? `"${query}"` : 'all content'}</p>
          <p className="mt-1">Use an empty query ("") to search for all pages and databases</p>
        </div>
      </div>
    </div>
  );
}

export { NotionSearchDisplay };
