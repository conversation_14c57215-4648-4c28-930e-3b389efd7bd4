import React from 'react';
import { User, AlignLeft } from 'lucide-react';

interface GmailComposeFieldsProps {
  address: string;
  subject: string;
  body?: string;
  label?: string;
}

function GmailComposeFields({ address, subject, body, label = 'To:' }: GmailComposeFieldsProps) {
  return (
    <div className="p-5 space-y-4">
      <div className="flex items-center mb-3">
        <User className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 flex-shrink-0" />
        <div className="flex-1 flex">
          <div className="text-sm min-w-20 font-medium text-gray-500 dark:text-gray-400 mb-1">
            {label}
          </div>
          <div className="text-sm text-gray-800 dark:text-gray-200">{address}</div>
        </div>
      </div>
      <div className="flex items-center mb-3">
        <AlignLeft className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 flex-shrink-0" />
        <div className="flex-1 flex">
          <div className="text-sm min-w-20 font-medium text-gray-500 dark:text-gray-400 mb-1">
            Subject:
          </div>
          <div className="text-sm font-medium text-gray-800 dark:text-gray-200">{subject}</div>
        </div>
      </div>
      <div className="border-t border-gray-200 dark:border-gray-700 my-3"></div>
      <div className="whitespace-pre-line text-sm text-gray-800 dark:text-gray-200 p-2 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
        {body}
      </div>
    </div>
  );
}

export { GmailComposeFields };
