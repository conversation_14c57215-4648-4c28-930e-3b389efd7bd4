import React from 'react';
import { MessageSquare, Hash, Reply } from 'lucide-react';
import { SlackSendMessageInput } from 'src/config/nangoModels';

interface SlackSendMessageDisplayProps {
  parameters: SlackSendMessageInput;
}

/**
 * Renders a rich display of Slack send message parameters
 */
function SlackSendMessageDisplay({ parameters }: SlackSendMessageDisplayProps) {
  // Extract parameters
  const channel = parameters.channel || '';
  const text = parameters.text || '';
  const threadTs = parameters.thread_ts || '';

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      <div className="p-5 space-y-4">
        {/* Header with icon */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3">
            <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 className="text-base font-medium text-gray-900 dark:text-white">Slack Message</h3>
        </div>

        {/* Channel */}
        <div className="flex items-start">
          <Hash className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
              Channel:
            </div>
            <div className="text-sm text-gray-800 dark:text-gray-200">{channel}</div>
          </div>
        </div>

        {/* Thread if applicable */}
        {threadTs && (
          <div className="flex items-start">
            <Reply className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                Thread:
              </div>
              <div className="text-sm text-gray-800 dark:text-gray-200 truncate max-w-xs">
                {threadTs}
              </div>
            </div>
          </div>
        )}

        {/* Message content */}
        <div className="mt-4">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Message:</div>
          <div className="whitespace-pre-line text-sm text-gray-800 dark:text-gray-200 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
            {text}
          </div>
        </div>
      </div>
    </div>
  );
}

export { SlackSendMessageDisplay };
