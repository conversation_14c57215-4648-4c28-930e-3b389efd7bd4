import React from 'react';
import { MessageCircle, Quote, Calendar, Heart, Repeat, Share } from 'lucide-react';
import { XIcon } from 'components/icons/lucide-like-social';
import { XSocialPostInput } from 'src/config/nangoModels';

interface XSocialPostDisplayProps {
  parameters: XSocialPostInput;
}

/**
 * Renders a rich display of X (Twitter) post parameters that looks like a real tweet
 */
function XSocialPostDisplay({ parameters }: XSocialPostDisplayProps) {
  // Extract parameters
  const text = parameters.text || '';
  const replyTo = parameters.reply_to || '';
  const quote = parameters.quote || '';

  // Mock user data (in a real implementation, this would come from the XSocialUserProfile)
  const mockUser = {
    name: 'User',
    username: 'username',
    profile_image_url: '',
  };

  // Format current date for the tweet timestamp
  const now = new Date();
  const formattedDate = `${now.toLocaleString('default', { month: 'short' })} ${now.getDate()}`;

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      <div className="p-5 space-y-4">
        {/* Tweet header with user info */}
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            {mockUser.profile_image_url ? (
              <img
                src={mockUser.profile_image_url}
                alt={`${mockUser.name}'s profile`}
                className="w-10 h-10 rounded-full"
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <XIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <span className="font-bold text-gray-900 dark:text-white">{mockUser.name}</span>
              <span className="ml-1 text-gray-500 dark:text-gray-400">@{mockUser.username}</span>
              <span className="mx-1 text-gray-500 dark:text-gray-400">·</span>
              <span className="text-gray-500 dark:text-gray-400 text-sm">{formattedDate}</span>
            </div>

            {/* Main tweet content */}
            <div className="mt-2 text-base text-gray-800 dark:text-gray-200 whitespace-pre-line">
              {text}
            </div>

            {/* Quote tweet if present */}
            {quote && (
              <div className="mt-3 p-3 border border-gray-200 dark:border-gray-700 rounded-md text-sm text-gray-800 dark:text-gray-200 bg-gray-50 dark:bg-gray-900/30">
                {quote}
              </div>
            )}

            {/* Tweet actions */}
            <div className="mt-3 flex justify-between max-w-md">
              <button className="flex items-center text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400">
                <MessageCircle className="w-4 h-4" />
                <span className="ml-1 text-xs">0</span>
              </button>
              <button className="flex items-center text-gray-500 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400">
                <Repeat className="w-4 h-4" />
                <span className="ml-1 text-xs">0</span>
              </button>
              <button className="flex items-center text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400">
                <Heart className="w-4 h-4" />
                <span className="ml-1 text-xs">0</span>
              </button>
              <button className="flex items-center text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400">
                <Share className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Reply to information */}
        {replyTo && (
          <div className="flex items-start mt-2">
            <MessageCircle className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                Replying to:
              </div>
              <div className="text-sm text-gray-800 dark:text-gray-200">{replyTo}</div>
            </div>
          </div>
        )}

        {/* Character count */}
        <div className="text-right text-xs text-gray-500 dark:text-gray-400 pt-2">
          {text.length} / 280 characters
        </div>
      </div>
    </div>
  );
}

export { XSocialPostDisplay };
