import React from 'react';
import { FileText, Database, Link } from 'lucide-react';
import { NotionCreatePageInput } from 'src/config/nangoModels';

interface NotionCreatePageDisplayProps {
  parameters: NotionCreatePageInput;
}

/**
 * Renders a rich display of Notion page creation parameters
 */
function NotionCreatePageDisplay({ parameters }: NotionCreatePageDisplayProps) {
  if (!parameters) {
    return null;
  }

  const { parentId, parentType, properties, children } = parameters;

  // Determine if parent is a database or page
  const isDatabase = parentType === 'database_id' || !parentType;
  const isPage = parentType === 'page_id';

  // Try to extract title from properties if it exists
  let title = 'Untitled Page';
  if (properties && properties.title) {
    // Handle different title property formats
    if (Array.isArray(properties.title) && properties.title.length > 0) {
      // Handle rich text array format
      if (properties.title[0].text && properties.title[0].text.content) {
        title = properties.title[0].text.content;
      } else if (properties.title[0].plain_text) {
        title = properties.title[0].plain_text;
      }
    } else if (properties.title.title && Array.isArray(properties.title.title)) {
      // Handle database property format
      if (properties.title.title.length > 0) {
        if (properties.title.title[0].text && properties.title.title[0].text.content) {
          title = properties.title.title[0].text.content;
        } else if (properties.title.title[0].plain_text) {
          title = properties.title.title[0].plain_text;
        }
      }
    } else if (typeof properties.title === 'string') {
      // Handle simple string format
      title = properties.title;
    }
  }

  // Count properties and children
  const propertyCount = properties ? Object.keys(properties).length : 0;
  const childrenCount = children ? children.length : 0;

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      <div className="p-5 space-y-4">
        {/* Header with icon */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-900 flex items-center justify-center mr-3">
            <FileText className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </div>
          <h3 className="text-base font-medium text-gray-900 dark:text-white">New Notion Page</h3>
        </div>

        {/* Page title */}
        <div className="text-lg font-medium text-gray-900 dark:text-white p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
          {title}
        </div>

        {/* Parent information */}
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            {isDatabase ? (
              <Database className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            ) : (
              <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            )}
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Parent {isDatabase ? 'Database' : 'Page'}:
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">ID: {parentId}</div>
          </div>
        </div>

        {/* Properties summary */}
        <div className="flex items-start mt-4">
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Properties:
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {propertyCount} {propertyCount === 1 ? 'property' : 'properties'} defined
            </div>

            {/* Display first few property keys */}
            {propertyCount > 0 && (
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                Keys: {Object.keys(properties).slice(0, 3).join(', ')}
                {propertyCount > 3 ? ` and ${propertyCount - 3} more...` : ''}
              </div>
            )}
          </div>
        </div>

        {/* Children summary */}
        {childrenCount > 0 && (
          <div className="flex items-start mt-4">
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Content Blocks:
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {childrenCount} {childrenCount === 1 ? 'block' : 'blocks'} defined
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export { NotionCreatePageDisplay };
