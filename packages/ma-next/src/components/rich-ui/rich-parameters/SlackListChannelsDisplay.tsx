import React from 'react';
import { MessageSquare } from 'lucide-react';
import { SlackListChannelsInput } from 'src/config/nangoModels';

interface SlackListChannelsDisplayProps {
  parameters: SlackListChannelsInput;
}

/**
 * Renders a rich display of Slack list channels parameters
 */
function SlackListChannelsDisplay({ parameters }: SlackListChannelsDisplayProps) {
  // Extract parameters
  const types = parameters.types || '';
  const limit = parameters.limit || 0;
  const cursor = parameters.cursor || '';

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      <div className="p-5 space-y-4">
        {/* Header with icon */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3">
            <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 className="text-base font-medium text-gray-900 dark:text-white">
            List Slack Channels
          </h3>
        </div>

        {/* Parameters */}
        <div className="space-y-3">
          {types && (
            <div className="flex items-start">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-32">
                Channel Types:
              </div>
              <div className="text-sm text-gray-800 dark:text-gray-200">{types}</div>
            </div>
          )}

          {limit > 0 && (
            <div className="flex items-start">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-32">
                Limit:
              </div>
              <div className="text-sm text-gray-800 dark:text-gray-200">{limit} channels</div>
            </div>
          )}

          {cursor && (
            <div className="flex items-start">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-32">
                Pagination Cursor:
              </div>
              <div className="text-sm text-gray-800 dark:text-gray-200 truncate max-w-xs">
                {cursor}
              </div>
            </div>
          )}

          {!types && !limit && !cursor && (
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Using default parameters to list all accessible channels
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { SlackListChannelsDisplay };
