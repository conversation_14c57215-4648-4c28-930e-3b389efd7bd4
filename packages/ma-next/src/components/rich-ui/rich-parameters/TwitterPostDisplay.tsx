import { MessageCircle, Quote } from 'lucide-react';
import { XIcon } from 'components/icons/lucide-like-social';
import { TwitterPostInput } from 'src/config/nangoModels';

interface TwitterPostDisplayProps {
  parameters: TwitterPostInput;
}

/**
 * Renders a rich display of Twitter (X) post parameters
 */
function TwitterPostDisplay({ parameters }: TwitterPostDisplayProps) {
  // Extract parameters
  const text = parameters.text || '';
  const replyTo = parameters.reply_to || '';
  const quote = parameters.quote || '';

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      {/* Post content */}
      <div className="p-5 space-y-4">
        {/* Post header with icon */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
            <XIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-base font-medium text-gray-900 dark:text-white">X Post</h3>
        </div>

        {/* Main post content */}
        <div className="text-base text-gray-800 dark:text-gray-200 whitespace-pre-line p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
          {text}
        </div>

        {/* Reply to information */}
        {replyTo && (
          <div className="flex items-start">
            <MessageCircle className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                Replying to:
              </div>
              <div className="text-sm text-gray-800 dark:text-gray-200">{replyTo}</div>
            </div>
          </div>
        )}

        {/* Quote information */}
        {quote && (
          <div className="flex items-start">
            <Quote className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                Quoting:
              </div>
              <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-md text-sm text-gray-800 dark:text-gray-200 bg-gray-50 dark:bg-gray-900/30">
                {quote}
              </div>
            </div>
          </div>
        )}

        {/* Character count */}
        <div className="text-right text-xs text-gray-500 dark:text-gray-400 pt-2">
          {text.length} / 280 characters
        </div>
      </div>
    </div>
  );
}

export { TwitterPostDisplay };
