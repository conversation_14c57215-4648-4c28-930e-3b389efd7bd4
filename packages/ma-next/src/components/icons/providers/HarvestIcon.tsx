interface HarvestIconProps {
  className?: string;
}

function HarvestIcon({ className = 'w-12 h-12' }: HarvestIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 62 62" fill="none" className={className}>
      <g clipPath="url(#harvest-clip)">
        <path
          fillRule="evenodd"
          d="M27.967 36.544V53h-2.719V28.246c0-1.534 1.255-2.719 2.72-2.719h8.089v8.228c0 1.534-1.186 2.789-2.65 2.789h-5.439zM52.303 9v41.211c0 1.534-1.255 2.789-2.72 2.789h-2.65V11.72c0-1.534 1.185-2.719 2.65-2.719h2.72zM41.495 9h2.65v41.211c0 1.534-1.255 2.789-2.65 2.789h-2.72V22.737c0-1.534 1.255-2.719 2.72-2.719V9zM19.808 53h-2.65V11.72c0-1.534 1.185-2.719 2.65-2.719h2.72v30.193c0 1.534-1.255 2.789-2.719 2.789V53zM9 53V11.72C9 10.185 10.255 9 11.72 9h2.65v41.211c0 1.534-1.186 2.789-2.65 2.789H9z"
          fill="#fa5d00"
        />
      </g>
      <defs>
        <clipPath id="harvest-clip">
          <path fill="#fff" transform="translate(9 9)" d="M0 0h43.303v44H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export { HarvestIcon };
