interface SelectFieldProps {
  field: any;
  nodeId: string;
  onSelectChange: (nodeId: string, fieldName: string, value: string, label: string) => void;
  defaultValue?: string;
}

function SelectField({ field, nodeId, onSelectChange, defaultValue = '' }: SelectFieldProps) {
  let options: { value: string; label: string }[] = [];

  try {
    if (typeof field.options === 'string') {
      // This would normally be evaluated by tournament in the backend
      options = [{ value: '', label: 'Options will be populated when executed' }];
    } else if (Array.isArray(field.options)) {
      options = field.options;
    } else if (field.options && typeof field.options === 'object') {
      // Handle the case where options is an object with numeric keys
      const hasNumericKeys = Object.keys(field.options).some(key => !isNaN(Number(key)));

      if (hasNumericKeys) {
        options = Object.values(field.options);
      } else if ('value' in field.options && 'label' in field.options) {
        options = [field.options as { value: string; label: string }];
      } else {
        options = [{ value: '', label: 'Invalid options format' }];
      }
    } else {
      options = [{ value: '', label: 'No options available' }];
    }
  } catch (error) {
    console.error('Error parsing options:', error);
    options = [{ value: '', label: 'Error loading options' }];
  }

  return (
    <select
      className="p-2 border rounded-md dark:bg-gray-800 dark:border-gray-700 inline-block min-w-[200px]"
      onChange={e =>
        onSelectChange(
          nodeId,
          field.name,
          e.target.value,
          options.find(o => o.value == e.target.value)?.label || ''
        )
      }
      defaultValue={defaultValue}
    >
      <option value="" disabled>
        Select an option
      </option>
      {options.map((option, i) => (
        <option key={i} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}

export { SelectField };
