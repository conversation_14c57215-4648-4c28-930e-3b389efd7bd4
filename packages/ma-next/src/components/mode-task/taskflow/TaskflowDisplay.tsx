import { useState } from 'react';
import { TaskflowAgentPanel } from '../../mode-agent/TaskflowAgentPanel';
import { TaskflowFormProvider } from '../../../contexts/TaskflowFormContext';
import { AgentSetup } from '../../mode-agent/agent-setup/AgentSetup';
import { usePrepareAgentSteps } from 'hooks/usePrepareAgentSteps';
import { useTaskflow } from 'hooks/useTaskflow';
import { MiniAgent } from '../../mode-agent/MiniAgent';
import { useChatContext } from 'providers/ChatContext';
import { deriveDisplayNodes } from '../../mode-agent/deriveDisplayNodes';

interface TaskflowDisplayProps {
  taskflowId: string;
}

/**
 * TaskflowDisplay component
 * Displays a taskflow with triggers
 */
function TaskflowDisplay({ taskflowId }: TaskflowDisplayProps) {
  const [advancedOptions, setAdvancedOptions] = useState(false);
  const { taskflow, setTaskflow, error } = useTaskflow(taskflowId);
  const isCurrent = useIsCurrent(taskflowId);

  const setupSteps = taskflow?.schema?.steps || [];
  const { finalSteps, allStepsCompleted } = usePrepareAgentSteps(
    setupSteps,
    advancedOptions,
    taskflow?.schema
  );

  const displayNodes = taskflow ? deriveDisplayNodes(taskflow) : [];

  if (error) {
    return <div className="p-4 text-red-500">There was an issue</div>;
  }

  if (!taskflow) {
    return null;
  }

  const isWorkflow = taskflow.schema?.triggers && taskflow.schema.triggers.length > 0;

  if (!isWorkflow) {
    return null;
  }

  return (
    <TaskflowFormProvider taskflow={taskflow} taskflowId={taskflowId} setTaskflow={setTaskflow}>
      {isCurrent ? (
        <>
          {finalSteps.length > 0 && <AgentSetup steps={finalSteps} />}
          <TaskflowAgentPanel
            taskflow={taskflow}
            setTaskflow={setTaskflow}
            advancedOptions={advancedOptions}
            setAdvancedOptions={setAdvancedOptions}
            setupIsComplete={allStepsCompleted}
            displayNodes={displayNodes}
          />
        </>
      ) : (
        <MiniAgent nodes={displayNodes} />
      )}
    </TaskflowFormProvider>
  );
}

function useIsCurrent(taskflowId: string) {
  const { currentTaskflowId } = useChatContext();
  return taskflowId === currentTaskflowId;
}

export { TaskflowDisplay };
