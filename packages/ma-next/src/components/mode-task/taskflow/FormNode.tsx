import { SelectField } from './SelectField';

interface FormNodeProps {
  nodeId: string;
  hitlParams: any;
  onSelectChange: (nodeId: string, fieldName: string, value: string, label: string) => void;
}

function FormNode({ nodeId, hitlParams, onSelectChange }: FormNodeProps) {
  // Get the output value if it exists in the context
  const nodeOutput = hitlParams.nodeOutput || {};

  // Determine if we should show labels (only if there are multiple fields)
  const fields = Array.isArray(hitlParams.parameters.fields)
    ? hitlParams.parameters.fields
    : Object.values(hitlParams.parameters.fields || {});
  const showLabels = fields.length > 1;

  return (
    <div className="p-4 rounded-lg border bg-blue-50 dark:bg-blue-900/30 border-blue-100 dark:border-blue-800">
      <h3 className="font-medium text-lg mb-4 text-left">
        {hitlParams.parameters.prompt || 'Please make a selection'}
      </h3>

      {/* Get fields from hitlParams */}
      {fields.map((field: any, index: number) => {
        // Only support select fields
        if (field.type === 'select') {
          // Get the default value from the output if it exists
          const defaultValue = nodeOutput[field.name]?.value || '';

          return (
            <div key={index}>
              <SelectField
                field={field}
                nodeId={nodeId}
                onSelectChange={onSelectChange}
                defaultValue={defaultValue}
                // showLabel={showLabels}
              />
            </div>
          );
        }

        return null; // Skip non-select fields
      })}
    </div>
  );
}

export { FormNode };
