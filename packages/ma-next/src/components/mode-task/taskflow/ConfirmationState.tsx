import { Loader2 } from 'lucide-react';
import { ConfirmationState as ConfirmationStateType } from '../../../hooks/useTaskflowExecution';

interface ConfirmationStateProps {
  state: ConfirmationStateType;
  nodeId: string;
  onConfirm: (nodeId: string, confirmed: boolean) => void;
}

function ConfirmationStateComponent({ state, nodeId, onConfirm }: ConfirmationStateProps) {
  if (state === 'idle') {
    return (
      <div className="flex justify-end space-x-4">
        <button
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium dark:border-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          onClick={() => onConfirm(nodeId, false)}
        >
          Cancel
        </button>
        <button
          className="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-500"
          onClick={() => onConfirm(nodeId, true)}
        >
          Confirm
        </button>
      </div>
    );
  }

  if (state === 'executing') {
    return (
      <div className="flex justify-end items-center">
        <Loader2 className="h-5 w-5 animate-spin text-indigo-600 mr-2" />
        <span className="text-sm text-gray-600 dark:text-gray-400">Executing...</span>
      </div>
    );
  }

  if (state === 'success') {
    return (
      <div className="flex justify-end items-center text-green-600 dark:text-green-400">
        <span className="text-sm">Success</span>
      </div>
    );
  }

  if (state === 'error') {
    return (
      <div className="flex justify-end items-center text-red-600 dark:text-red-400">
        <span className="text-sm">Error</span>
      </div>
    );
  }

  if (state === 'cancelled') {
    return (
      <div className="flex justify-end items-center text-gray-600 dark:text-gray-400">
        <span className="text-sm">Cancelled</span>
      </div>
    );
  }

  return null;
}

export { ConfirmationStateComponent };
