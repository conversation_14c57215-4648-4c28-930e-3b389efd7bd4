import { ConfirmationStateComponent } from './ConfirmationState';
import { ConfirmationState } from '../../../hooks/useTaskflowExecution';

interface ConfirmationNodeProps {
  nodeId: string;
  hitlParams: any;
  nodeType: string;
  confirmationState: ConfirmationState;
  onConfirm: (nodeId: string, confirmed: boolean) => void;
}

function ConfirmationNode({
  nodeId,
  hitlParams,
  nodeType,
  confirmationState,
  onConfirm,
}: ConfirmationNodeProps) {
  return (
    <div className="p-4 rounded-lg border bg-blue-50 dark:bg-blue-900/30 border-blue-100 dark:border-blue-800">
      <h3 className="font-medium text-lg mb-4 text-left">
        {nodeType
          .split('.')
          .slice(-2)
          .reverse()
          .join(': ')
          .replace(/-/g, ' ')
          .replace(/_/g, ' ')
          .replace(/\b\w/g, l => l.toUpperCase()) || 'Confirmation'}
      </h3>
      {/* Display parameters in a table */}
      {hitlParams.parameters && (
        <div className="mb-4 overflow-hidden border border-gray-200 dark:border-gray-700 rounded-md">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {hitlParams.parameters &&
                Object.entries(hitlParams.parameters).map(([key, value]: [string, any]) => {
                  // Get the field config from confirmationConfig if available
                  const fieldConfig = hitlParams.confirmationConfig?.[key];
                  // Get the key label (parameter name)
                  const keyLabel = fieldConfig?.keyLabel || key;
                  // Get the value label (display value)
                  // If valueLabel is explicitly set (even to empty string), use it
                  // Otherwise, try to get a sensible default
                  const valueLabel =
                    fieldConfig?.valueLabel !== undefined
                      ? fieldConfig.valueLabel
                      : typeof value === 'object'
                        ? value.label || JSON.stringify(value)
                        : String(value);

                  return (
                    <tr key={key}>
                      <td className="px-4 py-2 text-sm font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        {keyLabel}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                        {valueLabel}
                      </td>
                    </tr>
                  );
                })}
            </tbody>
          </table>
        </div>
      )}
      <ConfirmationStateComponent state={confirmationState} nodeId={nodeId} onConfirm={onConfirm} />
    </div>
  );
}

export { ConfirmationNode };
