import { useState, useEffect } from 'react';
import { useTaskflowExecution } from '../../../hooks/useTaskflowExecution';
import { FormNode } from './FormNode';
import { ConfirmationNode } from './ConfirmationNode';
import { ExecutionSetupStepsSection } from './ExecutionSetupStepsSection';
import { XCircle } from 'lucide-react';

interface TaskflowExecutionProps {
  executionId: string;
}

/**
 * TaskflowExecution component
 * Renders a taskflow execution with form and confirmation nodes
 */
function TaskflowExecution({ executionId }: TaskflowExecutionProps) {
  const { execution, error, confirmationStates, handleSelectChange, handleConfirmation } =
    useTaskflowExecution(executionId);
  const [setupCompleted, setSetupCompleted] = useState(false);

  // Check if setup is already completed based on execution context
  useEffect(() => {
    if (execution?.context?.setup?.started) {
      setSetupCompleted(true);
    }
  }, [execution]);

  if (error) {
    return (
      <div className="p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md text-red-600 dark:text-red-300">
        {error}
      </div>
    );
  }

  if (!execution) {
    return null;
  }

  // Check if this execution has setup steps in the context
  const setupNode = execution.context?.setup;
  const hasSetupSteps = setupNode && setupNode.steps && setupNode.steps.length > 0;

  // If execution has no context yet, return null
  if (!execution.context) {
    return null;
  }

  // Render all nodes with HITL parameters or errors
  return (
    <div className="space-y-6">
      {/* Render setup steps if they exist */}
      {hasSetupSteps && setupNode?.steps && (
        <ExecutionSetupStepsSection
          executionId={executionId}
          execution={execution}
          steps={setupNode.steps}
          onSetupComplete={() => setSetupCompleted(true)}
          isCompleted={setupCompleted}
        />
      )}

      {Object.entries(execution.context).map(([nodeId, nodeData]: [string, any]) => {
        // Render error state if present
        if (nodeData.status === 'ERROR' && nodeData.error) {
          return (
            <div
              key={nodeId}
              className="p-4 rounded-lg border bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-800"
            >
              <div className="flex items-center mb-2">
                <XCircle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
                <h3 className="font-medium text-lg text-red-600 dark:text-red-400">Error</h3>
              </div>
              <p className="text-red-600 dark:text-red-400">{nodeData.error}</p>
            </div>
          );
        }

        // Skip nodes without hitl parameters or output
        if (!nodeData.hitl && !nodeData.output) return null;

        const hitlParams = nodeData.hitl;
        if (!hitlParams) return null;

        // Render form
        if (hitlParams.type === 'form') {
          return (
            <FormNode
              key={nodeId}
              nodeId={nodeId}
              hitlParams={{ ...hitlParams, nodeOutput: nodeData.output || {} }}
              onSelectChange={handleSelectChange}
            />
          );
        }

        // Render confirmation
        if (hitlParams.type === 'confirmation') {
          console.log(`[TaskflowExecution] Rendering confirmation node ${nodeId}:`, {
            nodeData,
            hitlParams,
            confirmationState: confirmationStates[nodeId] || 'idle',
          });

          return (
            <ConfirmationNode
              key={nodeId}
              nodeId={nodeId}
              nodeType={nodeData.type}
              hitlParams={hitlParams}
              confirmationState={confirmationStates[nodeId] || 'idle'}
              onConfirm={handleConfirmation}
            />
          );
        }

        return null;
      })}
    </div>
  );
}

export { TaskflowExecution };
