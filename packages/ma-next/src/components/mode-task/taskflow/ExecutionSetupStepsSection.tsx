import { useState, useEffect } from 'react';
import { useConnections } from 'hooks/useConnections';
import { Button } from 'components/ui/button';
import { PROVIDER_CONFIGS } from 'src/config/integrationUI';
import { ConnectionModal } from 'components/modals/ConnectionModal/ConnectionModal';
import { useModal } from 'hooks/useModal';
import { Play } from 'lucide-react';
import { resumeTaskflowExecution } from 'lib/taskflow';
import { supabase } from 'lib/supabase';
import { TaskflowExecutionData } from 'hooks/useTaskflowExecution';

interface ExecutionSetupStep {
  type: string;
  provider?: string;
  completed?: boolean;
}

interface ExecutionSetupStepsSectionProps {
  executionId: string;
  execution: TaskflowExecutionData | null;
  steps: ExecutionSetupStep[];
  onSetupComplete: () => void;
  isCompleted?: boolean;
}

/**
 * ExecutionSetupStepsSection component
 * Displays setup steps for a taskflow execution and allows the user to complete them
 */
function ExecutionSetupStepsSection({
  executionId,
  execution,
  steps,
  onSetupComplete,
  isCompleted = false,
}: ExecutionSetupStepsSectionProps) {
  const [setupSteps, setExecutionSetupSteps] = useState<ExecutionSetupStep[]>(steps);
  const { connections } = useConnections();
  const { openModal, closeModal, isOpen } = useModal();
  const [isStarting, setIsStarting] = useState(false);
  const [setupCompleted, setSetupCompleted] = useState(isCompleted);

  // Initialize setupCompleted based on execution status
  useEffect(() => {
    if (isCompleted && !setupCompleted) {
      setSetupCompleted(true);
      return;
    }

    // Check if execution has started or has any nodes with status other than PAUSED
    if (execution) {
      // Check if setup node is marked as started
      if (execution.context?.setup?.started) {
        setSetupCompleted(true);
        return;
      }

      // Check if any node has been executed (has status other than PAUSED or undefined)
      const hasStarted = Object.values(execution.context || {}).some(
        (node: any) => node.status && node.status !== 'PAUSED'
      );

      if (hasStarted && !setupCompleted) {
        setSetupCompleted(true);
      }
    }
  }, [isCompleted, execution, setupCompleted]);

  // Update step completion status based on connections
  useEffect(() => {
    if (!connections.length) return;

    const updatedSteps = setupSteps.map(step => {
      if (step.type === 'connectProvider' && step.provider) {
        const isConnected = connections.some(conn => conn.providerKey === step.provider);
        return { ...step, completed: isConnected };
      }
      return step;
    });

    setExecutionSetupSteps(updatedSteps);
  }, [connections]);

  // Check if all steps are completed
  const allStepsCompleted = setupSteps.every(step => step.completed);

  // Handle starting the execution
  const handleStartExecution = async () => {
    if (!allStepsCompleted || isStarting || setupCompleted) return;

    setIsStarting(true);
    try {
      // Get the current context first
      const { data: executionData } = await supabase
        .from('taskflow_executions')
        .select('context')
        .eq('id', executionId)
        .single();

      // Merge the setup node with the existing context
      const updatedContext = {
        ...(executionData?.context || {}),
        setup: {
          type: 'setup',
          status: 'SUCCESS',
          steps: setupSteps,
          started: true,
        },
      };

      // Update the execution context to mark setup as started
      await supabase
        .from('taskflow_executions')
        .update({
          context: updatedContext,
        })
        .eq('id', executionId);

      // Then resume the taskflow execution with the first node
      await resumeTaskflowExecution(executionId, 'node1', {}, true);

      // Mark as completed in the UI
      setSetupCompleted(true);
      onSetupComplete();
    } catch (error) {
      console.error('Error starting execution:', error);
    } finally {
      setIsStarting(false);
    }
  };

  // Open connection modal for a provider
  const openConnect = (provider: string) => {
    openModal(`connection-${provider}`);
  };

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-lg mb-2">Setup Required</h3>
      {/* Setup steps box */}
      <div className="p-4 rounded-lg border bg-blue-50 dark:bg-blue-900/30 border-blue-100 dark:border-blue-800">
        <div className="space-y-3">
          {setupSteps.map((step, index) => {
            if (step.type === 'connectProvider' && step.provider) {
              const providerConfig =
                PROVIDER_CONFIGS[step.provider as keyof typeof PROVIDER_CONFIGS];
              if (!providerConfig) return null;

              const Icon = providerConfig.icon;
              const isConnected = step.completed;

              return (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div
                      className={`p-1.5 rounded-md ${isConnected ? 'bg-green-100 dark:bg-green-900/30' : 'bg-white dark:bg-gray-800'}`}
                    >
                      <Icon
                        className={`w-5 h-5 ${isConnected ? 'text-green-600 dark:text-green-400' : ''}`}
                      />
                    </div>
                    <span className={isConnected ? 'text-green-600 dark:text-green-400' : ''}>
                      {isConnected
                        ? `Connected to ${providerConfig.name}`
                        : `Connect ${providerConfig.name}`}
                    </span>
                  </div>
                  {!isConnected && (
                    <Button size="sm" onClick={() => openConnect(step.provider!)} variant="primary">
                      Connect
                    </Button>
                  )}
                  {/* Connection Modal */}
                  <ConnectionModal
                    isOpen={isOpen(`connection-${step.provider}`)}
                    onClose={() => closeModal()}
                    provider={step.provider}
                    requiredSyncs={step.syncScopes as Record<string, any> | undefined}
                    onConnected={async () => {
                      closeModal();
                      // Update step completion status in local state
                      const updatedSteps = setupSteps.map((s, i) =>
                        i === index ? { ...s, completed: true } : s
                      );
                      setExecutionSetupSteps(updatedSteps);

                      // Get the current context first
                      const { data: executionData } = await supabase
                        .from('taskflow_executions')
                        .select('context')
                        .eq('id', executionId)
                        .single();

                      // Merge the setup node with the existing context
                      const updatedContext = {
                        ...(executionData?.context || {}),
                        setup: {
                          type: 'setup',
                          status: 'PAUSED',
                          steps: updatedSteps,
                          started: false,
                        },
                      };

                      // Update the execution context in the database
                      await supabase
                        .from('taskflow_executions')
                        .update({
                          context: updatedContext,
                        })
                        .eq('id', executionId);
                    }}
                    onDisconnected={async () => {
                      closeModal();
                      // Update step completion status in local state
                      const updatedSteps = setupSteps.map((s, i) =>
                        i === index ? { ...s, completed: false } : s
                      );
                      setExecutionSetupSteps(updatedSteps);

                      // Get the current context first
                      const { data: executionData } = await supabase
                        .from('taskflow_executions')
                        .select('context')
                        .eq('id', executionId)
                        .single();

                      // Merge the setup node with the existing context
                      const updatedContext = {
                        ...(executionData?.context || {}),
                        setup: {
                          type: 'setup',
                          status: 'PAUSED',
                          steps: updatedSteps,
                          started: false,
                        },
                      };

                      // Update the execution context in the database
                      await supabase
                        .from('taskflow_executions')
                        .update({
                          context: updatedContext,
                        })
                        .eq('id', executionId);
                    }}
                  />
                </div>
              );
            }
            return null;
          })}
        </div>
      </div>
      {/* Continue button - always visible but disabled after clicking */}
      <div className="flex justify-end">
        <Button
          onClick={handleStartExecution}
          disabled={!allStepsCompleted || isStarting || setupCompleted}
          variant="success"
          className="flex items-center gap-2"
        >
          {isStarting ? (
            <>
              <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
              Starting...
            </>
          ) : setupCompleted ? (
            <>
              <Play className="h-4 w-4" />
              Continued
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              Continue
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

export { ExecutionSetupStepsSection };
