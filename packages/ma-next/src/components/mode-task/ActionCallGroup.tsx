import { useState } from 'react';
import { ActionCallGroupInvocation, ToolInvocationPart } from 'chat/protocol/ui';
import { useChatContext } from 'providers/ChatContext';
import { useConnections } from 'hooks/useConnections';
import { useAuth } from 'hooks/useAuth';
import { ConnectProviderForAction } from 'components/setup/ConnectProviderForAction';
import { AuthForAction } from 'components/setup/AuthForAction';
import { ActionCard } from './ActionCard';
import clsx from 'clsx';

type ActionCallGroupProps = {
  part: ToolInvocationPart & { content: ActionCallGroupInvocation };
  isLatestMessage: boolean;
};

const ActionCallGroup = ({ part, isLatestMessage }: ActionCallGroupProps) => {
  const { invocations } = part.content;
  const [isConfirming, setIsConfirming] = useState(false);
  const { append } = useChatContext();
  const { connectionsByProviderKey } = useConnections();
  const { isAuthenticated } = useAuth();

  // Get unique provider keys that require connections
  const requiredProviders = [
    ...new Set(
      invocations
        .filter(invocation => invocation.connectionRequired)
        .map(invocation => invocation.args.providerKey)
    ),
  ];

  // Check if all required connections are available
  const hasAllRequiredConnections = requiredProviders.every(
    providerKey => connectionsByProviderKey[providerKey]
  );

  // Check if any invocation requires a connection
  const requiresConnection = requiredProviders.length > 0;

  // Check if authentication is required
  const requiresAuth = requiresConnection && !isAuthenticated;

  // Check if all actions in the group have results or are cancelled
  const allComplete = invocations.every(invocation => !!invocation.result || !isLatestMessage);

  // If all actions are complete, just render them individually
  if (allComplete) {
    return (
      <div className="my-4">
        {invocations.map((invocation, index) => (
          <ActionCard
            key={index}
            toolCallId={invocation.toolCallId}
            actionConfig={invocation.args}
            result={invocation.result as any}
            isCancelled={!isLatestMessage && !invocation.result}
          />
        ))}
      </div>
    );
  }

  return (
    <>
      {/* Show auth requirement if needed */}
      {requiresAuth && (
        <div className="mb-6">
          <AuthForAction id={`group-${invocations[0].toolCallId}`} />
        </div>
      )}

      {/* Show connection requirements if needed */}
      {requiresConnection && !hasAllRequiredConnections && isAuthenticated && (
        <div className="mb-6">
          {requiredProviders.map(
            providerKey =>
              !connectionsByProviderKey[providerKey] && (
                <ConnectProviderForAction
                  key={providerKey}
                  providerKey={providerKey}
                  id={`group-${invocations[0].toolCallId}`}
                />
              )
          )}
        </div>
      )}
      <div className="my-6 bg-white/90 dark:bg-gray-800/90 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3
          className={clsx('text-lg font-bold text-gray-700 dark:text-gray-300 mb-4', {
            'opacity-50': requiresAuth || (requiresConnection && !hasAllRequiredConnections),
          })}
        >
          Confirm Multiple Actions
        </h3>

        <div className="space-y-4 mb-6">
          {invocations.map((invocation, index) => (
            <ActionCard
              key={index}
              toolCallId={invocation.toolCallId}
              actionConfig={invocation.args}
              hideHeader={index > 0}
              disabled={requiresAuth || (requiresConnection && !hasAllRequiredConnections)}
              result={invocation.result as any}
              hideButtons={true}
              bulk={true}
            />
          ))}
        </div>

        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            disabled={isConfirming}
            onClick={() => {
              const confirmedTools = invocations.reduce(
                (acc, invocation) => ({ ...acc, [invocation.toolCallId]: false }),
                {}
              );
              append({ confirmedTools });
            }}
            className={clsx(
              'px-4 py-2 rounded-lg text-sm font-medium border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700',
              isConfirming && 'opacity-50 cursor-not-allowed hover:bg-transparent'
            )}
          >
            Cancel All
          </button>
          <button
            disabled={
              isConfirming || requiresAuth || (requiresConnection && !hasAllRequiredConnections)
            }
            onClick={() => {
              setIsConfirming(true);
              const confirmedTools = invocations.reduce(
                (acc, invocation) => ({ ...acc, [invocation.toolCallId]: true }),
                {}
              );
              append({ confirmedTools }).finally(() => {
                setIsConfirming(false);
              });
            }}
            className={clsx(
              'px-4 py-2 rounded-lg text-sm font-medium bg-indigo-600 hover:bg-indigo-500 text-white',
              (isConfirming ||
                requiresAuth ||
                (requiresConnection && !hasAllRequiredConnections)) &&
                'opacity-50 cursor-not-allowed hover:bg-indigo-600'
            )}
          >
            {isConfirming ? 'Working...' : 'Confirm All'}
          </button>
        </div>
      </div>
    </>
  );
};

export { ActionCallGroup };
