import { ChevronDown, ChevronUp } from 'lucide-react';
import { FormatParameters } from './FormatParameters';
import { RichParameterDisplay } from '../rich-ui/RichParameterDisplay';

interface ActionParametersProps {
  actionParameters: Record<string, any> | undefined;
  isComplete: boolean;
  showParameters: boolean;
  setShowParameters: (show: boolean) => void;
  providerKey?: string;
  actionKey?: string;
}

function ActionParameters({
  actionParameters,
  isComplete,
  showParameters,
  setShowParameters,
  providerKey,
  actionKey,
}: ActionParametersProps) {
  if (!actionParameters || Object.keys(actionParameters).length === 0) {
    return null;
  }

  // Check if we have a rich display available for this provider/action
  const hasRichDisplay =
    providerKey && actionKey && RichParameterDisplay.canDisplay(providerKey, actionKey);

  // If we have a rich display, use it instead of the regular parameters display
  if (hasRichDisplay && !isComplete) {
    return (
      <div className="mt-5">
        <RichParameterDisplay
          providerKey={providerKey!}
          actionKey={actionKey!}
          actionParameters={actionParameters}
        />
      </div>
    );
  }

  // Otherwise, use the regular parameters display
  return (
    <div className="mt-5">
      {isComplete ? (
        showParameters ? (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
            <button
              onClick={() => setShowParameters(false)}
              className="w-full px-5 py-3 border-b border-gray-200 dark:border-gray-600 text-left flex justify-between items-center"
            >
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Parameters</h4>
              <ChevronUp className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>
            <div className="p-5">
              <FormatParameters
                actionParameters={actionParameters}
                providerKey={providerKey}
                actionKey={actionKey}
              />
            </div>
          </div>
        ) : (
          <button
            onClick={() => setShowParameters(true)}
            className="flex items-center justify-between w-full p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <span>Parameters</span>
            <ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          </button>
        )
      ) : (
        <div className="p-5 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
          <FormatParameters
            actionParameters={actionParameters}
            providerKey={providerKey}
            actionKey={actionKey}
          />
        </div>
      )}
    </div>
  );
}

export { ActionParameters };
