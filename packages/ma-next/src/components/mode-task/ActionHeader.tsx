import { PROVIDER_CONFIGS } from 'src/config/integrationUI';

interface ActionHeaderProps {
  hideHeader?: boolean;
  providerInfo: (typeof PROVIDER_CONFIGS)[keyof typeof PROVIDER_CONFIGS];
  actionDisplayName: string;
  actionKey: string;
  autoApproved?: boolean;
}

function ActionHeader({
  hideHeader,
  providerInfo,
  actionDisplayName,
  actionKey,
  autoApproved,
}: ActionHeaderProps) {
  const ProviderIcon = providerInfo.icon || null;

  return (
    <>
      {!hideHeader && (
        <h3 className="text-md font-bold text-gray-700 dark:text-gray-300 mb-2">Action</h3>
      )}
      <div className="flex items-center mb-5">
        <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-4">
          <ProviderIcon className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
        </div>
        <div>
          <div className="flex items-center">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {providerInfo.name} - {actionDisplayName}
            </h4>
            {autoApproved && (
              <span className="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-full">
                Auto-confirmed
              </span>
            )}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {actionKey === 'send-email' ? 'Send an email via Gmail' : 'Execute action'}
          </p>
        </div>
      </div>
    </>
  );
}

export { ActionHeader };
