import { useState } from 'react';
import { ActionCallArgs } from 'chat/protocol';
import { Maximize2, Minimize2 } from 'lucide-react';
import { ActionCard } from './ActionCard';
import clsx from 'clsx';

type MiniActionCallCardProps = {
  type: 'success' | 'working' | 'cancelled' | 'error' | 'auto-approved';
  args: ActionCallArgs;
  toolCallId?: string;
  result?: any;
  isLatestMessage?: boolean;
  customText?: string;
  autoApproved?: boolean;
};

const MiniActionCallCard = ({
  type,
  args,
  toolCallId,
  result,
  customText,
  autoApproved,
}: MiniActionCallCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Format the action key for display
  const formattedActionKey = args?.actionKey
    ?.split('-')
    .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  // If expanded, show the full ActionCard
  if (isExpanded && toolCallId) {
    return (
      <div className="relative">
        <button
          onClick={() => setIsExpanded(false)}
          className="absolute right-2 top-2 p-1 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 z-10"
        >
          <Minimize2 className="w-4 h-4 text-gray-500 dark:text-gray-400" />
        </button>
        <ActionCard
          toolCallId={toolCallId}
          actionConfig={args}
          result={result}
          isCancelled={type === 'cancelled'}
          autoApproved={autoApproved || type === 'auto-approved'}
        />
      </div>
    );
  }

  // Get the appropriate styles based on the type
  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          border: 'border-green-100 dark:border-green-800',
          bg: 'bg-green-50 dark:bg-green-900/20',
          text: 'text-green-700 dark:text-green-400',
        };
      case 'auto-approved':
        return {
          border: 'border-green-100 dark:border-green-800',
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          text: 'text-green-700 dark:text-green-400',
        };
      case 'error':
        return {
          border: 'border-red-100 dark:border-red-800',
          bg: 'bg-red-50 dark:bg-red-900/20',
          text: 'text-red-700 dark:text-red-400',
        };
      case 'cancelled':
        return {
          border: 'border-gray-100 dark:border-gray-700',
          bg: 'bg-gray-50 dark:bg-gray-800',
          text: 'text-gray-700 dark:text-gray-400',
        };
      case 'working':
      default:
        return {
          border: 'border-gray-100 dark:border-gray-700',
          bg: 'bg-gray-50 dark:bg-gray-800',
          text: 'text-gray-600 dark:text-gray-300',
        };
    }
  };

  const styles = getTypeStyles();

  // Get the status text based on the type
  const getStatusText = () => {
    if (customText) return customText;

    switch (type) {
      case 'success':
      case 'auto-approved':
        return formattedActionKey;
      case 'error':
        // Try to extract error message from result
        let errorMessage =
          result?.error.message ||
          result?.error ||
          (result?.data && typeof result.data === 'object' && 'error' in result.data
            ? result.data.error?.message
            : 'Error');

        // Truncate long error messages
        if (typeof errorMessage === 'string' && errorMessage.length > 50) {
          errorMessage = errorMessage.substring(0, 47) + '...';
        }

        return `${formattedActionKey}: ${errorMessage}`;
      case 'cancelled':
        return `${formattedActionKey}: Cancelled`;
      case 'working':
      default:
        return args?.userExplanation || `${formattedActionKey}: Working...`;
    }
  };

  // Show expand button only for success, auto-approved, and error states when we have a toolCallId
  const showExpandButton =
    (type === 'success' || type === 'error' || type === 'auto-approved') && toolCallId;

  return (
    <div
      className={clsx(
        'p-3 my-4 border rounded-md',
        styles.border,
        styles.bg,
        styles.text,
        isHovered && showExpandButton && 'shadow-sm'
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-center justify-between gap-2">
        <p className={clsx('text-sm')}>{getStatusText()}</p>
        {showExpandButton && isHovered && (
          <button
            onClick={() => setIsExpanded(true)}
            className="p-1 -m-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
          >
            <Maximize2 className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          </button>
        )}
      </div>
    </div>
  );
};

export { MiniActionCallCard };
