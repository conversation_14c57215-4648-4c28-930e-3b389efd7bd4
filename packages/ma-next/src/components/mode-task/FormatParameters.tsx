import { RichParameterDisplay } from '../rich-ui/RichParameterDisplay';

interface FormatParametersProps {
  actionParameters: Record<string, any>;
  providerKey?: string;
  actionKey?: string;
}

const FormatParameters = ({ actionParameters, providerKey, actionKey }: FormatParametersProps) => {
  const entries = Object.entries(actionParameters);

  const contentFields: [string, any][] = [];
  const regularFields: [string, any][] = [];

  entries.forEach(([key, value]) => {
    if (
      (typeof value === 'string' && value.length > 80) ||
      ['body', 'content', 'message', 'text', 'description'].some(field =>
        key.toLowerCase().includes(field)
      )
    ) {
      contentFields.push([key, value]);
    } else {
      regularFields.push([key, value]);
    }
  });

  // Check if we should show rich parameter display
  const shouldShowRichDisplay = providerKey && actionKey;

  // If we have a rich display available, use it instead of the regular display
  if (shouldShowRichDisplay) {
    const hasRichDisplay = RichParameterDisplay.canDisplay(providerKey!, actionKey!);

    if (hasRichDisplay) {
      return (
        <RichParameterDisplay
          providerKey={providerKey!}
          actionKey={actionKey!}
          actionParameters={actionParameters}
        />
      );
    }
  }

  // Otherwise, show the regular parameter display
  return (
    <div className="space-y-4">
      {/* Regular fields in a table */}
      {regularFields.length > 0 && (
        <table className="w-full border-collapse">
          <tbody>
            {regularFields.map(([key, value]) => (
              <tr key={key}>
                <td className="py-[13px] pr-4 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/3">
                  {formatFieldName(key)}
                </td>
                <td className="py-3 text-sm text-gray-800 dark:text-gray-200">
                  {formatFieldValue(value, key)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {/* Content fields displayed separately */}
      {contentFields.map(([key, value]) => (
        <div key={key} className="pt-2">
          {contentFields.length > 1 && (
            <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {formatFieldName(key)}
            </h5>
          )}
          <div className="text-sm text-gray-800 dark:text-gray-200">
            {formatFieldValue(value, key)}
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Formats a field name for display
 */
function formatFieldName(key: string): string {
  return key
    .split(/[-_]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

function formatFieldValue(value: any, fieldName: string): React.ReactNode {
  if (value === null || value === undefined) {
    return <span className="text-gray-400 dark:text-gray-500">None</span>;
  }

  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }

  if (typeof value === 'string') {
    const contentFields = ['body', 'content', 'message', 'text', 'description'];
    if (contentFields.some(field => fieldName.toLowerCase().includes(field)) && value.length > 80) {
      return (
        <>
          <div className="text-sm text-gray-800 dark:text-gray-200 mb-2 -mt-2">
            {formatFieldName(fieldName)}
          </div>
          <div className="bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-700 whitespace-pre-line">
            {value}
          </div>
        </>
      );
    }
    return value;
  }

  if (typeof value === 'object') {
    if (Array.isArray(value)) {
      if (value.length === 0)
        return <span className="text-gray-400 dark:text-gray-500">Empty list</span>;
      return (
        <ul className="list-none">
          {value.map((item: any, i: any) => (
            <li key={i} className="first:mt-0 mt-1">
              {typeof item === 'object' ? JSON.stringify(item) : String(item)}
            </li>
          ))}
        </ul>
      );
    }
    return JSON.stringify(value, null, 2);
  }

  return String(value);
}

export { FormatParameters };
