import { Check } from 'lucide-react';
import clsx from 'clsx';
import { useChatContext } from 'providers/ChatContext';
import { useState } from 'react';
import { useProfile } from 'hooks/useProfile';

interface ActionButtonsProps {
  toolCallId: string;
  disabled?: boolean;
  isComplete: boolean;
  providerKey: string;
  actionKey: string;
  autoApproved?: boolean;
}

function ActionButtons({
  toolCallId,
  disabled,
  isComplete,
  providerKey,
  actionKey,
  autoApproved,
}: ActionButtonsProps) {
  const { profile, updateProfile } = useProfile();
  const [isConfirming, setIsConfirming] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const { append } = useChatContext();

  const autoApprovalKey = `${providerKey}:${actionKey}`;
  const autoApprovals = profile?.preferences?.autoApprovals || [];
  const autoApprove = autoApprovals.includes(autoApprovalKey);

  const handleAutoApproveToggle = () => {
    if (!profile) return;

    updateProfile({
      preferences: {
        ...profile.preferences,
        autoApprovals: autoApprove
          ? autoApprovals.filter(key => key !== autoApprovalKey)
          : [...autoApprovals, autoApprovalKey],
      },
    });
  };

  return (
    <div
      className={clsx(
        'flex justify-between items-center',
        isComplete ? 'mt-3' : 'mt-6' // Less padding when complete
      )}
    >
      {/* Auto-approve checkbox - only show when not complete */}
      {(!isComplete || autoApproved) && (
        <div className="flex items-center">
          <button
            onClick={handleAutoApproveToggle}
            disabled={disabled || isConfirming || isCancelling}
            className={clsx(
              'flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400',
              (disabled || isConfirming || isCancelling) && 'opacity-50 cursor-not-allowed'
            )}
          >
            <div
              className={clsx(
                'w-5 h-5 rounded-md mr-2 flex items-center justify-center',
                autoApprove ? 'bg-indigo-600' : 'border border-gray-300 dark:border-gray-600',
                disabled && 'opacity-50'
              )}
            >
              {autoApprove && <Check className="w-3 h-3 text-white" />}
            </div>
            Auto-confirm in the future
          </button>
        </div>
      )}

      {/* Action buttons on the right */}
      {!isComplete && (
        <div className="flex space-x-3">
          <button
            disabled={disabled || isConfirming || isCancelling}
            onClick={() => {
              setIsCancelling(true);
              append({
                confirmedTools: { [toolCallId]: false },
              }).finally(() => {
                setIsCancelling(false);
              });
            }}
            className={clsx(
              'px-4 py-2 rounded-lg text-sm font-medium border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700',
              (disabled || isConfirming || isCancelling) &&
                'opacity-50 cursor-not-allowed hover:bg-transparent'
            )}
          >
            {isCancelling ? 'Cancelling...' : 'Cancel'}
          </button>
          <button
            disabled={disabled || isConfirming || isCancelling}
            onClick={() => {
              setIsConfirming(true);
              append({
                confirmedTools: { [toolCallId]: true },
              }).finally(() => {
                setIsConfirming(false);
              });
            }}
            className={clsx(
              'px-4 py-2 rounded-lg text-sm font-medium bg-indigo-600 hover:bg-indigo-500 text-white',
              (disabled || isConfirming || isCancelling) &&
                'opacity-50 cursor-not-allowed hover:bg-indigo-600'
            )}
          >
            {isConfirming ? 'Working...' : 'Confirm'}
          </button>
        </div>
      )}
    </div>
  );
}

export { ActionButtons };
