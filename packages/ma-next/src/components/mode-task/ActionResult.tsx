import { Check, ChevronDown, ChevronUp } from 'lucide-react';
import clsx from 'clsx';
import { ActionCallResult } from 'chat/protocol/tools';
import { useState } from 'react';
import React from 'react';
import { FormatParameters } from './FormatParameters';

interface ActionResultProps {
  result?: ActionCallResult;
  isCancelled?: boolean;
  actionParameters?: Record<string, any>;
  providerKey?: string;
  actionKey?: string;
}

function ActionResult({ result, isCancelled, actionParameters }: ActionResultProps) {
  const [showResultSection, setShowResultSection] = useState(false);
  const [showResultDetails, setShowResultDetails] = useState(false);
  const [showParameters, setShowParameters] = useState(false);

  if (!result && !isCancelled) {
    return null;
  }

  return (
    <div className="mt-4">
      {React.createElement(
        'button',
        {
          onClick: () => {
            setShowResultSection(!showResultSection);
            if (!showResultSection) {
              setShowResultDetails(true);
              setShowParameters(Boolean(isCancelled || result?.error));
            }
          },
          className: clsx(
            'flex items-center justify-between w-full p-3 rounded-lg text-sm font-medium transition-colors',
            {
              'bg-gray-50 border border-gray-200 dark:bg-gray-700/20 dark:border-gray-600 text-gray-700 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700/30':
                isCancelled,
              'bg-green-50 border border-green-100 dark:bg-green-900/20 dark:border-green-800 text-green-700 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30':
                result?.success,
              'bg-red-50 border border-red-100 dark:bg-red-900/20 dark:border-red-800 text-red-700 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30':
                !isCancelled && !result?.success,
            }
          ),
        },
        <>
          <div className="flex items-center gap-2">
            {isCancelled ? (
              <span className="font-medium">Cancelled:</span>
            ) : result?.success ? (
              <Check className="w-4 h-4" />
            ) : (
              <span className="font-medium">Error:</span>
            )}
            <span>
              {isCancelled
                ? 'Action was cancelled'
                : result?.success
                  ? 'Action completed successfully'
                  : // @ts-expect-error
                    result?.data?.error?.message || 'Action failed'}
            </span>
          </div>
          {showResultSection ? (
            <ChevronUp className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          )}
        </>
      )}
      <div className={clsx({ hidden: !showResultSection })}>
        {/* Display data if available with toggle */}
        <div className="mt-2 space-y-2">
          {/* Response data section - only for non-cancelled actions */}
          {!isCancelled && result !== undefined && (
            <div>
              <button
                onClick={() => setShowResultDetails(!showResultDetails)}
                className="flex items-center justify-between w-full p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700"
              >
                <span>Response Data</span>
                {showResultDetails ? (
                  <ChevronUp className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                )}
              </button>
              {showResultDetails && (
                <div className="mt-2 text-sm text-gray-700 dark:text-gray-300">
                  <pre className="overflow-auto p-2 bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                    {JSON.stringify(result?.error || result?.data || result, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}

          {/* Parameters section - for both cancelled and non-cancelled actions */}
          {actionParameters && Object.keys(actionParameters).length > 0 && (
            <div>
              <button
                onClick={() => setShowParameters(!showParameters)}
                className="flex items-center justify-between w-full p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700"
              >
                <span>Parameters</span>
                {showParameters ? (
                  <ChevronUp className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                )}
              </button>
              {showParameters && (
                <div className="mt-2">
                  <div className="bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                    <div className="p-4">
                      <FormatParameters actionParameters={actionParameters} />
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { ActionResult };
