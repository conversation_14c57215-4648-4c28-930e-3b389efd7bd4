import clsx from 'clsx';

interface ConnectionLineProps {
  mini?: boolean;
}

function ConnectionLine({ mini = false }: ConnectionLineProps) {
  return (
    <>
      {/* Horizontal line for desktop or mini mode */}
      <div
        className={clsx(
          'hidden sm:flex bg-gray-300 dark:bg-gray-600 relative self-center transform',
          mini ? 'w-4 h-0.5' : 'w-12 h-0.5 flex-grow mx-4 -translate-y-4'
        )}
      >
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-2 h-2 border-t border-r border-gray-300 dark:border-gray-600 transform rotate-45"></div>
      </div>

      {/* Vertical line for mobile (not shown in mini mode) */}
      {!mini && (
        <div className="sm:hidden h-8 w-0.5 bg-gray-300 dark:bg-gray-600 relative transform -translate-y-4">
          <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-2 h-2 border-b border-r border-gray-300 dark:border-gray-600 transform rotate-45"></div>
        </div>
      )}
    </>
  );
}

export { ConnectionLine };
