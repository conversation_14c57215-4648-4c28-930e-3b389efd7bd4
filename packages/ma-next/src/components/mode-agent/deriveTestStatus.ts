import { TestStatus } from 'src/types';

/**
 * Helper function to derive test status from execution
 * @param execution The taskflow execution object
 * @returns The test status
 */
function deriveTestStatus(execution: any): TestStatus {
  if (!execution) {
    return 'idle';
  }

  // Check if the execution is completed
  if (execution.completedAt) {
    // Check if any nodes have errors
    const hasErrors = Object.values(execution.context || {}).some(
      (node: any) => node.status === 'ERROR'
    );

    return hasErrors ? 'failed' : 'success';
  }

  // Check if the execution has a status field
  if (execution.status) {
    if (execution.status === 'PENDING') {
      return 'simulating';
    } else if (execution.status === 'RUNNING') {
      return 'working';
    } else if (execution.status === 'ERROR') {
      return 'failed';
    } else if (execution.status === 'SUCCESS') {
      return 'success';
    }
  }

  // If not completed, check if it's in progress
  const isSimulating = Object.values(execution.context || {}).every(
    (node: any) => !node.status || node.status === 'PENDING'
  );

  return isSimulating ? 'simulating' : 'working';
}

export { deriveTestStatus };
