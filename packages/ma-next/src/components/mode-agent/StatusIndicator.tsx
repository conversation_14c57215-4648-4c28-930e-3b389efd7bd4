import { CheckCircle2, XCircle } from 'lucide-react';
import { NodeStatus } from '../../types';

interface StatusIndicatorProps {
  status: NodeStatus;
  testStatus: string;
  nodeIndex: number;
}

function StatusIndicator({ status, testStatus, nodeIndex }: StatusIndicatorProps) {
  if (status === 'simulating' && nodeIndex === 0) {
    return (
      <div className="text-xs text-indigo-600 dark:text-indigo-400 flex items-center">
        <span className="animate-pulse">Simulating...</span>
      </div>
    );
  }

  if (status === 'active' && testStatus === 'idle' && nodeIndex === 0) {
    return (
      <div className="text-xs text-green-600 dark:text-green-400 flex items-center">
        <span>Active</span>
      </div>
    );
  }

  if (status === 'inactive' && testStatus === 'idle' && nodeIndex === 0) {
    return (
      <div className="text-xs text-gray-400 dark:text-gray-500 flex items-center">
        <span>Inactive</span>
      </div>
    );
  }

  if (status === 'processing' && nodeIndex === 1) {
    return (
      <div className="text-xs text-indigo-600 dark:text-indigo-400 flex items-center">
        <span className="animate-pulse">Processing...</span>
      </div>
    );
  }

  if (status === 'failed' && nodeIndex === 1) {
    return (
      <div className="text-xs text-red-600 dark:text-red-400 flex items-center">
        <XCircle className="w-4 h-4 mr-1" />
        <span>Failed</span>
      </div>
    );
  }

  if (status === 'success' && nodeIndex === 2) {
    return (
      <div className="text-xs text-green-600 dark:text-green-400 flex items-center">
        <CheckCircle2 className="w-4 h-4 mr-1" />
        <span>Success</span>
      </div>
    );
  }

  return null;
}

export { StatusIndicator };
