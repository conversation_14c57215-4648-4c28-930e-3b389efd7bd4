import clsx from 'clsx';

import type { ReactNode } from 'react';

interface AgentControlPanelProps {
  setupIsComplete: boolean;
  isTesting: boolean;
  isActive: boolean;
  onTest?: () => void;
  onToggleActive?: () => Promise<void>;
  children?: any;
}

function AgentControlPanel({
  isTesting,
  isActive,
  setupIsComplete,
  onTest,
  onToggleActive,
  children,
}: AgentControlPanelProps) {
  const toggleTrigger = async () => {
    if (onToggleActive) {
      await onToggleActive();
    }
  };

  return (
    <div className="mt-6 mb-8">
      <div
        className={clsx('p-3 rounded-lg bg-white dark:bg-gray-800', {
          'opacity-100': setupIsComplete,
          'opacity-50 pointer-events-none': !setupIsComplete,
        })}
      >
        <div className="flex justify-between items-center">
          <button
            onClick={onTest}
            disabled={!setupIsComplete || isTesting}
            className={clsx('px-6 py-2 rounded-md text-sm font-medium', {
              'bg-indigo-600 hover:bg-indigo-500 text-white': setupIsComplete && !isTesting,
              'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500':
                !setupIsComplete || isTesting,
            })}
          >
            Test Agent
          </button>

          <div className="flex items-center">
            <span className="mr-2 text-sm text-gray-600 dark:text-gray-400">
              {isActive ? 'Active' : 'Inactive'}
            </span>
            <button
              onClick={toggleTrigger}
              className={clsx(
                'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2',
                {
                  'bg-green-500': isActive,
                  'bg-gray-200 dark:bg-gray-700': !isActive,
                }
              )}
            >
              <span
                className={clsx(
                  'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                  {
                    'translate-x-6': isActive,
                    'translate-x-1': !isActive,
                  }
                )}
              />
            </button>
          </div>
        </div>
        {children}
      </div>
    </div>
  );
}

export { AgentControlPanel };
