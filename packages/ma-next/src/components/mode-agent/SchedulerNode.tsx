import clsx from 'clsx';
import { Clock } from 'lucide-react';
import { NodeStatus } from '../../types';
import { AgentNode } from './AgentNode';

interface SchedulerNodeProps {
  label: string;
  status: NodeStatus;
  testStatus: string;
  nodeIndex: number;
}

function SchedulerNode({ label, status, testStatus, nodeIndex }: SchedulerNodeProps) {
  const isProcessing = status === 'processing' || status === 'simulating';
  const isFailed = status === 'failed';

  const icon = (
    <Clock
      className={clsx('w-10 h-10', {
        'text-indigo-600 dark:text-indigo-400': isProcessing,
        'text-red-600 dark:text-red-400': isFailed,
        'text-gray-600 dark:text-gray-400': !isProcessing && !isFailed,
      })}
    />
  );

  return (
    <AgentNode
      icon={icon}
      label={label}
      status={status}
      testStatus={testStatus}
      nodeIndex={nodeIndex}
    />
  );
}

export { SchedulerNode };
