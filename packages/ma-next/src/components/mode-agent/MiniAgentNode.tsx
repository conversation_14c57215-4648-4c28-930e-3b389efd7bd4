import clsx from 'clsx';

interface MiniAgentNodeProps {
  icon: React.ReactNode;
  label?: string;
}

function MiniAgentNode({ icon, label }: MiniAgentNodeProps) {
  return (
    <div className="flex flex-col items-center scale-90">
      <div
        className={clsx(
          'w-20 h-12 p-3 rounded-lg border-2 flex flex-col items-center justify-center border-gray-200 dark:border-gray-700'
        )}
      >
        <div className="scale-75">{icon}</div>
        {label && (
          <div className="text-[10px] font-medium text-gray-700 dark:text-gray-300 text-nowrap">
            {label}
          </div>
        )}
      </div>
    </div>
  );
}

export { MiniAgentNode };
