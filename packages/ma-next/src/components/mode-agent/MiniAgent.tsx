import React from 'react';
import { ConnectionLine } from './ConnectionLine';
import { MiniAgentNode } from './MiniAgentNode';
import {
  ICON_LOOKUP,
  ProviderType,
  getActionLabel,
  getSyncLabel,
} from 'src/config/agentLabels';
import { Bot, Undo2 } from 'lucide-react';
import { GmailIcon } from 'components/icons/providers';

interface MiniAgentProps {
  nodes: any[];
}

function MiniAgent({ nodes }: MiniAgentProps) {
  return (
    <div className="group relative">
      <div className="opacity-20 flex flex-row items-center justify-between w-[350px] pr-4 mb-6 bg-white rounded-md  p-3 dark:bg-gray-800">
        {nodes.map((node, index) => {
          // Get the provider from node config
          const provider = node.providerKey as ProviderType;

          return (
            <React.Fragment key={node.id}>
              {index > 0 && (
                <div className="flex-shrink-0 mx-1">
                  <ConnectionLine mini />
                </div>
              )}
              {node.type === 'ai' ? (
                <div className="flex-shrink-0">
                  <MiniAgentNode icon={<Bot className="w-8 h-8 translate-y-1" />} label="AI" />
                </div>
              ) : (
                <div className="flex-shrink-0">
                  <MiniAgentNode
                    icon={
                      provider && provider in ICON_LOOKUP ? ICON_LOOKUP[provider] : <GmailIcon />
                    }
                    label={
                      node.syncKey
                        ? getSyncLabel(provider, node.syncKey)
                        : node.actionKey
                        ? getActionLabel(provider, node.actionKey)
                        : ''
                    }
                  />
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>
      <div className="absolute -bottom-2 flex gap-1 left-2 opacity-0 group-hover:opacity-100 transition-opacity text-[10px] text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded-md cursor-pointer">
        <Undo2 className="w-3 h-3" />
        Rollback (Coming soon)
      </div>
    </div>
  );
}

export { MiniAgent };
