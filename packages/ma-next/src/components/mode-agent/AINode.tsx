import clsx from 'clsx';
import { Bot, Settings } from 'lucide-react';
import { NodeStatus } from '../../types';
import { AgentNode } from './AgentNode';
import { Dispatch, SetStateAction } from 'react';

interface AINodeProps {
  status: NodeStatus;
  testStatus: string;
  nodeIndex: number;
  advancedOptions?: boolean;
  setAdvancedOptions?: Dispatch<SetStateAction<boolean>>;
}

function AINode({
  status,
  testStatus,
  nodeIndex,
  advancedOptions = false,
  setAdvancedOptions,
}: AINodeProps) {
  const isProcessing = status === 'processing';
  const isFailed = status === 'failed';

  const handleSettingsClick = () => {
    if (setAdvancedOptions) {
      setAdvancedOptions(!advancedOptions);
    }
  };

  const icon = (
    <>
      <Bot
        className={clsx('w-10 h-10', {
          'text-indigo-600 dark:text-indigo-400': isProcessing,
          'text-red-600 dark:text-red-400': isFailed,
          'text-gray-600 dark:text-gray-400': !isProcessing && !isFailed,
        })}
      />
      <button onClick={handleSettingsClick}>
        <Settings className="w-[18px] h-[18px] absolute top-[-2px] p-px right-[-2px] opacity-0 bg-white dark:bg-gray-800 cursor-pointer rounded transition-opacity duration-100 group-hover:opacity-100" />
      </button>
    </>
  );

  return (
    <AgentNode
      icon={icon}
      label="AI"
      status={status}
      testStatus={testStatus}
      nodeIndex={nodeIndex}
    />
  );
}

export { AINode };
