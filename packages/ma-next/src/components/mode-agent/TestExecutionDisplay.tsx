import { RichResultDisplay } from '../rich-ui/RichResultDisplay';
import { RichParameterDisplay } from '../rich-ui/RichParameterDisplay';
import { RichSyncOutputs } from 'components/rich-ui/RichSyncOutputs';

interface TestExecutionDisplayProps {
  nodes: any[];
  execution: any | null;
}

function TestExecutionDisplay({ nodes, execution }: TestExecutionDisplayProps) {
  if (!execution) return null;

  const triggerNode = nodes[0];
  const actionNode = nodes[nodes.length - 1];

  if (!triggerNode || !actionNode) return null;

  const triggerProviderKey = triggerNode.providerKey;
  const triggerSyncKey = triggerNode.syncKey;
  const actionProviderKey = actionNode.providerKey;
  const actionKey = actionNode.actionKey;

  const triggerData = execution.triggerData || execution.context?.[triggerNode.id]?.output;
  const actionParameters =
    execution.context?.[actionNode.id]?.status === 'SUCCESS' &&
    (execution.context[actionNode.id]?.executionData?.actionParameters ||
      execution.context[actionNode.id]?.parameters);

  const canTrigger =
    triggerProviderKey &&
    triggerSyncKey &&
    RichSyncOutputs.canDisplay(triggerProviderKey, triggerSyncKey);
  const canAction =
    actionProviderKey && actionKey && RichParameterDisplay.canDisplay(actionProviderKey, actionKey);

  if (!canTrigger || !canAction) return null;

  return (
    <div className="mt-6 flex flex-col sm:flex-row sm:space-x-4">
      {triggerData && (
        <div className="sm:w-1/2 sm:mb-0">
          <RichSyncOutputs
            output={triggerData}
            providerKey={triggerProviderKey}
            syncKey={triggerSyncKey}
          />
        </div>
      )}
      {actionParameters && (
        <div className="sm:w-1/2">
          <RichParameterDisplay
            providerKey={actionProviderKey}
            actionKey={actionKey}
            actionParameters={actionParameters}
          />
        </div>
      )}
    </div>
  );
}

export { TestExecutionDisplay };
