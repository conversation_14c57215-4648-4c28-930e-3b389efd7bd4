import { strictEqual } from 'node:assert';
import { test } from 'node:test';
import { deriveDisplayNodes } from '../deriveDisplayNodes';

test('deriveDisplayNodes handles agent.aToB nodes', () => {
  const taskflow = {
    schema: {
      triggers: [
        {
          id: 't1',
          parameters: {
            providerKey: 'github',
            syncKey: 'pull-requests',
            model: 'GithubPullRequest',
          },
        },
      ],
      nodes: [
        {
          id: 'n1',
          type: 'agent.aToB',
          parameters: {
            output: { providerKey: 'github', actionKey: 'update-pull-request' },
          },
        },
      ],
    },
  };

  const nodes = deriveDisplayNodes(taskflow as any);
  strictEqual(nodes[2].providerKey, 'github');
  strictEqual(nodes[2].actionKey, 'update-pull-request');
});
