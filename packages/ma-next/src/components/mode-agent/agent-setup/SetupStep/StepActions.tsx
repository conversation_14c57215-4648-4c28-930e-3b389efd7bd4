import { Pencil } from 'lucide-react';

type StepActionsProps = {
  completed?: boolean;
  skippable?: boolean;
  isDisabled?: boolean;
  onEdit: () => void;
  onSkip: () => void;
  children?: React.ReactNode; // For custom content
};

const StepActions = ({
  completed,
  skippable,
  isDisabled,
  onEdit,
  onSkip,
  children,
}: StepActionsProps) => (
  <div className="flex items-start ml-3">
    {completed && !isDisabled && (
      <button
        onClick={onEdit}
        className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
      >
        <Pencil className="w-4 h-4" />
      </button>
    )}
    {!completed && !isDisabled && skippable && (
      <button
        onClick={onSkip}
        className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
      >
        Skip
      </button>
    )}
    {!completed && !isDisabled && children}
  </div>
);

export default StepActions;
