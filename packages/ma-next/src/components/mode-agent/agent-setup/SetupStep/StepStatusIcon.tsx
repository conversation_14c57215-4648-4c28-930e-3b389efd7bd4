import clsx from 'clsx';
import { CheckCircle2 } from 'lucide-react';

type StepStatusIconProps = {
  completed?: boolean;
};

const StepStatusIcon = ({ completed }: StepStatusIconProps) => (
  <div
    className={clsx('w-6 h-6 mt-0.5 rounded-full flex items-center justify-center flex-shrink-0', {
      'bg-green-100 dark:bg-green-900': completed,
      'bg-gray-100 dark:bg-gray-800': !completed,
    })}
  >
    {completed ? (
      <CheckCircle2 className="w-4 h-4" />
    ) : (
      <div className="w-2 h-2 rounded-full bg-current" />
    )}
  </div>
);

export default StepStatusIcon;
