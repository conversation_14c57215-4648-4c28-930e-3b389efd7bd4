import clsx from 'clsx';

type StepContainerProps = {
  completed?: boolean;
  isDisabled?: boolean;
  children: React.ReactNode;
};

const StepContainer = ({ completed, isDisabled, children }: StepContainerProps) => (
  <div
    className={clsx('flex items-center justify-between p-4 rounded-lg', {
      'text-green-600 dark:text-green-400': completed,
      'text-gray-600 dark:text-gray-400': !completed,
      'opacity-50': isDisabled,
    })}
  >
    {children}
  </div>
);

export default StepContainer;
