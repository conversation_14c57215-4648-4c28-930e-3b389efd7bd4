import { SetupStepType } from '../../../../types';
import { useTaskflowField } from 'hooks/useTaskflowField';

// New imports
import StepContainer from './StepContainer';
import StepStatusIcon from './StepStatusIcon';
import StepText from './StepText';
import StepActions from './StepActions';

type SetupStepProps = {
  step: SetupStepType;
  stepIndex: number;
  isDisabled?: boolean;
  statusIcon?: React.ReactNode;
  text?: React.ReactNode;
  actions?: React.ReactNode;
  content?: React.ReactNode;
  canSkip?: boolean;
  inlineContent?: React.ReactNode;
};

function SetupStep({
  step,
  stepIndex,
  statusIcon,
  text,
  actions,
  content,
  isDisabled,
  canSkip,
  inlineContent,
}: SetupStepProps) {
  const { onChange } = useTaskflowField<boolean>(`steps[${stepIndex}].completed`);

  return (
    <div>
      <StepContainer completed={step.completed} isDisabled={isDisabled}>
        <div className="flex items-start space-x-3 flex-1">
          {statusIcon || <StepStatusIcon completed={step.completed} />}
          <StepText>{text}</StepText>
        </div>
        {actions || (
          <StepActions
            completed={step.completed}
            skippable={step.skippable || canSkip}
            isDisabled={isDisabled}
            onEdit={() => onChange(false)}
            onSkip={() => onChange(true)}
          >
            {inlineContent}
          </StepActions>
        )}
      </StepContainer>
      {step.completed ? null : content}
    </div>
  );
}

export { SetupStep };
