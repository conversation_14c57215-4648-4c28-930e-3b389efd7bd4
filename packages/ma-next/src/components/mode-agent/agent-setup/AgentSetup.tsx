import { STEP_COMPONENTS } from './components';
import { SetupStepType } from 'src/types';

interface AgentSetupProps {
  steps: SetupStepType[];
}

function AgentSetup({ steps }: AgentSetupProps) {
  return (
    <div className="space-y-2 mt-10">
      <h3 className="text-md font-bold text-gray-700 dark:text-gray-300 mb-2">Setup</h3>
      {steps.map((step, index) => {
        const previousSteps = steps.slice(0, index);
        const Component =
          STEP_COMPONENTS[step.type as keyof typeof STEP_COMPONENTS] || STEP_COMPONENTS.default;

        return (
          <Component
            key={index}
            step={step}
            stepIndex={index}
            isDisabled={previousSteps.some(s => !s.completed)}
          />
        );
      })}
    </div>
  );
}

export { AgentSetup };
