import React, { useState, useEffect } from 'react';
import { SetupStep } from '../SetupStep/SetupStep';
import { StepInterface } from '../agentSetupTypes';
import clsx from 'clsx';
import { useTaskflowForm } from '../../../../contexts/TaskflowFormContext';

function FineTuneStep(props: StepInterface) {
  const { taskflow, updateTaskflowField } = useTaskflowForm();
  const [isSaving, setIsSaving] = useState(false);

  // Find the first AI node with a system prompt
  const aiNode = taskflow?.schema?.nodes?.find(
    (node: any) => node.type === 'ai.simple' && node.parameters?.system !== undefined
  );

  // Get the index of the AI node
  const aiNodeIndex = aiNode ? taskflow.schema.nodes.indexOf(aiNode) : -1;

  // Get the current system prompt
  const systemPrompt = aiNode?.parameters?.system || '';

  const [briefText, setBriefText] = useState(systemPrompt);

  useEffect(() => {
    setBriefText(systemPrompt);
  }, [systemPrompt]);

  const handleBriefSubmit = () => {
    if (briefText.trim() && aiNodeIndex !== -1) {
      // Set saving state
      setIsSaving(true);

      // Update the system prompt in the AI node
      updateTaskflowField(`nodes[${aiNodeIndex}].parameters.system`, briefText);

      setTimeout(() => {
        setIsSaving(false);
      }, 500);
    }
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (
      e.key === 'Enter' &&
      (e.ctrlKey || e.metaKey) &&
      briefText.trim() &&
      briefText !== systemPrompt
    ) {
      e.preventDefault();
      handleBriefSubmit();
    }
  };

  const handleBriefInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setBriefText(e.target.value);
  };

  return (
    <SetupStep
      {...props}
      text="Fine-tune your AI system prompt"
      content={
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
          <div className="relative">
            <textarea
              value={briefText}
              onChange={handleBriefInputChange}
              onKeyDown={handleKeyDown}
              placeholder="Customize the system prompt for your AI"
              className="w-full h-40 p-3 pb-12 border border-gray-200 rounded-lg resize-none
                       focus:ring-2 focus:ring-indigo-500 focus:border-transparent
                       dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              onClick={handleBriefSubmit}
              disabled={!briefText?.trim() || briefText === systemPrompt || isSaving}
              className={clsx(
                'absolute bottom-3 right-3 px-3 py-1.5 rounded-lg text-xs font-medium',
                {
                  'bg-indigo-600 hover:bg-indigo-500 text-white disabled:bg-indigo-300 disabled:cursor-not-allowed':
                    briefText?.trim() && briefText !== systemPrompt && !isSaving,
                  'bg-indigo-500 text-white cursor-wait': isSaving,
                  'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500':
                    (!briefText?.trim() || briefText === systemPrompt) && !isSaving,
                }
              )}
            >
              {isSaving ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      }
    />
  );
}

export { FineTuneStep };
