import { useState } from 'react';
import { MapPin, Loader2 } from 'lucide-react';
import { useTaskflowField } from 'hooks/useTaskflowField';
import { SetupStep } from '../SetupStep/SetupStep';
import { StepInterface } from '../agentSetupTypes';

function UserLocationStep({ step, stepIndex }: StepInterface) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { onChange: updateStepCompletion } = useTaskflowField<boolean>(
    `steps[${stepIndex}].completed`
  );
  const { onChange: updateLocationData } = useTaskflowField<{
    latitude: number;
    longitude: number;
    text: string;
  }>(`steps[${stepIndex}].data`);

  const handleGetLocation = () => {
    setIsLoading(true);
    setError(null);

    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser');
      setIsLoading(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      position => {
        const { latitude, longitude } = position.coords;
        updateLocationData({
          latitude,
          longitude,
          text: step.data?.text || 'Set your location',
        });
        updateStepCompletion(true);
        setIsLoading(false);
      },
      error => {
        let errorMessage = 'Unknown error occurred while getting your location';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location permission was denied';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'The request to get your location timed out';
            break;
        }
        setError(errorMessage);
        setIsLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      }
    );
  };

  return (
    <SetupStep
      step={step}
      stepIndex={stepIndex}
      content={
        step.completed && step.data?.latitude && step.data?.longitude ? (
          <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
            <MapPin className="w-4 h-4" />
            <span>
              Location set: {step.data.latitude.toFixed(6)}, {step.data.longitude.toFixed(6)}
            </span>
          </div>
        ) : (
          <div className="mt-2">
            {error && <div className="mb-2 text-sm text-red-600 dark:text-red-400">{error}</div>}
            <button
              onClick={handleGetLocation}
              disabled={isLoading}
              className="flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-500 disabled:bg-indigo-400"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Getting your location...
                </>
              ) : (
                <>
                  <MapPin className="w-4 h-4" />
                  Get my location
                </>
              )}
            </button>
          </div>
        )
      }
    />
  );
}

export { UserLocationStep };
