import { ConnectProvider } from 'components/setup/ConnectProviderForAgent';
import { AuthSetupStep } from 'components/setup/AuthForAgent';
import { FineTuneStep } from './FineTuneStep';
import { UserLocationStep } from './UserLocationStep';

const STEP_COMPONENTS = {
  connectProvider: ConnectProvider,
  fineTune: FineTuneStep,
  userLocation: UserLocationStep,
  auth: AuthSetupStep,
  default: () => null,
};

export { STEP_COMPONENTS };
