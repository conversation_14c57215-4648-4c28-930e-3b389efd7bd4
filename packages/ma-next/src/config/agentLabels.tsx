import {
  SlackIcon,
  NotionIcon,
  LinkedInIcon,
  GmailIcon,
  GoogleCalendarIcon,
  ZoomIcon,
  ShopifyIcon,
  GithubIcon,
} from 'components/icons/providers';

// Define provider types for type safety
export type ProviderType =
  | 'zoom'
  | 'slack'
  | 'shopify'
  | 'notion'
  | 'linkedin'
  | 'google-mail'
  | 'google-calendar'
  | 'github';

// Icon lookup map for providers
export const ICON_LOOKUP: Record<ProviderType, JSX.Element> = {
  zoom: <ZoomIcon />,
  slack: <SlackIcon />,
  shopify: <ShopifyIcon />,
  notion: <NotionIcon />,
  linkedin: <LinkedInIcon />,
  'google-mail': <GmailIcon />,
  'google-calendar': <GoogleCalendarIcon />,
  github: <GithubIcon />,
};

// Label overrides for sync keys
const SYNC_LABEL_OVERRIDES: Record<ProviderType, Record<string, string>> = {
  zoom: {
    meetings: 'Meeting synced',
    recordings: 'Recording synced',
  },
  slack: {
    messages: 'Message synced',
    channels: 'Channel synced',
  },
  shopify: {
    products: 'Product synced',
    orders: 'Order synced',
  },
  notion: {
    pages: 'Page synced',
    databases: 'Database synced',
  },
  linkedin: {
    posts: 'Post synced',
    connections: 'Connection synced',
  },
  'google-mail': {
    labels: 'Label added',
    'emails-fork': 'Receive email',
  },
  'google-calendar': {
    events: 'Event synced',
    calendars: 'Calendar synced',
  },
  github: {
    repos: 'Repository synced',
    issues: 'Issue synced',
  },
};

const ACTION_LABEL_OVERRIDES: Partial<Record<ProviderType, Record<string, string>>> = {
  'google-mail': {
    'send-email': 'Send email',
    'compose-draft': 'Compose draft',
    'compose-draft-reply': 'Draft reply',
  },
  github: {
    'create-pr': 'Create PR',
  },
};

function capitalize(word: string): string {
  if (!word) return '';
  return word.charAt(0).toUpperCase() + word.slice(1);
}

function singular(word: string): string {
  return word.endsWith('s') ? word.slice(0, -1) : word;
}

function defaultSyncLabel(key: string): string {
  return (
    key
      .split(/[_-]/)

      .map(w => capitalize(singular(w)))
      .join(' ') + ' synced'
  );
}

function defaultActionLabel(key: string): string {
  return key
    .split(/[_-]/)
    .map(w => capitalize(w))
    .join(' ');
}

export function getSyncLabel(provider: ProviderType, key: string): string {
  return SYNC_LABEL_OVERRIDES[provider]?.[key] ?? defaultSyncLabel(key);
}

export function getActionLabel(provider: ProviderType, key: string): string {
  return ACTION_LABEL_OVERRIDES[provider]?.[key] ?? defaultActionLabel(key);
}
