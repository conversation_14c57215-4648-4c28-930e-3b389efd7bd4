import {
  GmailIcon,
  GoogleCalendarIcon,
  GoogleSheetsIcon,
  NotionIcon,
  LinkedInIcon,
  TwitterIcon,
  SlackIcon,
  DropboxIcon,
  GithubIcon,
  GoogleDocsIcon,
  GoogleDriveIcon,
  HarvestIcon,
  LinearIcon,
} from 'components/icons/providers';

const PROVIDER_CONFIGS = {
  'google-mail': {
    title: 'Send email with Gmail',
    available: true,
    name: 'G<PERSON>',
    icon: GmailIcon,
    headerColor: 'bg-red-600',
    description: 'Connect to your Gmail to allow your agents to manage emails and drafts in Gmail.',
    descriptionShort: 'Manage emails and drafts.',
    features: [
      'Send email and drafts from your Gmail account',
      'Read and extract data from incoming emails',
      'Receive notifications upon receiving mail',
    ],
  },
  'google-calendar': {
    title: 'Manage events with Google Calendar',
    available: true,
    name: 'Google Calendar',
    icon: GoogleCalendarIcon,
    headerColor: 'bg-blue-600',
    description:
      'Connect to Google Calendar to allow your agents to manage your calendar events and meetings.',
    descriptionShort: 'Manage events and meetings.',
    features: [
      'Create and manage calendar events',
      'Schedule meetings automatically',
      'Get notifications for upcoming events',
    ],
  },
  'google-sheet': {
    title: 'Work with Google Sheets',
    available: true,
    name: 'Google Sheets',
    icon: GoogleSheetsIcon,
    headerColor: 'bg-green-600',
    description:
      'Connect to Google Sheets to allow your agents to read and write spreadsheet data.',
    descriptionShort: 'Read and write spreadsheet data.',
    features: [
      'Read data from spreadsheets',
      'Write and update spreadsheet data',
      'Create new spreadsheets',
    ],
  },
  notion: {
    title: 'Organize with Notion',
    available: true,
    name: 'Notion',
    icon: NotionIcon,
    headerColor: 'bg-gray-900',
    description: 'Connect to Notion to allow your agents to manage your workspace content.',
    descriptionShort: 'Manage your workspace content.',
    features: ['Create and update pages', 'Manage databases', 'Search and retrieve content'],
  },
  linkedin: {
    title: 'Network on LinkedIn',
    available: true,
    name: 'LinkedIn',
    icon: LinkedInIcon,
    headerColor: 'bg-blue-700',
    description: 'Connect to LinkedIn to allow your agents to manage your professional network.',
    descriptionShort: 'Manage your professional network.',
    features: ['Manage connections', 'Post updates', 'Track engagement'],
  },
  'x-social': {
    title: 'Engage on X',
    available: true,
    name: 'X',
    icon: TwitterIcon,
    headerColor: 'bg-black',
    description:
      'Connect to X (formerly Twitter) to allow your agents to manage your social presence.',
    descriptionShort: 'Manage your social presence.',
    features: ['Post tweets', 'Monitor mentions', 'Engage with followers'],
  },
  slack: {
    title: 'Communicate with Slack',
    available: true,
    name: 'Slack',
    icon: SlackIcon,
    headerColor: 'bg-purple-600',
    description: 'Connect to Slack to allow your agents to manage your team communications.',
    descriptionShort: 'Manage your team communications.',
    features: ['Send messages', 'Monitor channels', 'Manage notifications'],
    syncs: {
      messages: {
        label: 'Messages',
        description: 'Sync user messages',
      },
    },
  },
  dropbox: {
    title: 'Manage files with Dropbox',
    available: true,
    name: 'Dropbox',
    icon: DropboxIcon,
    headerColor: 'bg-blue-600', // Updated to match emcpe
    description: 'Connect to Dropbox to allow your agents to manage files and folders.',
    descriptionShort: 'Manage files and folders.',
    features: ['Upload and download files', 'Organize folders', 'Share files'],
  },
  github: {
    title: 'Collaborate on GitHub',
    available: true,
    name: 'GitHub',
    icon: GithubIcon,
    headerColor: 'bg-black',
    invertInModal: true, // Add this property to handle dark mode better
    description: 'Connect to GitHub to allow your agents to manage repositories and issues.',
    descriptionShort: 'Manage repositories & issues.',
    features: ['Manage repositories', 'Track issues', 'Collaborate on code'],
  },
  'google-docs': {
    title: 'Edit Google Docs',
    available: true,
    name: 'Google Docs',
    icon: GoogleDocsIcon,
    headerColor: 'bg-blue-600', // Updated to match emcpe
    description: 'Connect to Google Docs to allow your agents to read and edit documents.',
    descriptionShort: 'Read and edit documents.',
    features: ['Create and edit documents', 'Read document content', 'Format text'],
  },
  'google-drive': {
    title: 'Access Google Drive',
    available: true,
    name: 'Google Drive',
    icon: GoogleDriveIcon,
    headerColor: 'bg-yellow-500', // Updated to match emcpe
    description: 'Connect to Google Drive to allow your agents to manage files and folders.',
    descriptionShort: 'Manage files & folders.',
    features: ['Upload and download files', 'Organize folders', 'Manage permissions'],
  },
  linear: {
    title: 'Manage projects with Linear',
    available: true,
    name: 'Linear',
    icon: LinearIcon,
    headerColor: 'bg-indigo-600',
    description: 'Connect to Linear to allow your agents to manage issues, projects, and teams.',
    descriptionShort: 'Manage issues and projects.',
    features: [
      'Create and update issues',
      'Manage project status',
      'Assign tasks to team members', // Placeholder feature
    ],
  },
  harvest: {
    title: 'Track time with Harvest',
    available: true,
    name: 'Harvest',
    icon: HarvestIcon,
    headerColor: 'bg-orange-600',
    description:
      'Connect to Harvest to allow your agents to track time, manage timesheets, and view reports.',
    descriptionShort: 'Track time and manage timesheets.',
    features: [
      'Log time entries',
      'Manage projects and tasks',
      'Generate time reports', // Placeholder feature
    ],
  },
  // 'zoom': {
  //   title: 'Schedule meetings with Zoom',
  //   available: true, // Assuming available, adjust if needed
  //   name: 'Zoom',
  //   icon: ZoomIcon,
  //   headerColor: 'bg-blue-400', // Placeholder color
  //   description: 'Connect to Zoom to allow your agents to schedule and manage meetings.',
  //   descriptionShort: 'Schedule and manage meetings.',
  //   features: [
  //     'Schedule meetings',
  //     'Manage meeting participants',
  //     'Retrieve meeting recordings'
  //   ]
  // }
} as const;

export { PROVIDER_CONFIGS };
