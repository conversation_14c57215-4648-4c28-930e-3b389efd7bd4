import {
  createClient,
  FunctionInvokeOptions,
  FunctionsFetchError,
  FunctionsHttpError,
  FunctionsRelayError,
} from '@supabase/supabase-js';
import { FunctionsResponse } from '@supabase/functions-js';

// Use NEXT_PUBLIC_ prefix for client-side variables in Next.js
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  throw new Error('NEXT_PUBLIC_SUPABASE_URL is required.');
}
if (!supabaseAnonKey) {
  throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY is required.');
}

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
  db: {
    schema: 'public',
  },
});

// Helper function to get the current user's ID from Supabase
async function getCurrentUserId(): Promise<string | null> {
  const { data } = await supabase.auth.getSession();
  return data.session?.user?.id || null;
}

// Copy-paste of supabase.functions.invoke, but with a fetch to /api/${functionName} instead of /functions/v1/${functionName}
supabase.functions.invoke = async function invoke<T = any>(
  functionName: string,
  options: FunctionInvokeOptions = {}
): Promise<FunctionsResponse<T>> {
  try {
    const { headers, method, body: functionArgs } = options;
    const _headers: Record<string, string> = {};
    let { region } = options;
    if (!region) {
      region = this.region;
    }
    if (region && region !== 'any') {
      _headers['x-region'] = region;
    }
    let body: any;
    if (
      functionArgs &&
      ((headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) || !headers)
    ) {
      if (
        (typeof Blob !== 'undefined' && functionArgs instanceof Blob) ||
        functionArgs instanceof ArrayBuffer
      ) {
        // will work for File as File inherits Blob
        // also works for ArrayBuffer as it is the same underlying structure as a Blob
        _headers['Content-Type'] = 'application/octet-stream';
        body = functionArgs;
      } else if (typeof functionArgs === 'string') {
        // plain string
        _headers['Content-Type'] = 'text/plain';
        body = functionArgs;
      } else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {
        // don't set content-type headers
        // Request will automatically add the right boundary value
        body = functionArgs;
      } else {
        // default, assume this is JSON
        _headers['Content-Type'] = 'application/json';
        body = JSON.stringify(functionArgs);
      }
    }

    const response = await this.fetch(`api/${functionName}`, {
      method: method || 'POST',
      // headers priority is (high to low):
      // 1. invoke-level headers
      // 2. client-level headers
      // 3. default Content-Type header
      headers: { ..._headers, ...this.headers, ...headers },
      body,
    }).catch(fetchError => {
      throw new FunctionsFetchError(fetchError);
    });

    const isRelayError = response.headers.get('x-relay-error');
    if (isRelayError && isRelayError === 'true') {
      throw new FunctionsRelayError(response);
    }

    if (!response.ok) {
      throw new FunctionsHttpError(response);
    }

    const responseType = (response.headers.get('Content-Type') ?? 'text/plain')
      .split(';')[0]
      .trim();
    let data: any;
    if (responseType === 'application/json') {
      data = await response.json();
    } else if (responseType === 'application/octet-stream') {
      data = await response.blob();
    } else if (responseType === 'text/event-stream') {
      data = response;
    } else if (responseType === 'multipart/form-data') {
      data = await response.formData();
    } else {
      // default to text
      data = await response.text();
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error };
  }
};

if (typeof window !== 'undefined' && window) {
  window.supabase = supabase;
}

export { getCurrentUserId, supabase };
