import { supabase } from './supabase';
import Nango from '@nangohq/frontend';
import { invokeFunction } from 'utils/invokeFunction';
import { AuthOptions } from '@nangohq/frontend';

/**
 * This is a signed token that tells <PERSON><PERSON> to trust requests as to which user this is.
 */
let connectionsConfigToken: string | null = null;
let nango: Nango | null = null;
let lastTokenFetch: number | null = null;
let tokenFetchInflight = false;

const TOKEN_EXPIRY_MS = 15 * 60 * 1000; // 15 minutes

/**
 * Creates a new connection to Nango for this user.
 */
async function authNango(integrationId: string) {
  const authOptions: AuthOptions & { params: Record<string, string> } = {
    detectClosedAuthWindow: true,
    params: {},
  };

  if (integrationId === 'harvest') {
    authOptions.params.appDetails = 'MakeAgent (<EMAIL>)';
  }

  if (integrationId === 'slack') {
    authOptions.authorization_params = {
      user_scope: [
        'channels:history',
        'channels:read',
        'chat:write',
        'files:read',
        'files:write',
        'reactions:read',
        'reactions:write',
        'search:read',
        'users:read',
        'users:read.email',
      ].join(','),
    };
  }

  try {
    while (!nango) {
      await primeNango();
    }
    return [null, await nango!.auth(integrationId, authOptions)];
  } catch (error) {
    console.error('Failed to connect to Nango:', error);
    return [error, null];
  }
}

/**
 * Call this when the user clicks the "Connect" button for a provider.
 */
async function connectProvider(integrationId: string) {
  await primeNango();
  return authNango(integrationId);
}

/**
 * Deletes a connection using the Edge Function
 */
async function deleteConnection(connectionId: string, providerConfigKey: string) {
  try {
    const { error } = await invokeFunction('connections', {
      method: 'DELETE',
      body: { connectionId, providerConfigKey },
    });

    return [error, !error];
  } catch (error) {
    console.error('Error deleting connection:', error);
    return [error, false];
  }
}

/**
 * Primes the Nango token for this user.
 */
async function primeNango() {
  const {
    data: { session },
  } = await supabase.auth.getSession();
  if (!session) return;

  // Check if the token is still valid
  if (
    nango &&
    connectionsConfigToken &&
    lastTokenFetch &&
    Date.now() - lastTokenFetch < TOKEN_EXPIRY_MS
  ) {
    return;
  }

  if (tokenFetchInflight) {
    return;
  }

  tokenFetchInflight = true;

  try {
    const { data, error } = await invokeFunction('get-connections-config-token');

    if (error || !data?.token) {
      console.error('Failed to get Nango token:', error);
      return;
    }

    nango = new Nango({ connectSessionToken: data.token });
    connectionsConfigToken = data.token;
    lastTokenFetch = Date.now();
  } catch (error) {
    console.error('Error getting Nango token:', error);
  } finally {
    tokenFetchInflight = false;
  }
}

export { connectProvider, deleteConnection, primeNango };
