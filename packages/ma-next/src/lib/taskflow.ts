import { supabase } from './supabase';
import { invokeFunction } from 'utils/invokeFunction';

/**
 * Subscribes to updates for a taskflow execution
 */
function subscribeToExecutionUpdates(
  executionId: string,
  callback: (execution: Record<string, any>) => void
) {
  const subscription = supabase
    .channel(`taskflow-execution-${executionId}`)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'taskflow_executions',
        filter: `id=eq.${executionId}`,
      },
      payload => {
        console.log('Execution update received:', payload);
        callback(payload.new);
      }
    )
    .subscribe();

  return subscription;
}

/**
 * Fetches a taskflow execution by ID
 */
async function fetchTaskflowExecution(executionId: string) {
  const { data, error } = await supabase
    .from('taskflow_executions')
    .select('*')
    .eq('id', executionId)
    .single();

  if (error) {
    console.error('Error fetching taskflow execution:', error);
    throw error;
  }

  return data;
}

/**
 * Resumes a paused taskflow execution
 */
async function resumeTaskflowExecution(
  executionId: string,
  nodeId: string,
  data: Record<string, any>,
  // Default to true to replay subsequent nodes
  force: boolean = true
): Promise<Record<string, any>> {
  try {
    const { data: result, error } = await invokeFunction('resume-taskflow', {
      body: {
        executionId,
        nodeId,
        data,
        force,
      },
    });

    if (error) {
      throw new Error(error.message || 'Failed to resume taskflow');
    }

    return result;
  } catch (error) {
    console.error('Error resuming taskflow:', error);
    throw error;
  }
}

export { fetchTaskflowExecution, resumeTaskflowExecution, subscribeToExecutionUpdates };
