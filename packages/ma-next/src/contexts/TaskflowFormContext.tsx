import React, { createContext, useContext, useCallback, ReactNode, useMemo } from 'react';
import { set } from 'lodash';
import { supabase } from 'lib/supabase';
import { useDebouncedCallback } from 'use-debounce';

interface TaskflowFormContextType {
  taskflow: any;
  updateTaskflowField: (path: string, value: any) => void;
}

const TaskflowFormContext = createContext<TaskflowFormContextType | undefined>(undefined);

interface TaskflowFormProviderProps {
  children: ReactNode;
  taskflow: any;
  taskflowId: string;
  setTaskflow: (taskflow: any) => void;
}

function TaskflowFormProvider({
  children,
  taskflow,
  taskflowId,
  setTaskflow,
}: TaskflowFormProviderProps) {
  // Debounced function to update the taskflow in the database
  const debouncedUpdateTaskflow = useDebouncedCallback(
    async (updatedSchema: any) => {
      try {
        await supabase.from('taskflows').update({ schema: updatedSchema }).eq('id', taskflowId);
      } catch (error) {
        console.error('Failed to update taskflow:', error);
      }
    },
    500 // 500ms debounce time
  );

  // Function to update a field in the taskflow schema
  const updateTaskflowField = useCallback(
    (path: string, value: any) => {
      const updatedTaskflow = { ...taskflow };
      const updatedSchema = JSON.parse(JSON.stringify(taskflow.schema));

      // Update the field using lodash set
      set(updatedSchema, path, value);

      // Update the local state
      updatedTaskflow.schema = updatedSchema;
      setTaskflow(updatedTaskflow);

      // Debounced update to the database
      debouncedUpdateTaskflow(updatedSchema);
    },
    [taskflow, debouncedUpdateTaskflow]
  );

  const contextValue = useMemo(
    () => ({
      taskflow,
      updateTaskflowField,
    }),
    [taskflow, updateTaskflowField]
  );

  return (
    <TaskflowFormContext.Provider value={contextValue}>{children}</TaskflowFormContext.Provider>
  );
}

function useTaskflowForm() {
  const context = useContext(TaskflowFormContext);
  if (context === undefined) {
    throw new Error('useTaskflowForm must be used within a TaskflowFormProvider');
  }
  return context;
}

export { TaskflowFormProvider, useTaskflowForm };
