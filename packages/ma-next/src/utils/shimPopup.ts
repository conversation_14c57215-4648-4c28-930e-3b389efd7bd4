// Only run this code in a browser environment
if (typeof window !== 'undefined') {
  const originalWindowOpen = window.open;
  window.open = function (url, windowName, windowFeatures) {
    // Define popup dimensions
    const popupWidth = 800;
    const popupHeight = 600;

    // Calculate center position relative to current window
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const windowLeft = window.screenX;
    const windowTop = window.screenY;

    const left = windowLeft + (windowWidth - popupWidth) / 2;
    const top = windowTop + (windowHeight - popupHeight) / 2;

    // Ensure positive coordinates
    const finalLeft = Math.max(0, left);
    const finalTop = Math.max(0, top);

    // Build the features string
    const customFeatures = `width=${popupWidth},height=${popupHeight},left=${finalLeft},top=${finalTop}`;
    const finalFeatures = windowFeatures ? `${windowFeatures},${customFeatures}` : customFeatures;

    // Call the original window.open
    const popup = originalWindowOpen.call(window, url, windowName, finalFeatures);
    return popup;
  };
}
