function formatSyncScope(scope: any): string {
  if (!scope || typeof scope !== 'object') return '';
  return Object.entries(scope)
    .map(([key, val]) => {
      let count = 0;
      if (Array.isArray(val)) count = val.length;
      else if (val && typeof val === 'object') count = Object.keys(val).length;
      else if (typeof val === 'number') count = val as number;
      const label = count === 1 ? key.replace(/s$/, '') : key;
      return `${count} ${label}`;
    })
    .join(', ');
}

export { formatSyncScope };
