import { TestStatus, NodeStatus } from '../types';

function getNodeStatus(
  nodeIndex: number,
  testStatus: TestStatus,
  triggerActive: boolean
): NodeStatus {
  // Test Agent button should always cause simulating state to happen on the first node
  if (testStatus === 'simulating') {
    if (nodeIndex === 0) return 'simulating'; // Only trigger node is simulating
    return 'idle'; // Other nodes are idle
  }

  if (testStatus === 'working') {
    // In a multi-node workflow, we simulate processing through the nodes sequentially
    // For simplicity, we'll assume the middle nodes are processing during 'working' state
    if (nodeIndex === 0) return 'idle'; // First node is done
    if (nodeIndex === 1) return 'processing'; // Second node is processing
    return 'idle'; // Other nodes are waiting
  }

  if (testStatus === 'success') {
    // In success state, the last node shows success
    if (nodeIndex === 0) return 'idle'; // First node is idle
    if (nodeIndex === 1) return 'idle'; // Middle nodes are idle
    return 'success'; // Last node shows success
  }

  if (testStatus === 'failed') {
    // In failed state, we assume the AI or processing node failed
    if (nodeIndex === 1) return 'failed'; // Middle node shows failed
    return 'idle'; // Other nodes are idle
  }

  // Default idle state - check trigger activation for first node
  if (nodeIndex === 0) {
    return triggerActive ? 'active' : 'inactive';
  }

  return 'idle';
}

export { getNodeStatus };
