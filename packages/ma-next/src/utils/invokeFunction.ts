import { supabase } from 'lib/supabase';

// Supported HTTP methods
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Configuration options for the API call
interface CallApiOptions<TRequest> {
  method?: HttpMethod; // Default: POST
  headers?: Record<string, string>; // Custom headers
  body?: TRequest; // Request payload (optional for GET)
  signal?: AbortSignal; // For cancellation
}

// Response object type
interface ApiResponse<TResponse> {
  data: TResponse | null;
  error: Error | null;
}

/**
 * Makes an authenticated request to a specified API URL
 * @param endpoint The function name
 * @param options Configuration for the request (method, headers, body, etc.)
 * @returns An object with { data, error } where only one is non-null
 */
async function invokeFunction<TResponse = any, TRequest = unknown>(
  endpoint: string,
  options: CallApiOptions<TRequest> = {}
): Promise<ApiResponse<TResponse>> {
  const { method = 'POST', headers = {}, body, signal } = options;

  // Validate inputs
  if (!endpoint || typeof endpoint !== 'string') {
    return { data: null, error: new Error('Invalid or missing API URL') };
  }

  // Get Supabase access token
  const session = (await supabase.auth.getSession()).data.session;
  const accessToken = session?.access_token;

  if (!accessToken) {
    return { data: null, error: new Error('No valid Supabase session token') };
  }

  // Construct headers
  const defaultHeaders: Record<string, string> = {
    Authorization: `Bearer ${accessToken}`,
  };
  if (body && method !== 'GET') {
    defaultHeaders['Content-Type'] = 'application/json';
  }
  const finalHeaders = { ...defaultHeaders, ...headers };

  // Prepare request config
  const fetchOptions: RequestInit = {
    method,
    headers: finalHeaders,
    signal,
  };

  if (body && method !== 'GET') {
    fetchOptions.body = JSON.stringify(body);
  }

  try {
    // Make the request
    const response = await fetch(`/api/${endpoint}`, fetchOptions);

    // Parse JSON once
    let responseData;
    try {
      responseData = await response.json();
    } catch (parseError) {
      return {
        data: null,
        error: new Error(
          `Failed to parse response from ${endpoint}: ${(parseError as Error).message}`
        ),
      };
    }

    // Check response status
    if (!response.ok) {
      return {
        data: null,
        error: new Error(
          responseData.error || `Failed to call ${endpoint} (HTTP ${response.status})`
        ),
      };
    }

    return { data: responseData as TResponse, error: null };
  } catch (error) {
    // Handle fetch or JSON parsing errors
    return {
      data: null,
      error:
        error instanceof Error ? error : new Error(`Network or parsing error: ${String(error)}`),
    };
  }
}

export { invokeFunction };
