import { useEffect, useRef } from 'react';
import { TestStatus } from '../types';
import { useTaskflowField } from './useTaskflowField';
import { invokeFunction } from 'utils/invokeFunction';

/**
 * Custom hook to handle testing a taskflow
 * @returns An object with the handleTest function
 */
function useHandleTaskflowTest() {
  const { onChange: updateTestStatus } = useTaskflowField<TestStatus>('testStatus');
  const { onChange: updateIsTesting } = useTaskflowField<boolean>('isTesting');

  // Refs to track simulation timers
  const simulationTimerRef = useRef<NodeJS.Timeout | null>(null);
  const workingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      if (simulationTimerRef.current) {
        clearTimeout(simulationTimerRef.current);
      }
      if (workingTimerRef.current) {
        clearTimeout(workingTimerRef.current);
      }
    };
  }, []);

  const handleTest = async (triggerId: string) => {
    try {
      // Start with simulating the trigger
      updateIsTesting(true);
      updateTestStatus('simulating');

      // After 1 second, move to the working state (AI processing)
      simulationTimerRef.current = setTimeout(async () => {
        updateTestStatus('working');

        if (!triggerId) {
          throw new Error('No trigger ID available');
        }

        // Call the test workflow API with the triggerId
        const { data, error } = await invokeFunction('test-workflow', {
          body: {
            triggerId,
          },
        });

        if (error) {
          updateIsTesting(false);
          updateTestStatus('failed');
          return;
        }

        if (data?.success) {
          updateIsTesting(false);
          updateTestStatus('success');
        } else {
          updateIsTesting(false);
          updateTestStatus('failed');
        }
      }, 1000);
    } catch (error) {
      console.error('Test failed:', error);
      updateIsTesting(false);
      updateTestStatus('failed');
    }
  };

  return { handleTest };
}

export { useHandleTaskflowTest };
