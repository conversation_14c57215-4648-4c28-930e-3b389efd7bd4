import { useEffect } from 'react';
import { connectionSyncsStore, ConnectionSync } from 'stores/connectionSyncs';

export interface ConnectionSync {
  name: string;
  status: string;
  frequency?: string;
  nextScheduledSyncAt?: string;
}

function useConnectionSyncs(providerKey: string) {
  const syncs = connectionSyncsStore.useSyncs(providerKey);
  const loading = connectionSyncsStore.useLoading(providerKey);

  const refresh = () => connectionSyncsStore.load(providerKey);
  const start = (name: string) => connectionSyncsStore.start(providerKey, name);
  const pause = (name: string) => connectionSyncsStore.pause(providerKey, name);

  useEffect(() => {
    connectionSyncsStore.load(providerKey);
  }, [providerKey]);

  return { syncs, loading, start, pause, refresh };
}

export { useConnectionSyncs };
