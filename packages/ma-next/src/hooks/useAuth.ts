import { authStore } from '../stores/auth';
import { modalStore } from '../stores/modal';

function useAuth() {
  const state = authStore.useAuthState();
  const { openModal, closeModal } = modalStore;

  return {
    ...state,
    verifyOtp: (otp: string) => authStore.verifyOtp(otp),
    login: (email: string, password: string) => authStore.login(email, password),
    register: (email: string, password: string) => authStore.register(email, password),
    logout: () => authStore.logout(),
    openLoginModal: () => openModal('login'),
    openRegisterModal: () => openModal('register'),
    closeAuthModal: () => closeModal(),
    resetPassword: (email: string) => authStore.resetPassword(email),
    resetLoginForm: () => authStore.resetLoginForm(),
    setNewPassword: (password: string, doLogin: boolean) =>
      authStore.setNewPassword(password, doLogin),
    clearError: () => authStore.clearError(),
  };
}

export { useAuth };
