import { Profile, profileStore } from '../stores/profile';

/**
 * Hook to access and manage the user's profile data.
 * Automatically loads the profile when the user is authenticated.
 */
function useProfile() {
  const profileState = profileStore.useProfileState();

  return {
    ...profileState,
    updateProfile: (profileData: Partial<Omit<Profile, 'id'>>) =>
      profileStore.updateProfile(profileData),
  };
}

export { useProfile };
