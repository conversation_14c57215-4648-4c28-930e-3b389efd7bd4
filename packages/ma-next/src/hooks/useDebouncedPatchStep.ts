import { useCallback, useRef } from 'react';
import { invokeFunction } from 'utils/invokeFunction';

// Default debounce delay in milliseconds
const DEFAULT_DEBOUNCE_DELAY = 500;

// Define the request type
export interface PatchStepRequest {
  agentId: string;
  path: string;
  value: any;
  meta?: any;
}

/**
 * Hook to create a debounced version of the patchStep function
 * This reduces the number of API calls when multiple updates happen in quick succession
 * @param delay Optional debounce delay in milliseconds (default: 500ms)
 * @returns An object with the debouncedPatchStep function
 */
function useDebouncedPatchStep(delay = DEFAULT_DEBOUNCE_DELAY) {
  // Use a ref to store the timeout ID and the latest request
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const latestRequestRef = useRef<PatchStepRequest | null>(null);

  // Create a debounced version of the patchStep function
  const debouncedPatchStep = useCallback(
    (request: PatchStepRequest) => {
      // Store the latest request
      latestRequestRef.current = request;

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set a new timeout
      timeoutRef.current = setTimeout(async () => {
        // Only make the API call if we have a request
        if (latestRequestRef.current) {
          try {
            await invokeFunction('patch-step', {
              body: latestRequestRef.current,
            });
          } catch (error) {
            console.error('Failed to patch step:', error);
          }
        }
      }, delay);
    },
    [delay]
  );

  return { debouncedPatchStep };
}

export { useDebouncedPatchStep };
