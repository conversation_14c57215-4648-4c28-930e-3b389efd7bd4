import { useConnections } from './useConnections';
import { useAuth } from './useAuth';
import { SetupStepType } from 'src/types';

/**
 * Custom hook to prepare agent setup steps
 * @param setupSteps The raw setup steps from the taskflow schema
 * @param advancedOptions Whether advanced options are enabled
 * @param taskflowSchema The taskflow schema to extract system prompt from
 * @returns An object with the processed steps and whether all steps are completed
 */
function usePrepareAgentSteps(
  setupSteps: SetupStepType[] = [],
  advancedOptions: boolean = false,
  taskflowSchema?: any
) {
  // Ensure setupSteps is always an array
  const steps = Array.isArray(setupSteps) ? setupSteps : [];

  const { connections } = useConnections();
  const { isAuthenticated } = useAuth();

  // Process setup steps based on connection status
  const processedSteps = steps.map((step: SetupStepType) => {
    if (
      step.type === 'connectProvider' &&
      connections.some(conn => conn.providerKey === step.provider)
    ) {
      return { ...step, completed: true };
    }
    return { ...step, completed: false };
  });

  // Add auth step if not authenticated
  let finalSteps: SetupStepType[] = !isAuthenticated
    ? [{ type: 'auth', completed: false }, ...processedSteps]
    : processedSteps;

  // Add fineTune step if advancedOptions is true and we have an AI node with a system prompt
  if (advancedOptions && taskflowSchema?.nodes) {
    // Find the first AI node with a system prompt
    const aiNode = taskflowSchema.nodes.find(
      (node: any) => node.type === 'ai.simple' && node.parameters?.system
    );

    if (aiNode) {
      finalSteps = [
        ...finalSteps,
        {
          type: 'fineTune',
          completed: false,
          advanced: true,
          data: {
            brief: aiNode.parameters.system || '',
          },
        },
      ];
    }
  }

  // Check if all setup steps are completed
  const allStepsCompleted =
    finalSteps.every(step => step.completed !== false || step.advanced) || finalSteps.length === 0;

  return { finalSteps, allStepsCompleted };
}

export { usePrepareAgentSteps };
