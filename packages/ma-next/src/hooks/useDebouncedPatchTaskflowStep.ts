import { useCallback, useRef } from 'react';
import { invokeFunction } from 'utils/invokeFunction';

// Default debounce delay in milliseconds
const DEFAULT_DEBOUNCE_DELAY = 500;

// Define the request type
export interface PatchTaskflowStepRequest {
  taskflowId: string;
  path: string;
  value: any;
  meta?: any;
}

/**
 * Hook to create a debounced version of the patchTaskflowStep function
 * This reduces the number of API calls when multiple updates happen in quick succession
 * @param delay Optional debounce delay in milliseconds (default: 500ms)
 * @returns An object with the debouncedPatchTaskflowStep function
 */
function useDebouncedPatchTaskflowStep(delay = DEFAULT_DEBOUNCE_DELAY) {
  // Use a ref to store the timeout ID and the latest request
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const latestRequestRef = useRef<PatchTaskflowStepRequest | null>(null);

  // Create a debounced version of the patchTaskflowStep function
  const debouncedPatchTaskflowStep = useCallback(
    (request: PatchTaskflowStepRequest) => {
      // Store the latest request
      latestRequestRef.current = request;

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set a new timeout
      timeoutRef.current = setTimeout(async () => {
        // Only make the API call if we have a request
        if (latestRequestRef.current) {
          try {
            await invokeFunction('patch-taskflow-step', {
              body: latestRequestRef.current,
            });
          } catch (error) {
            console.error('Failed to patch taskflow step:', error);
          }
        }
      }, delay);
    },
    [delay]
  );

  return { debouncedPatchTaskflowStep };
}

export { useDebouncedPatchTaskflowStep };
