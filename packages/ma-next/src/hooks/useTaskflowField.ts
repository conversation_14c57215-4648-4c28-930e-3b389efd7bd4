import { useCallback } from 'react';
import { get } from 'lodash-es';
import { useTaskflowForm } from '../contexts/TaskflowFormContext';

/**
 * Hook to create a field handler for a specific path in the taskflow schema
 * @template T The type of the field value
 * @param path The path to the field in the taskflow schema (e.g., "steps[0].completed")
 * @returns An object with value and onChange handler
 */
function useTaskflowField<T>(path: string) {
  const { taskflow, updateTaskflowField } = useTaskflowForm();

  // Get the current value from the taskflow schema
  const value = get(taskflow.schema, path) as T | undefined;

  // Create an onChange handler
  const onChange = useCallback(
    (newValue: T) => {
      updateTaskflowField(path, newValue);
    },
    [path, updateTaskflowField]
  );

  return { value, onChange };
}

export { useTaskflowField };
