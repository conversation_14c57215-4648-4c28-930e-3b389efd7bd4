import { useEffect, RefObject } from 'react';
import { UseChatHelpers } from '@ai-sdk/react';

/**
 * Custom hook to focus a textarea element when the chat streaming finishes.
 *
 * @param textareaRef - A ref object pointing to the textarea element.
 * @param status - The current status of the chat (e.g., 'streaming', 'finished').
 */
const useFocusWhenFinishedStreaming = (
  textareaRef: RefObject<HTMLTextAreaElement | null>,
  status: UseChatHelpers['status']
) => {
  useEffect(() => {
    if (status !== 'submitted' && status !== 'streaming') {
      textareaRef.current?.focus();
    }
  }, [status, textareaRef]); // Added textareaRef to dependencies as per React hooks linting rules
};

export { useFocusWhenFinishedStreaming };
