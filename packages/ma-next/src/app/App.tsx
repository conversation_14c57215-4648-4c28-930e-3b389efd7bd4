'use client';

import { ReactNode } from 'react';
import { ChatProvider } from 'providers/ChatContext';
import { Navbar } from 'components/navbar/Navbar';
import { IntlProvider } from 'intl';
import { AuthModal } from 'components/modals';
import 'utils/shimPopup';

function App({ children }: { children: ReactNode }) {
  return (
    <IntlProvider>
      <ChatProvider>
        <div className="min-h-screen flex flex-col bg-slate-50 dark:bg-gray-900 text-gray-800 dark:text-gray-100 scrollbar-themed">
          <Navbar />
          {children}
        </div>
        <AuthModal />
      </ChatProvider>
    </IntlProvider>
  );
}

export { App };
