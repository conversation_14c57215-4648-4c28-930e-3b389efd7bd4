import type { Metadata } from 'next';
import './globals.css';
import { useChat } from '@ai-sdk/react';
import { ReactElement } from 'react';
import Script from 'next/script';
import { App } from './App';

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactElement<{ chat: ReturnType<typeof useChat> }>;
}>) {
  return (
    <html lang="en" className="scrollbar-themed h-full">
      <body className="bg-slate-50 dark:bg-gray-900">
        <App>{children}</App>
        {process.env.NODE_ENV === 'production' && (
          <Script
            src="https://cloud.umami.is/script.js"
            data-website-id="dc4b0252-7a93-43e3-9a71-e14bc135ff6e"
            strategy="afterInteractive"
          />
        )}
      </body>
    </html>
  );
}

const metadata: Metadata = {
  title: {
    default: 'MakeAgent',
    template: '%s | MakeAgent',
  },
  description: 'Prompt to create and automate AI agents.',
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
  // themeColor moved to viewport export below
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'MakeAgent Chat',
  },
  metadataBase: new URL('http://localhost:3000'),
  openGraph: {
    type: 'website',
    url: '/',
    title: 'MakeAgent Chat',
    description: 'Prompt to create and automate AI agents.',
    images: ['/logo.svg'],
    siteName: 'MakeAgent Chat',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MakeAgent Chat',
    description: 'Prompt to create and automate AI agents.',
    images: ['/logo.svg'],
  },
  alternates: {
    canonical: '/',
  },
};

const viewport = {
  themeColor: '#ffffff',
};

export { metadata, viewport };
