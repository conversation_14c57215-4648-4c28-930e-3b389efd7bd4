'use client';

import { AlphaInfoPanel, PromptInput } from 'components/conversation';
import { useRouter } from 'next/navigation';
import { useChatContext } from 'providers/ChatContext';
import { ROUTES } from 'src/config';
import { getCurrentUserId, supabase } from 'lib/supabase';

export default function Chat() {
  const router = useRouter();
  const chat = useChatContext();

  return (
    <div className="p-6">
      <div className="max-w-3xl mx-auto min-h-[calc(100vh-8rem)] flex flex-col items-center justify-center">
        <AlphaInfoPanel />
        <PromptInput
          {...chat}
          handleSubmit={async e => {
            if (!chat.input.trim()) return;

            let userId = await getCurrentUserId();

            if (!userId) {
              const { data, error } = await supabase.auth.signInAnonymously();

              if (error) {
                throw error;
              }

              if (!data?.user?.id) {
                throw new Error('Failed to get user ID');
              }

              userId = data.user.id;
            }

            // HACK: we need to fix the actual useChat to only display messages from a given chat.
            chat.setMessages([]);
            chat.handleSubmit(e);
            router.push(ROUTES.chat(chat.id));
          }}
          isNewChat
        />
      </div>
    </div>
  );
}
