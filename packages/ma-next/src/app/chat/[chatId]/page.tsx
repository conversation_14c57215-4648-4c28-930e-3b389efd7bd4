'use client';

import { useEffect, useRef } from 'react';
import { PromptInput } from 'components/conversation/PromptInput';
import { useChatContext } from 'providers/ChatContext';
import { Message } from 'chat/protocol';
import { Messages } from 'components/conversation';

export default function Chat() {
  const chat = useChatContext();

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const { userMessageRef, latestMessageRef } = useScrollIntoView(chat.messages);

  return (
    <>
      <div
        ref={messagesContainerRef}
        className="flex-1 px-5 overflow-y-auto scrollbar-themed"
        style={{
          marginTop: '64px',
          height: 'calc(100vh - 64px - 130px)',
          paddingBottom: 'calc(100vh - 64px - 120px)',
        }}
      >
        <Messages
          messages={chat.messages}
          userMessageRef={userMessageRef}
          latestMessageRef={latestMessageRef}
        />
      </div>

      <div className="fixed bottom-0 left-0 right-0 bg-slate-50 dark:bg-gray-900 p-4 md:p-6">
        <div className="max-w-3xl mx-auto">
          <PromptInput {...chat} />
        </div>
      </div>
    </>
  );
}

function useScrollIntoView(messages: Message[]) {
  const userMessageRef = useRef<HTMLDivElement>(null);
  const latestMessageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (messages.length > 0) {
      const lastUserMessageIndex = messages.length - 2;
      const element = lastUserMessageIndex >= 0 ? userMessageRef.current : latestMessageRef.current;

      if (element) {
        const elementTop = element.getBoundingClientRect().top + window.scrollY;
        const offsetPosition = elementTop - 100; // 100px offset from the top
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth',
        });
      }
    }
  }, [messages.length]);

  return { userMessageRef, latestMessageRef };
}
