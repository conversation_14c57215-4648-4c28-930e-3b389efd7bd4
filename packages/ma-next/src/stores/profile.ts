import { createStore } from './createStore';
import { supabase } from '../lib/supabase';

export interface ProfilePreferences {
  timeZone?: string;
  autoApprovals?: string[]; // Array of provider:action keys that are auto-approved
  // Add other preference fields here in the future
}

export interface Profile {
  id: string;
  firstName: string | null;
  lastName: string | null;
  preferences: ProfilePreferences | null;
}

interface ProfileState {
  profile: Profile | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: ProfileState = {
  profile: null,
  isLoading: false,
  error: null,
};

/**
 * `ProfileStore` is a singleton class responsible for managing user profile data
 * using Supabase. It handles loading, updating, and accessing profile information.
 * The store uses the same createStore pattern as other stores in the application.
 */
class ProfileStore {
  private static instance: ProfileStore;
  private store;

  private constructor() {
    this.store = createStore<ProfileState>(initialState);

    // Reset state on logout
    supabase.auth.onAuthStateChange(async event => {
      if (event === 'SIGNED_IN') {
        // Load profile when user signs in
        setTimeout(() => {
          this.loadProfile();
        });
      } else if (event === 'SIGNED_OUT') {
        this.store.setState(initialState);
      }
    });
  }

  /**
   * Gets the singleton instance of `ProfileStore`.
   * @returns The singleton instance of `ProfileStore`.
   */
  static getInstance(): ProfileStore {
    if (!ProfileStore.instance) {
      ProfileStore.instance = new ProfileStore();
    }
    return ProfileStore.instance;
  }

  /**
   * Loads the user's profile from Supabase.
   * If the profile doesn't have a timeZone preference, it will be set to the user's current timeZone.
   */
  async loadProfile() {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return;
    }

    this.store.setState({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, firstName, lastName, preferences')
        .single();

      if (error) throw error;

      // Initialize profile with default values if needed
      const profile: Profile = {
        id: data.id,
        firstName: data.firstName || null,
        lastName: data.lastName || null,
        preferences: data.preferences || null,
      };

      // Check if we need to update the timeZone preference
      let needsUpdate = false;
      if (!profile.preferences) {
        profile.preferences = { timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone };
        needsUpdate = true;
      } else if (!profile.preferences.timeZone) {
        profile.preferences.timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        needsUpdate = true;
      }

      this.store.setState({
        profile,
        isLoading: false,
        error: null,
      });

      // Update the profile if needed
      if (needsUpdate) {
        await this.updateProfile({
          preferences: profile.preferences,
        });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      this.store.setState({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load profile',
      });
    }
  }

  /**
   * Updates the user's profile in Supabase.
   * @param profileData Partial profile data to update
   */
  async updateProfile(profileData: Partial<Omit<Profile, 'id'>>) {
    const { profile } = this.store.getState();

    if (!profile) {
      console.error('Cannot update profile: No profile loaded');
      return;
    }

    try {
      const { error } = await supabase.from('profiles').update(profileData).eq('id', profile.id);

      if (error) throw error;

      // Update local state
      this.store.setState({
        profile: {
          ...profile,
          ...profileData,
        },
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      this.store.setState({
        error: error instanceof Error ? error.message : 'Failed to update profile',
      });
    }
  }

  /**
   * A hook to access the profile state.
   * @returns Profile store state
   */
  useProfileState() {
    return this.store.useStoreState();
  }
}

const profileStore = ProfileStore.getInstance();
export { profileStore };
