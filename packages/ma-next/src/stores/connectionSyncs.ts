import { createStore } from './createStore';
import { invokeFunction } from '../utils/invokeFunction';

export interface ConnectionSync {
  name: string;
  status: string;
  frequency?: string;
  nextScheduledSyncAt?: string;
}

interface SyncState {
  syncsByProvider: Record<string, ConnectionSync[]>;
  loading: Record<string, boolean>;
}

const initialState: SyncState = {
  syncsByProvider: {},
  loading: {},
};

class ConnectionSyncsStore {
  private static instance: ConnectionSyncsStore;
  private store;

  private constructor() {
    this.store = createStore<SyncState>(initialState);
  }

  static getInstance(): ConnectionSyncsStore {
    if (!ConnectionSyncsStore.instance) {
      ConnectionSyncsStore.instance = new ConnectionSyncsStore();
    }
    return ConnectionSyncsStore.instance;
  }

  async load(providerKey: string) {
    if (!providerKey) return;
    this.store.setState({ loading: { ...this.store.getState().loading, [providerKey]: true } });
    const { data } = await invokeFunction<{ syncs: ConnectionSync[] }>(
      `connection-syncs?providerKey=${providerKey}`,
      { method: 'GET' }
    );
    if (data?.syncs) {
      this.store.setState({
        syncsByProvider: {
          ...this.store.getState().syncsByProvider,
          [providerKey]: data.syncs,
        },
      });
    }
    this.store.setState({ loading: { ...this.store.getState().loading, [providerKey]: false } });
  }

  async start(providerKey: string, sync: string | { name: string; metadata?: any }) {
    const payload = typeof sync === 'string' ? { [sync]: null } : { [sync.name]: sync.metadata };

    // Optimistically update the sync status locally so the UI feels responsive
    const current = this.store.getState().syncsByProvider[providerKey] || [];
    this.store.setState({
      syncsByProvider: {
        ...this.store.getState().syncsByProvider,
        [providerKey]: current.map(s =>
          s.name === payload.name ? { ...s, status: 'RUNNING' } : s
        ),
      },
    });

    await invokeFunction('connection-syncs', {
      method: 'POST',
      body: { providerKey, syncs: payload, action: 'start' },
    });
    await this.load(providerKey);
  }

  async pause(providerKey: string, name: string) {
    // Optimistically update the sync status locally so the UI feels responsive
    const current = this.store.getState().syncsByProvider[providerKey] || [];
    this.store.setState({
      syncsByProvider: {
        ...this.store.getState().syncsByProvider,
        [providerKey]: current.map(sync =>
          sync.name === name ? { ...sync, status: 'PAUSED' } : sync
        ),
      },
    });

    await invokeFunction('connection-syncs', {
      method: 'POST',
      body: { providerKey, syncs: [name], action: 'pause' },
    });
    await this.load(providerKey);
  }

  useSyncs(providerKey: string) {
    return this.store.useStoreState(state => state.syncsByProvider[providerKey] || []);
  }

  useLoading(providerKey: string) {
    return this.store.useStoreState(state => state.loading[providerKey] || false);
  }
}

const connectionSyncsStore = ConnectionSyncsStore.getInstance();
export { connectionSyncsStore, ConnectionSyncsStore };
