import { createStore } from './createStore';
import { supabase } from '../lib/supabase';

export interface PromptState {
  text: string;
  isOverflowing: boolean;
}

const initialState: PromptState = {
  text: '',
  isOverflowing: false,
};

class PromptStore {
  private static instance: PromptStore;
  private store;

  private constructor() {
    this.store = createStore<PromptState>(initialState);

    supabase.auth.onAuthStateChange(async event => {
      if (event === 'SIGNED_OUT') {
        this.store.setState(initialState);
      }
    });
  }

  static getInstance(): PromptStore {
    if (!PromptStore.instance) {
      PromptStore.instance = new PromptStore();
    }
    return PromptStore.instance;
  }

  setText(text: string) {
    this.store.setState({ text });
  }

  setOverflowing(isOverflowing: boolean) {
    this.store.setState({ isOverflowing });
  }

  clear() {
    this.store.setState(initialState);
  }

  usePromptState() {
    return this.store.useStoreState();
  }
}

const promptStore = PromptStore.getInstance();
export { promptStore };
