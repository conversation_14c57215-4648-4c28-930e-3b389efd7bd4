export interface Profile {
  id: string;
  firstName?: string;
  lastName?: string;
  preferences?: any;
  createdAt: string;
  updatedAt: string;
}

export interface Conversation {
  id: string;
  title?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Taskflow {
  id: string;
  schema: any;
  conversationId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  testExecutionId?: string;
  conversations?: Conversation;
}

export interface TaskflowExecution {
  id: string;
  taskflowId: string;
  triggerData?: any;
  context: Record<string, any>;
  result?: any;
  status?: string;
  startedAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface ExecutionStep {
  id: string;
  type: string;
  status: 'RUNNING' | 'SUCCESS' | 'ERROR' | 'PAUSED';
  output?: any;
  error?: string;
  hitl?: any;
}

export interface ExecutionTrace {
  executionId: string;
  trace: { description: string; data: any }[];
}

export type InspectorMode = 'trigger' | 'inspection';
