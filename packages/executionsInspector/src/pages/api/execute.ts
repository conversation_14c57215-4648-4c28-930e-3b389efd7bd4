import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Only allow localhost
  const host = req.headers.host;
  if (!host || (!host.includes('localhost') && !host.includes('127.0.0.1'))) {
    return res.status(403).json({ error: 'Access denied' });
  }

  try {
    const { taskflowId, triggerData, userId } = req.body;

    if (!taskflowId || !triggerData || !userId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // For now, create a mock execution in the database
    // TODO: Replace this with actual ma-next execution when build issues are resolved
    const { data: execution, error: execError } = await supabase
      .from('taskflow_executions')
      .insert({
        taskflowId,
        triggerData,
        context: {},
        status: 'RUNNING',
        startedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .select()
      .single();

    if (execError) {
      console.error('Error creating execution:', execError);
      return res.status(500).json({ error: execError.message });
    }

    // Simulate execution completion after a short delay
    setTimeout(async () => {
      await supabase
        .from('taskflow_executions')
        .update({
          status: 'SUCCESS',
          result: { message: 'Mock execution completed successfully' },
          completedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .eq('id', execution.id);
    }, 2000);

    return res.status(200).json({
      success: true,
      executionId: execution.id,
      result: { message: 'Execution started successfully' }
    });
  } catch (error) {
    console.error('Error in execute API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
