import { useState } from 'react';
import { TaskflowExecution } from '@/types';
import { Play, Clock, CheckCircle, XCircle, Pause, Eye } from 'lucide-react';

interface ExecutionsListProps {
  executions: TaskflowExecution[];
  onSelectExecution: (execution: TaskflowExecution) => void;
  selectedExecution: TaskflowExecution | null;
}

const STATUS_ICONS = {
  SUCCESS: CheckCircle,
  ERROR: XCircle,
  RUNNING: Play,
  PAUSED: Pause,
  PENDING: Clock,
} as const;

const STATUS_COLORS = {
  SUCCESS: 'text-green-600 dark:text-green-400',
  ERROR: 'text-red-600 dark:text-red-400',
  RUNNING: 'text-blue-600 dark:text-blue-400',
  PAUSED: 'text-yellow-600 dark:text-yellow-400',
  PENDING: 'text-gray-600 dark:text-gray-400',
} as const;

export function ExecutionsList({ executions, onSelectExecution, selectedExecution }: ExecutionsListProps) {
  const [sortBy, setSortBy] = useState<'startedAt' | 'status'>('startedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const sortedExecutions = [...executions].sort((a, b) => {
    let comparison = 0;
    
    if (sortBy === 'startedAt') {
      comparison = new Date(a.startedAt).getTime() - new Date(b.startedAt).getTime();
    } else if (sortBy === 'status') {
      comparison = (a.status || '').localeCompare(b.status || '');
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const handleSort = (field: 'startedAt' | 'status') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const formatDuration = (startedAt: string, completedAt?: string) => {
    const start = new Date(startedAt);
    const end = completedAt ? new Date(completedAt) : new Date();
    const diffMs = end.getTime() - start.getTime();
    
    if (diffMs < 1000) return `${diffMs}ms`;
    if (diffMs < 60000) return `${Math.round(diffMs / 1000)}s`;
    if (diffMs < 3600000) return `${Math.round(diffMs / 60000)}m`;
    return `${Math.round(diffMs / 3600000)}h`;
  };

  const getContextSummary = (context: any) => {
    if (!context || typeof context !== 'object') return 'Empty';
    
    const keys = Object.keys(context);
    if (keys.length === 0) return 'Empty';
    
    const statuses = keys.map(key => context[key]?.status).filter(Boolean);
    const statusCounts = statuses.reduce((acc: Record<string, number>, status: string) => {
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
    
    return Object.entries(statusCounts)
      .map(([status, count]) => `${count} ${status}`)
      .join(', ');
  };

  if (executions.length === 0) {
    return (
      <div className="card">
        <div className="text-center py-8 text-muted-foreground">
          <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No executions found for this taskflow</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Executions ({executions.length})</h3>
        
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-muted-foreground">Sort by:</span>
          <button
            onClick={() => handleSort('startedAt')}
            className={`px-2 py-1 rounded ${
              sortBy === 'startedAt' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
            }`}
          >
            Time {sortBy === 'startedAt' && (sortOrder === 'asc' ? '↑' : '↓')}
          </button>
          <button
            onClick={() => handleSort('status')}
            className={`px-2 py-1 rounded ${
              sortBy === 'status' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
            }`}
          >
            Status {sortBy === 'status' && (sortOrder === 'asc' ? '↑' : '↓')}
          </button>
        </div>
      </div>

      <div className="space-y-2 max-h-96 overflow-auto">
        {sortedExecutions.map((execution) => {
          const StatusIcon = STATUS_ICONS[execution.status as keyof typeof STATUS_ICONS] || Clock;
          const statusColor = STATUS_COLORS[execution.status as keyof typeof STATUS_COLORS] || 'text-gray-600 dark:text-gray-400';
          const isSelected = selectedExecution?.id === execution.id;
          
          return (
            <div
              key={execution.id}
              onClick={() => onSelectExecution(execution)}
              className={`p-3 rounded-lg border cursor-pointer transition-all hover:shadow-sm ${
                isSelected 
                  ? 'border-primary bg-primary/5 shadow-sm' 
                  : 'border-border hover:border-primary/50'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <StatusIcon className={`h-4 w-4 ${statusColor}`} />
                    <span className="font-medium text-sm">
                      {execution.status || 'UNKNOWN'}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {formatDuration(execution.startedAt, execution.completedAt)}
                    </span>
                  </div>
                  
                  <div className="text-xs text-muted-foreground mb-1">
                    Started: {new Date(execution.startedAt).toLocaleString()}
                  </div>
                  
                  {execution.completedAt && (
                    <div className="text-xs text-muted-foreground mb-1">
                      Completed: {new Date(execution.completedAt).toLocaleString()}
                    </div>
                  )}
                  
                  <div className="text-xs text-muted-foreground">
                    Context: {getContextSummary(execution.context)}
                  </div>
                  
                  {execution.triggerData && Object.keys(execution.triggerData).length > 0 && (
                    <div className="text-xs text-muted-foreground mt-1">
                      Trigger: {Object.keys(execution.triggerData).join(', ')}
                    </div>
                  )}
                </div>
                
                <div className="ml-2 flex-shrink-0">
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}