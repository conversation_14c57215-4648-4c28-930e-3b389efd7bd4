import { Taskflow } from '@/types';
import { Code, Database, Settings, Workflow } from 'lucide-react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface SchemaPanelProps {
  taskflow: Taskflow;
}

export function SchemaPanel({ taskflow }: SchemaPanelProps) {
  const { schema } = taskflow;

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-lg">
            <Database className="h-5 w-5" />
            <span>Schema Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Settings className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium text-sm">Basic Info</span>
            </div>
            <div className="space-y-2 text-sm ml-6">
              <div className="flex justify-between">
                <span className="text-muted-foreground">ID:</span>
                <span className="font-mono text-xs">{taskflow.id.substring(0, 12)}...</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Active:</span>
                <span className={taskflow.active ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}>
                  {taskflow.active ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Created:</span>
                <span className="text-xs">{new Date(taskflow.createdAt).toLocaleDateString()}</span>
              </div>
              {schema?.name && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Name:</span>
                  <span className="text-xs">{schema.name}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {schema?.triggers && (
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Workflow className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium text-sm">Triggers ({schema.triggers.length})</span>
              </div>
              <div>
                <SyntaxHighlighter
                  language="json"
                  style={tomorrow}
                  customStyle={{
                    margin: 0,
                    fontSize: '10px',
                    backgroundColor: 'transparent',
                    padding: '8px'
                  }}
                >
                  {JSON.stringify(schema.triggers, null, 2)}
                </SyntaxHighlighter>
              </div>
            </div>
          )}

          {schema?.nodes && (
            <>
              <Separator />
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Code className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium text-sm">Nodes ({schema.nodes.length})</span>
                </div>
                <div>
                  <SyntaxHighlighter
                    language="json"
                    style={tomorrow}
                    customStyle={{
                      margin: 0,
                      fontSize: '10px',
                      backgroundColor: 'transparent',
                      padding: '8px'
                    }}
                  >
                    {JSON.stringify(schema.nodes, null, 2)}
                  </SyntaxHighlighter>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-lg">
            <Code className="h-5 w-5" />
            <span>Full Schema</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <SyntaxHighlighter
              language="json"
              style={tomorrow}
              customStyle={{
                margin: 0,
                fontSize: '10px',
                backgroundColor: 'transparent',
                padding: '8px'
              }}
            >
              {JSON.stringify(schema, null, 2)}
            </SyntaxHighlighter>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
