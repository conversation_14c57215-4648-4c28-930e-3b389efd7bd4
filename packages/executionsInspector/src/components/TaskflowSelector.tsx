import { Taskflow } from '@/types';
import { Workflow } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface TaskflowSelectorProps {
  availableTaskflows: Taskflow[];
  selectedTaskflow: Taskflow | null;
  onTaskflowSelect: (taskflow: Taskflow | null) => void;
}

export function TaskflowSelector({ availableTaskflows, selectedTaskflow, onTaskflowSelect }: TaskflowSelectorProps) {
  const getTaskflowDisplayName = (taskflow: Taskflow) => {
    const schema = taskflow.schema;
    if (schema?.name) {
      return schema.name;
    }
    if (schema?.title) {
      return schema.title;
    }
    return `Taskflow ${taskflow.id.substring(0, 8)}...`;
  };

  const getTaskflowDescription = (taskflow: Taskflow) => {
    const schema = taskflow.schema;
    if (schema?.description) {
      return schema.description;
    }
    const nodeCount = schema?.nodes?.length || 0;
    const triggerCount = schema?.triggers?.length || 0;
    return `${nodeCount} nodes, ${triggerCount} triggers`;
  };

  const handleTaskflowSelect = (taskflowId: string) => {
    const taskflow = availableTaskflows.find(t => t.id === taskflowId);
    onTaskflowSelect(taskflow || null);
  };

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-muted-foreground">Taskflow</label>
      <Select
        value={selectedTaskflow?.id || ''}
        onValueChange={handleTaskflowSelect}
      >
        <SelectTrigger className="">
          <div className="flex items-center space-x-2">
            <Workflow className="h-4 w-4 text-muted-foreground" />
            <SelectValue placeholder="Select taskflow..." />
          </div>
        </SelectTrigger>
        <SelectContent>
          {availableTaskflows.length === 0 ? (
            <div className="p-3 text-sm text-muted-foreground">
              No taskflows with executions found
            </div>
          ) : (
            availableTaskflows.map((taskflow) => (
              <SelectItem key={taskflow.id} value={taskflow.id}>
                <div className="flex flex-col">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">
                      {getTaskflowDisplayName(taskflow)}
                    </span>
                    {taskflow.active && (
                      <span className="px-2 py-0.5 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs rounded-full">
                        Active
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {getTaskflowDescription(taskflow)}
                  </span>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
