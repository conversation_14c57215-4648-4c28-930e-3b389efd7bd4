import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { TaskflowExecution, ExecutionTrace } from '@/types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Play,
  CheckCircle,
  XCircle,
  Clock,
  Pause,
  Activity,
  Database,
  AlertCircle,
  Triangle,
} from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/cjs/styles/prism';

interface ExecutionDetailsProps {
  executionId: string;
}

export function ExecutionDetails({ executionId }: ExecutionDetailsProps) {
  const [execution, setExecution] = useState<TaskflowExecution | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [trace, setTrace] = useState<ExecutionTrace | null>(null);
  const [contextOpen, setContextOpen] = useState(false);

  // fetch execution
  useEffect(() => {
    async function fetchExecution() {
      setLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase
          .from('taskflow_executions')
          .select('*')
          .eq('id', executionId)
          .maybeSingle();
        if (error) throw error;
        if (data) setExecution(data as TaskflowExecution);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch execution');
      } finally {
        setLoading(false);
      }
    }
    if (executionId) {
      fetchExecution();
    }
  }, [executionId]);

  useEffect(() => {
    async function fetchTrace() {
      const { data } = await supabase
        .from('execution_traces')
        .select('*')
        .eq('executionId', executionId)
        .maybeSingle();
      if (data) setTrace(data as ExecutionTrace);
    }
    if (executionId) {
      fetchTrace();
    }
  }, [executionId]);

  // realtime updates
  useEffect(() => {
    if (!executionId) return;
    const channel = supabase
      .channel(`execution-${executionId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'taskflow_executions',
          filter: `id=eq.${executionId}`,
        },
        payload => {
          if (payload.new) setExecution(payload.new as TaskflowExecution);
        }
      )
      .subscribe();
    return () => {
      channel.unsubscribe();
    };
  }, [executionId]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />;
      case 'ERROR':
        return <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />;
      case 'RUNNING':
        return <Play className="h-4 w-4 text-blue-600 dark:text-blue-400" />;
      case 'PAUSED':
        return <Pause className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4 animate-spin" />
            <span>Loading execution...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !execution) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
            <AlertCircle className="h-4 w-4" />
            <span>{error || 'Execution not found'}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderStep = (nodeId: string, nodeData: any) => {
    const status = nodeData.status || 'UNKNOWN';

    return (
      <Card key={nodeId} className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-3 text-base">
            {getStatusIcon(status)}
            <span className="font-mono text-sm">{nodeId}</span>
            {nodeData.type && (
              <span className="text-sm text-muted-foreground">({nodeData.type})</span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {nodeData.output && (
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center space-x-2">
                <Database className="h-4 w-4" />
                <span>Output</span>
              </h4>
              <div>
                <SyntaxHighlighter
                  language="json"
                  style={tomorrow}
                  customStyle={{
                    margin: 0,
                    fontSize: '11px',
                    backgroundColor: 'transparent',
                    padding: '8px',
                  }}
                >
                  {JSON.stringify(nodeData.output, null, 2)}
                </SyntaxHighlighter>
              </div>
            </div>
          )}

          {nodeData.error && (
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center space-x-2 text-red-700 dark:text-red-400">
                <XCircle className="h-4 w-4" />
                <span>Error</span>
              </h4>
              <div className="bg-red-50 dark:bg-red-900 rounded p-2">
                <pre className="text-xs text-red-800 dark:text-red-300">
                  {typeof nodeData.error === 'string'
                    ? nodeData.error
                    : JSON.stringify(nodeData.error, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {nodeData.hitl && (
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center space-x-2 text-blue-700 dark:text-blue-400">
                <Activity className="h-4 w-4" />
                <span>Human in the Loop</span>
              </h4>
              <div>
                <SyntaxHighlighter
                  language="json"
                  style={tomorrow}
                  customStyle={{
                    margin: 0,
                    fontSize: '11px',
                    backgroundColor: 'transparent',
                    padding: '8px',
                  }}
                >
                  {JSON.stringify(nodeData.hitl, null, 2)}
                </SyntaxHighlighter>
              </div>
            </div>
          )}

          {nodeData.steps && Array.isArray(nodeData.steps) && (
            <div>
              <h4 className="text-sm font-medium mb-2">Steps</h4>
              <div className="space-y-2">
                {nodeData.steps.map((step: any, idx: number) => (
                  <div
                    key={idx}
                    className="flex items-center space-x-2 text-sm p-2 bg-muted rounded"
                  >
                    <span
                      className={
                        step.completed
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-gray-600 dark:text-gray-400'
                      }
                    >
                      {step.completed ? (
                        <CheckCircle className="h-3 w-3" />
                      ) : (
                        <Clock className="h-3 w-3" />
                      )}
                    </span>
                    <span>
                      {step.type} {step.provider && `(${step.provider})`}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const stepsEntries =
    execution.context && typeof execution.context === 'object'
      ? Object.entries(execution.context)
      : [];

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {getStatusIcon(execution.status || 'UNKNOWN')}
            <span>Execution Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div>
                <span className="text-muted-foreground">ID:</span>
                <div className="font-mono text-xs mt-1">{execution.id}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Started:</span>
                <div className="text-xs mt-1">{new Date(execution.startedAt).toLocaleString()}</div>
              </div>
            </div>
            <div className="space-y-2">
              <div>
                <span className="text-muted-foreground">Status:</span>
                <div className="flex items-center space-x-2 mt-1">
                  {getStatusIcon(execution.status || 'UNKNOWN')}
                  <span className="text-sm font-medium">{execution.status}</span>
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Completed:</span>
                <div className="text-xs mt-1">
                  {execution.completedAt
                    ? new Date(execution.completedAt).toLocaleString()
                    : 'Not completed'}
                </div>
              </div>
            </div>
          </div>

          {execution.triggerData && (
            <>
              <Separator />
              <div>
                <h4 className="text-sm font-medium mb-2">Trigger Data</h4>
                <div>
                  <SyntaxHighlighter
                    language="json"
                    style={tomorrow}
                    customStyle={{
                      margin: 0,
                      fontSize: '11px',
                      backgroundColor: 'transparent',
                      padding: '8px',
                    }}
                  >
                    {JSON.stringify(execution.triggerData, null, 2)}
                  </SyntaxHighlighter>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
          <Activity className="h-5 w-5" />
          <span>Execution Steps</span>
        </h3>
        {stepsEntries.length === 0 ? (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">No execution steps available</div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {stepsEntries.map(([nodeId, nodeData]) => renderStep(nodeId, nodeData))}
          </div>
        )}
      </div>

      {execution.context && (
        <div>
          <h3
            className="text-lg font-semibold mt-6 mb-2 flex items-center space-x-2 cursor-pointer"
            onClick={() => setContextOpen(!contextOpen)}
          >
            <Triangle
              className={`h-4 w-4 transition-transform ${contextOpen ? 'rotate-180' : ''}`}
            />
            <span>Execution Context</span>
          </h3>
          {contextOpen && (
            <Card>
              <CardContent>
                <SyntaxHighlighter
                  language="json"
                  style={tomorrow}
                  customStyle={{
                    margin: 0,
                    fontSize: '11px',
                    backgroundColor: 'transparent',
                    padding: '8px',
                  }}
                >
                  {JSON.stringify(execution.context, null, 2)}
                </SyntaxHighlighter>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {trace?.trace && trace.trace.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mt-6 mb-4 flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Debug Trace</span>
          </h3>
          <Card>
            <CardContent className="space-y-4">
              {trace.trace.map((t, idx) => (
                <div key={idx} className="text-xs">
                  <div className="font-medium mb-1">{t.description}</div>
                  {t.data && Object.keys(t.data).length > 0 && (
                    <SyntaxHighlighter
                      language="json"
                      style={tomorrow}
                      customStyle={{
                        margin: 0,
                        fontSize: '11px',
                        backgroundColor: 'transparent',
                        padding: '8px',
                      }}
                    >
                      {JSON.stringify(t.data, null, 2)}
                    </SyntaxHighlighter>
                  )}
                  {idx < trace.trace.length - 1 && <Separator className="my-2" />}
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
