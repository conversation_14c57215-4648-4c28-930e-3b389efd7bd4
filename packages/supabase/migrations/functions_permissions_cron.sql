-----------------------------------------------------------------------------------------
--============================== Extensions  ================================--
-----------------------------------------------------------------------------------------

CREATE EXTENSION IF NOT EXISTS pg_net SCHEMA public;

-----------------------------------------------------------------------------------------
--============================== FUNCTIONS  ================================--
-----------------------------------------------------------------------------------------


--=================== TRIGGERED FUNCTIONS ==================================--

------------ on_auth_user_created -> handle_new_user ------------
-- Create function to create new profiles when a new user is created
create or replace function public.handle_new_user()
returns trigger
language plpgsql
security definer set search_path = public
as $$
begin
  insert into public.profiles (id, "createdAt", "updatedAt")
  values (
    new.id,
    new.created_at,
    new.created_at
  );
  return new;
end;
$$;

-- Create trigger for new users
drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- Set permissions
alter function public.handle_new_user() owner to postgres;
revoke all on function public.handle_new_user() from public;
grant execute on function public.handle_new_user() to authenticated;
------


----- update_conversation_current_taskflow function on_taskflow_created -----
CREATE OR REPLACE FUNCTION update_conversation_current_taskflow()
RETURNS TRIGGER AS $$
DECLARE
  prev_taskflow_id text;
BEGIN
  -- Get the previous taskflow ID
  SELECT "currentTaskflowId"
  INTO prev_taskflow_id
  FROM conversations
  WHERE id = NEW."conversationId";

  -- Update the conversation's currentTaskflowId
  UPDATE conversations
  SET "currentTaskflowId" = NEW.id
  WHERE id = NEW."conversationId";

  -- If there was a previous taskflow and it's not the new taskflow, set it to inactive
  IF prev_taskflow_id IS NOT NULL AND prev_taskflow_id != NEW.id THEN
    UPDATE taskflows
    SET active = false
    WHERE id = prev_taskflow_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql
SECURITY DEFINER;

-- Reattach trigger
DROP TRIGGER IF EXISTS set_conversation_current_taskflow ON taskflows;
CREATE TRIGGER set_conversation_current_taskflow
AFTER INSERT ON taskflows
FOR EACH ROW EXECUTE FUNCTION update_conversation_current_taskflow();

-- Permissions
ALTER FUNCTION public.update_conversation_current_taskflow() OWNER TO postgres;
REVOKE ALL ON FUNCTION public.update_conversation_current_taskflow() FROM public;
GRANT EXECUTE ON FUNCTION public.update_conversation_current_taskflow() TO service_role;
------------------------------------------------------------------------------------------


----- append_conversation_message 2-for-1 operation for submit-prompt to shave some millis -----
CREATE OR REPLACE FUNCTION append_conversation_message(conv_id uuid, new_message jsonb)
RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  UPDATE conversations
  SET messages = COALESCE(messages, '[]'::jsonb) || CASE
    WHEN new_message IS NOT NULL THEN new_message
    ELSE '[]'::jsonb
  END
  WHERE id = conv_id::text
  RETURNING jsonb_build_object(
    'messages', messages,
    'oaiResponseId', "oaiResponseId"
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql;
------------------------------------------------------------------------------------------



--====== CLIENT CALLABLE FUNCTIONS ======--

------------ migrate_anonymous_user function called by users ------------
-- Create function to migrate conversations from anonymous users to authenticated users
create or replace function public.migrate_anonymous_user(anon_id text)
returns void
language plpgsql
security definer
as $$
begin
  -- Validate input
  if anon_id is null then
    raise exception 'Anonymous user ID cannot be null';
  end if;

  -- Migrate conversations to authenticated user
  update public.conversations
  set "userId" = auth.uid()
  where "userId" = anon_id;

  -- Optional: Log if no rows were updated
  if not found then
    raise notice 'No conversations found for anonymous user %', anon_id;
  end if;

  return;
end;
$$;

-- Set permissions
alter function public.migrate_anonymous_user(text) owner to postgres;
revoke all on function public.migrate_anonymous_user(text) from public;
grant execute on function public.migrate_anonymous_user(text) to authenticated;
------



-----------------------------------------------------------------------------------------
--============================== MISCL  ================================--
-----------------------------------------------------------------------------------------


-- Oddly necessary:
-------------- ALTER TABLE <table> ALTER COLUMN id SET DEFAULT gen_random_uuid() -----------------------
DO $$
BEGIN
  EXECUTE (SELECT string_agg('ALTER TABLE ' || table_name || ' ALTER COLUMN id SET DEFAULT gen_random_uuid();', ' ')
           FROM information_schema.tables
           WHERE table_schema = 'public');
END $$;
--------------------------------------------------------



-----------------------------------------------------------------------------------------
--============================== PERMISSIONS  ================================--
-----------------------------------------------------------------------------------------


-- -- Grant usage on the public schema to the authenticated role (for client-side)
GRANT USAGE ON SCHEMA public TO authenticated;

-- -- Grant usage to the service role (for Edge Functions)
GRANT USAGE ON SCHEMA public TO service_role;
----------------------------------------




--===== SERVICE ROLE ========
----- GRANT ALL ON public.<table> TO service_role -----------------------
DO $$
BEGIN
  EXECUTE (SELECT string_agg('GRANT ALL ON public.' || table_name || ' TO service_role;', ' ')
           FROM information_schema.tables
           WHERE table_schema = 'public');
END $$;
--------------------------------------------------------

-- Ensure service_role can access auth schema (for admin operations)
GRANT USAGE ON SCHEMA auth TO service_role;
GRANT SELECT ON auth.users TO service_role; -- For listUsers() in your function
--==========================




--===== AUTHENTICATED ROLE ========
----- GRANT ALL ON public.<table> TO service_role -----------------------
GRANT SELECT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.conversations TO authenticated;
GRANT SELECT, INSERT, DELETE ON public.connections TO authenticated;
-- Grant only SELECT and UPDATE for emcpe_servers to authenticated users
GRANT SELECT, UPDATE ON public.emcpe_servers TO authenticated;
-- Grant access to taskflow tables
GRANT SELECT ON public.taskflow_executions TO authenticated;
GRANT SELECT ON public.sync_triggers TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.taskflows TO authenticated;
-------------------------------------------------------------------------------


----- DELETED ROWS SHOULD RETURN FULL OBJECT TO REAL-TIME RESPONSES -----
ALTER TABLE connections REPLICA IDENTITY FULL;
ALTER TABLE taskflow_executions REPLICA IDENTITY FULL;
-------------------------------------------------------------------------


----- ALTER TABLE <table> ENABLE ROW LEVEL SECURITY -----
DO $$
BEGIN
  EXECUTE (SELECT string_agg('ALTER TABLE ' || table_name || ' ENABLE ROW LEVEL SECURITY;', ' ')
           FROM information_schema.tables
           WHERE table_schema = 'public');
END $$;
--------------------------------------------------------

-- Profiles: Read and update own profile
DROP POLICY IF EXISTS "read_own_profile" ON profiles;
CREATE POLICY "read_own_profile" ON profiles FOR SELECT TO authenticated USING (auth.uid()::text = id);
DROP POLICY IF EXISTS "update_own_profile" ON profiles;
CREATE POLICY "update_own_profile" ON profiles FOR UPDATE TO authenticated USING (auth.uid()::text = id);

-- Connections: Read and insert own connections
DROP POLICY IF EXISTS "read_own_connections" ON connections;
CREATE POLICY "read_own_connections" ON connections FOR SELECT TO authenticated USING (auth.uid()::text = "userId");
DROP POLICY IF EXISTS "insert_own_connections" ON connections;
CREATE POLICY "insert_own_connections" ON connections FOR INSERT TO authenticated WITH CHECK (auth.uid()::text = "userId");
DROP POLICY IF EXISTS "delete_own_connections" ON connections;
CREATE POLICY "delete_own_connections" ON connections FOR DELETE TO authenticated USING (auth.uid()::text = "userId");

-- Mcpeasy servers: Read own servers, update only own preferences
DROP POLICY IF EXISTS "read_own_emcpe_servers" ON emcpe_servers;
CREATE POLICY "read_own_emcpe_servers" ON emcpe_servers FOR SELECT TO authenticated USING (auth.uid()::text = "userId");

-- Remove insert policy as grant is removed
DROP POLICY IF EXISTS "insert_own_emcpe_servers" ON emcpe_servers;

-- Modify update policy to only allow updating the 'preferences' column
DROP POLICY IF EXISTS "update_own_emcpe_servers" ON emcpe_servers;
CREATE POLICY "update_own_emcpe_servers" ON emcpe_servers
  FOR UPDATE TO authenticated
  USING (auth.uid()::text = "userId")
  WITH CHECK (auth.uid()::text = "userId"); -- Keep the check for safety, though USING should cover it

-- Explicitly allow updating only the 'preferences' column via GRANT (alternative to complex WITH CHECK)
-- Note: Column-level grants are often simpler than complex WITH CHECK clauses for this purpose.
-- We adjust the main GRANT above, so this policy just needs the USING clause.

-- Remove delete policy as grant is removed
DROP POLICY IF EXISTS "delete_own_emcpe_servers" ON emcpe_servers;

-- Conversations: Read and insert own conversations
DROP POLICY IF EXISTS "read_own_conversations" ON conversations;
CREATE POLICY "read_own_conversations" ON conversations FOR SELECT TO authenticated USING (auth.uid()::text = "userId");
DROP POLICY IF EXISTS "insert_own_conversations" ON conversations;
CREATE POLICY "insert_own_conversations" ON conversations FOR INSERT TO authenticated WITH CHECK (auth.uid()::text = "userId");
DROP POLICY IF EXISTS "update_own_conversations" ON conversations;
CREATE POLICY "update_own_conversations" ON conversations FOR UPDATE TO authenticated USING (auth.uid()::text = "userId");
DROP POLICY IF EXISTS "delete_own_conversations" ON conversations;
CREATE POLICY "delete_own_conversations" ON conversations FOR DELETE TO authenticated USING (auth.uid()::text = "userId");

-- Taskflows: manage own taskflows
DROP POLICY IF EXISTS "manage_own_taskflows" ON taskflows;
CREATE POLICY "manage_own_taskflows" ON taskflows
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM conversations
    WHERE conversations.id = taskflows."conversationId"
    AND conversations."userId" = auth.uid()::text
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM conversations
    WHERE conversations.id = taskflows."conversationId"
    AND conversations."userId" = auth.uid()::text
  )
);



-- Taskflow Executions: Read own executions
DROP POLICY IF EXISTS "read_own_taskflow_executions" ON taskflow_executions;
CREATE POLICY "read_own_taskflow_executions" ON taskflow_executions
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM taskflows
    JOIN conversations ON conversations.id = taskflows."conversationId"
    WHERE taskflows.id = taskflow_executions."taskflowId"
    AND conversations."userId" = auth.uid()::text
  )
);


-- Sync Triggers: Read own sync triggers
DROP POLICY IF EXISTS "read_own_sync_triggers" ON sync_triggers;
CREATE POLICY "read_own_sync_triggers" ON sync_triggers
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM taskflows
    JOIN conversations ON conversations.id = taskflows."conversationId"
    WHERE taskflows.id = sync_triggers."taskflowId"
    AND conversations."userId" = auth.uid()::text
  )
);
