# Coding Style Guide

## Function and Component Declaration Order

Always define functions and components before they are used. This improves code readability by ensuring that the most important elements appear first, with helper functions following.

### ❌ Incorrect:

```typescript
// Helper function defined before the main component
function useCustomHook() {
  // Implementation
}

function Component() {
  // Implementation using useCustomHook
}
```

### ✅ Correct:

```typescript
// Main component defined first
function Component() {
  // Implementation using useCustomHook
}

// Helper functions defined after
function useCustomHook() {
  // Implementation
}
```

## Export Patterns

Always group exports at the end of the file (except for Next.js page files which require default exports). This creates a clear separation between implementation and interface.

### ❌ Incorrect:

```typescript
export function Component() {
  // Implementation
}

function helperFunction() {
  // Implementation
}
```

### ✅ Correct:

```typescript
function Component() {
  // Implementation
}

function helperFunction() {
  // Implementation
}

export { Component, helperFunction };
```

#### Exception: Next.js Page Files

Next.js page files require default exports, which is the only exception to these rules.

## Prefer single function files

Use discretion, but wherever there is a main file, with pure functions that can be broken out to a new file, do so. Exceptions are helper files which might suitably have many similar domain functions that are all pure.
