# MakeAgent

A multi-package repository for building AI agents with various integrations.

## Repository Structure

This repository is organized as a multi-package monorepo using npm workspaces. The packages are located in the `packages/` directory:

- **easymcpeasy**: A React-based package for easy MCP integration, including its own Supabase functions (`@makeagent/easymcpeasy`)
- **mastra**: A package for agent orchestration (`@makeagent/mastra`)
- **ui**: Shared UI components for MakeAgent (`@makeagent/ui`)
- **nango-integrations**: Nango integration configurations (`@makeagent/nango-integrations`)

## Getting Started

### Prerequisites

- Node.js 18+
- npm 7+
- Supabase CLI (for mcp-server)

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/make-agent.git
cd make-agent

# Install dependencies
npm install
```

## Development

Each package can be developed independently or together:

```bash
# Start the main application
npm run dev

# Start the MCP server
npm run mcp:dev
```

## Building

```bash
# Build all packages
npm run build

# Build a specific package
npm run mcp:build
```

## Testing

```bash
# Run tests for all packages
npm run test

# Run tests for a specific package
npm run mcp:test
```

## Deployment

```bash
# Deploy the MCP server to Supabase
npm run mcp:deploy
```

======= =OLD=======

### Local Development - Setup

```
npm run dev
npx supabase start
npx supabase functions serve --no-verify-jwt
cd mastra && mastra dev
ngrok http 54321 -> point nango to this url (<url>/functions/v1/sync) as the webhook
```

### 3rd party things Pointing to MakeAgent things

Oauth Redirect URI: https://makeagent.com/oauth-callback
Slack webhook url: https://httutluxuftvjelfxzot.supabase.co/functions/v1/slack-message-handler
Mastra actions url: https://httutluxuftvjelfxzot.supabase.co/functions/v1/actions-webhook
Nango sync url: https://httutluxuftvjelfxzot.supabase.co/functions/v1/sync

### URLS USED TO SIGN UP WITH 3rd Parties

PRIVACY PAGE
https://makeagent.com/terms-of-service
https://makeagent.com/privacy-policy

### TAGLINES

Create and manage automative AI agents.

## TODO:

BLOG POSTS:

- MakeAgent is an MCP server + Client, combined.

- ASTRO
- TERMS
- PRIVACY
- GOOGLE

## INTROSPECTION

- CREATE AN ACTION_AGENT for MCP client mode. (little toggle button in the chat)

- We need to divide SYNCS into two categories:

  - Event source - automatically started

  - Resources - usually manually started

  To fix:

  - If you're logged out, and then login to perform an action, the conversationId changes, and this ruins it for Mastra.

## GOOGLE JUSTIFICATION

MakeAgent combines automation with AI, to allow customers to set up AI driven workflows to manage their digital life.

MakeAgent never utilises these apis except to:

- show the user which account they have connected for a given workflow
- as part of an execution or task or workflow expressly created or initiated by the user (including via AI services they initiate).
- trigger events when data synchronisation shows there are changed records, in order to initiate workflows.
- Gather data for processing within a workflow or task.

MakeAgent groups the authentication with Google into each service individually (sheets, docs, youtube, calendar, gmail, drive), requesting scopes for that service separately, so there is never a "grant-all" scenario.

However, within each service we must balance usability: authorising 12 times across 12 different tasks granting only the exact scopes required would be cumbersome.

For some agents/workflows that get created, I will create a custom trigger called ma-chat. Let me tell you what I know:

At the moment when a workflow get's created it's defined by the intentAgent which chooses the possible providers from nangoConstants generated by nangoIntrospection.

Then, when a nango sync comes through, we check that and trigger
