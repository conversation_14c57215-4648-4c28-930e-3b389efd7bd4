Data Stream Protocol
A data stream follows a special protocol that the AI SDK provides to send information to the frontend.

Each stream part has the format TYPE_ID:CONTENT_JSON\n.

When you provide data streams from a custom backend, you need to set the x-vercel-ai-data-stream header to v1.

The following stream parts are currently supported:

Text Part
The text parts are appended to the message as they are received.

Format: 0:string\n

Example: 0:"example"\n

Reasoning Part
The reasoning parts are appended to the message as they are received. The reasoning part is available through reasoning.

Format: g:string\n

Example: g:"I will open the conversation with witty banter."\n

Redacted Reasoning Part
The redacted reasoning parts contain reasoning data that has been redacted. This is available through redacted_reasoning.

Format: i:{"data": string}\n

Example: i:{"data": "This reasoning has been redacted for security purposes."}\n

Reasoning Signature Part
The reasoning signature parts contain a signature associated with the reasoning. This is available through reasoning_signature.

Format: j:{"signature": string}\n

Example: j:{"signature": "abc123xyz"}\n

Source Part
The source parts are appended to the message as they are received. The source part is available through source.

Format: h:Source\n

Example: h:{"sourceType":"url","id":"source-id","url":"https://example.com","title":"Example"}\n

File Part
The file parts contain binary data encoded as base64 strings along with their MIME type. The file part is available through file.

Format: k:{data:string; mimeType:string}\n

Example: k:{"data":"base64EncodedData","mimeType":"image/png"}\n

Data Part
The data parts are parsed as JSON and appended to the data array as they are received. The data is available through data.

Format: 2:Array<JSONValue>\n

Example: 2:[{"key":"object1"},{"anotherKey":"object2"}]\n

Message Annotation Part
The message annotation parts are appended to the message as they are received. The annotation part is available through annotations.

Format: 8:Array<JSONValue>\n

Example: 8:[{"id":"message-123","other":"annotation"}]\n

Error Part
The error parts are appended to the message as they are received.

Format: 3:string\n

Example: 3:"error message"\n

Tool Call Streaming Start Part
A part indicating the start of a streaming tool call. This part needs to be sent before any tool call delta for that tool call. Tool call streaming is optional, you can use tool call and tool result parts without it.

Format: b:{toolCallId:string; toolName:string}\n

Example: b:{"toolCallId":"call-456","toolName":"streaming-tool"}\n

Tool Call Delta Part
A part representing a delta update for a streaming tool call.

Format: c:{toolCallId:string; argsTextDelta:string}\n

Example: c:{"toolCallId":"call-456","argsTextDelta":"partial arg"}\n

Tool Call Part
A part representing a tool call. When there are streamed tool calls, the tool call part needs to come after the tool call streaming is finished.

Format: 9:{toolCallId:string; toolName:string; args:object}\n

Example: 9:{"toolCallId":"call-123","toolName":"my-tool","args":{"some":"argument"}}\n

Tool Result Part
A part representing a tool result. The result part needs to be sent after the tool call part for that tool call.

Format: a:{toolCallId:string; result:object}\n

Example: a:{"toolCallId":"call-123","result":"tool output"}\n

Start Step Part
A part indicating the start of a step.

It includes the following metadata:

messageId to indicate the id of the message that this step belongs to.
Format: f:{messageId:string}\n

Example: f:{"messageId":"step_123"}\n

Finish Step Part
A part indicating that a step (i.e., one LLM API call in the backend) has been completed.

This part is necessary to correctly process multiple stitched assistant calls, e.g. when calling tools in the backend, and using steps in useChat at the same time.

It includes the following metadata:

FinishReason
Usage for that step.
isContinued to indicate if the step text will be continued in the next step.
The finish step part needs to come at the end of a step.

Format: e:{finishReason:'stop' | 'length' | 'content-filter' | 'tool-calls' | 'error' | 'other' | 'unknown';usage:{promptTokens:number; completionTokens:number;},isContinued:boolean}\n

Example: e:{"finishReason":"stop","usage":{"promptTokens":10,"completionTokens":20},"isContinued":false}\n

Finish Message Part
A part indicating the completion of a message with additional metadata, such as FinishReason and Usage. This part needs to be the last part in the stream.

Format: d:{finishReason:'stop' | 'length' | 'content-filter' | 'tool-calls' | 'error' | 'other' | 'unknown';usage:{promptTokens:number; completionTokens:number;}}\n

Example: d:{"finishReason":"stop","usage":{"promptTokens":10,"completionTokens":20}}\n

The data stream protocol is supported by useChat and useCompletion on the frontend and used by default. useCompletion only supports the text and data stream parts.

On the backend, you can use toDataStreamResponse() from the streamText result object to return a streaming HTTP response.

Data Stream Example
Here is a Next.js example that uses the data stream protocol:

app/page.tsx

'use client';

import { useCompletion } from '@ai-sdk/react';

export default function Page() {
const { completion, input, handleInputChange, handleSubmit } = useCompletion({
streamProtocol: 'data', // optional, this is the default
});

return (

<form onSubmit={handleSubmit}>
<input name="prompt" value={input} onChange={handleInputChange} />
<button type="submit">Submit</button>
<div>{completion}</div>
</form>
);
}
app/api/completion/route.ts

import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
const { prompt }: { prompt: string } = await req.json();

const result = streamText({
model: openai('gpt-4o'),
prompt,
});

return result.toDataStreamResponse();
}
