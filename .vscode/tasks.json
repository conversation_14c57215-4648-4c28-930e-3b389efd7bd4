{"version": "2.0.0", "tasks": [{"label": "Run pnpm run ma", "type": "shell", "command": "pnpm run ma", "options": {"shell": {"executable": "zsh", "args": ["-l", "-c"]}}, "presentation": {"reveal": "always", "panel": "shared", "group": "dev-terminals", "showReuseMessage": false}, "group": "none"}, {"label": "Run pnpm run mastra", "type": "shell", "command": "pnpm run mastra", "options": {"shell": {"executable": "zsh", "args": ["-l", "-c"]}}, "presentation": {"reveal": "always", "panel": "shared", "group": "dev-terminals", "showReuseMessage": false}, "group": "none", "problemMatcher": []}, {"label": "Run pnpm run supabase", "type": "shell", "command": "pnpm run supabase", "options": {"shell": {"executable": "zsh", "args": ["-l", "-c"]}}, "presentation": {"reveal": "always", "panel": "shared", "group": "dev-terminals", "showReuseMessage": false}, "group": "none"}, {"label": "Run ngrok http 54321", "type": "shell", "command": "ngrok http 54321", "options": {"shell": {"executable": "zsh", "args": ["-l", "-c"]}}, "presentation": {"reveal": "always", "panel": "shared", "group": "dev-terminals", "showReuseMessage": false}, "group": "none"}, {"label": "Run All Terminals", "dependsOn": ["Run pnpm run ma", "Run pnpm run mastra", "Run pnpm run supabase", "Run ngrok http 54321"], "dependsOrder": "parallel", "problemMatcher": [], "group": {"kind": "build", "isDefault": true}}]}