import { createClient } from '@supabase/supabase-js';
import { <PERSON><PERSON> } from '@nangohq/node';
import 'dotenv/config';

// Main function at the top
async function syncConnections() {
  const useEmailFilter = process.argv.includes('--emails');
  const forceSync = process.argv.includes('--force');

  // Validate environment variables
  const envVars = {
    productionSupabaseUrl: process.env.PRODUCTION_SUPABASE_URL,
    productionSupabaseServiceRoleKey: process.env.PRODUCTION_SUPABASE_SERVICE_ROLE_KEY,
    localSupabaseUrl: process.env.LOCAL_SUPABASE_URL,
    localSupabaseServiceRoleKey: process.env.LOCAL_SUPABASE_SERVICE_ROLE_KEY,
    nangoSecretKey: process.env.NANGO_SECRET_KEY,
    filterEmails: process.env.FILTER_EMAILS
      ? process.env.FILTER_EMAILS.split(',').map(email => email.trim().toLowerCase())
      : [],
  };

  if (!envVars.productionSupabaseUrl || !envVars.productionSupabaseServiceRoleKey) {
    console.error(
      'Production Supabase URL and Service Role Key must be provided in environment variables.'
    );
    process.exit(1);
  }
  if (!envVars.localSupabaseUrl || !envVars.localSupabaseServiceRoleKey) {
    console.error(
      'Local Supabase URL and Service Role Key must be provided in environment variables.'
    );
    process.exit(1);
  }
  if (!envVars.nangoSecretKey) {
    console.error('Nango Secret Key must be provided in environment variables.');
    process.exit(1);
  }

  // Initialize clients
  const clients = {
    productionSupabase: createClient(
      envVars.productionSupabaseUrl,
      envVars.productionSupabaseServiceRoleKey
    ),
    localSupabase: createClient(envVars.localSupabaseUrl, envVars.localSupabaseServiceRoleKey),
    nango: new Nango({ secretKey: envVars.nangoSecretKey }),
  };

  // Fork early based on mode
  if (useEmailFilter) {
    await runEmailFilterMode(clients, envVars.filterEmails);
  } else {
    await runSyncMode(clients, forceSync);
  }
}

// Helper functions below
interface ConnectionDetails {
  id: string;
  providerKey?: string;
  displayName?: string;
  email?: string;
}

// Fetches connections from a Supabase database
async function fetchSupabaseConnections(
  supabaseClient: any,
  dbName: string,
  connectionIds?: string[]
): Promise<ConnectionDetails[]> {
  console.log(`Fetching connections from ${dbName} Supabase...`);
  try {
    let query = supabaseClient.from('connections').select('id, providerKey, displayName');
    if (connectionIds) {
      query = query.in('id', connectionIds);
    }
    const { data, error } = await query;
    if (error) throw error;
    const connections = data.map((conn: any) => ({
      id: conn.id,
      providerKey: conn.providerKey,
      displayName: conn.displayName,
    }));
    console.log(`Fetched ${connections.length} connections from ${dbName} Supabase.`);
    return connections;
  } catch (error) {
    console.error(`Error fetching ${dbName} Supabase connections:`, error);
    throw error;
  }
}

// Fetches and filters Nango connections
async function fetchNangoConnections(nango: any, filterEmails: string[]): Promise<any[]> {
  try {
    const response = await nango.listConnections();
    let connections = response.connections.map((conn: any) => ({
      ...conn,
      email: conn.end_user?.email || conn.display_name,
    }));
    if (filterEmails.length > 0) {
      connections = connections.filter(
        (conn: any) => conn.email && filterEmails.includes(conn.email.toLowerCase())
      );
    }
    console.log(`Fetched ${connections.length} connections from Nango.`);
    return connections;
  } catch (error) {
    console.error('Error fetching Nango connections:', error);
    throw error;
  }
}

// Outputs the connection presence table
function printPresenceTable(
  nangoConnections: any[],
  productionConnections: ConnectionDetails[],
  localConnections: ConnectionDetails[]
) {
  const nangoConnectionMap = new Map(nangoConnections.map(conn => [conn.connection_id, conn]));
  const productionConnectionMap = new Map(productionConnections.map(conn => [conn.id, conn]));
  const localConnectionMap = new Map(localConnections.map(conn => [conn.id, conn]));
  const connectionIds = nangoConnections.map(conn => conn.connection_id);

  console.log(
    '| Connection ID                        | Provider Key         | Display Name / Email | Nango | Production | Local |'
  );
  console.log(
    '|--------------------------------------|----------------------|----------------------|-------|------------|-------|'
  );

  connectionIds.forEach(connId => {
    const nangoConn = nangoConnectionMap.get(connId);
    const productionConn = productionConnectionMap.get(connId);
    const localConn = localConnectionMap.get(connId);

    const providerKey =
      nangoConn?.provider_config_key || productionConn?.providerKey || localConn?.providerKey || '';
    const displayName =
      nangoConn?.email ||
      nangoConn?.display_name ||
      productionConn?.displayName ||
      localConn?.displayName ||
      '';

    const inNango = nangoConn ? 'X' : ' ';
    const inProduction = productionConn ? 'X' : ' ';
    const inLocal = localConn ? 'X' : ' ';

    console.log(
      `| ${connId} | ${providerKey.padEnd(20)} | ${displayName.padEnd(20)} |   ${inNango}   |     ${inProduction}      |   ${inLocal}   |`
    );
  });

  console.log('---------------------------------------------------------------------------------');
}

// Runs the email filter mode (outputs only presence table)
async function runEmailFilterMode(clients: any, filterEmails: string[]) {
  // Fetch and filter Nango connections
  let nangoConnections: any[] = [];
  try {
    nangoConnections = await fetchNangoConnections(clients.nango, filterEmails);
  } catch (error) {
    return; // Error already logged
  }

  // Fetch Supabase connections for Nango IDs
  let productionConnections: ConnectionDetails[] = [];
  let localConnections: ConnectionDetails[] = [];
  const nangoConnectionIds = nangoConnections.map(conn => conn.connection_id);

  if (nangoConnectionIds.length > 0) {
    try {
      productionConnections = await fetchSupabaseConnections(
        clients.productionSupabase,
        'Production',
        nangoConnectionIds
      );
      localConnections = await fetchSupabaseConnections(
        clients.localSupabase,
        'Local',
        nangoConnectionIds
      );
    } catch (error) {
      return; // Error already logged
    }
  }

  // Output only the presence table
  printPresenceTable(nangoConnections, productionConnections, localConnections);
}

// Deletes connections from a Supabase database
async function deleteSupabaseConnections(
  supabaseClient: any,
  dbName: string,
  connectionIds: string[]
): Promise<void> {
  if (connectionIds.length === 0) {
    console.log(`No connections to remove from ${dbName} Supabase.`);
    return;
  }
  console.log(`Removing ${connectionIds.length} connections from ${dbName} Supabase...`);
  try {
    const { error } = await supabaseClient.from('connections').delete().in('id', connectionIds);
    if (error) throw error;
    console.log(
      `Successfully removed ${connectionIds.length} connections from ${dbName} Supabase.`
    );
  } catch (error) {
    console.error(`Error removing connections from ${dbName} Supabase:`, error);
  }
}

// Identifies connections to remove in sync mode
function identifyConnectionsToRemove(
  nangoConnections: any[],
  productionConnections: ConnectionDetails[],
  localConnections: ConnectionDetails[]
) {
  const nangoConnectionMap = new Map(nangoConnections.map(conn => [conn.connection_id, conn]));
  const productionConnectionMap = new Map(productionConnections.map(conn => [conn.id, conn]));
  const localConnectionMap = new Map(localConnections.map(conn => [conn.id, conn]));

  const toRemoveFromNango = nangoConnections.filter(
    conn =>
      !productionConnectionMap.has(conn.connection_id) &&
      !localConnectionMap.has(conn.connection_id)
  );

  const toRemoveFromProductionSupabase = productionConnections
    .filter(conn => !nangoConnectionMap.has(conn.id) && !localConnectionMap.has(conn.id))
    .map(conn => conn.id);

  const toRemoveFromLocalSupabase = localConnections
    .filter(conn => !nangoConnectionMap.has(conn.id) && !productionConnectionMap.has(conn.id))
    .map(conn => conn.id);

  return { toRemoveFromNango, toRemoveFromProductionSupabase, toRemoveFromLocalSupabase };
}

// Prints the removal table in sync mode
function printRemovalTable(
  toRemoveFromNango: any[],
  toRemoveFromProductionSupabase: string[],
  toRemoveFromLocalSupabase: string[]
) {
  console.log('\n--- Connections to be removed (Dry Run) ---');
  console.log(
    '| Nango                                | Production                           | Local                                |'
  );
  console.log(
    '|--------------------------------------|--------------------------------------|--------------------------------------|'
  );

  const maxRows = Math.max(
    toRemoveFromNango.length,
    toRemoveFromProductionSupabase.length,
    toRemoveFromLocalSupabase.length
  );

  for (let i = 0; i < maxRows; i++) {
    const nangoId = toRemoveFromNango[i]?.connection_id || '';
    const productionId = toRemoveFromProductionSupabase[i] || '';
    const localId = toRemoveFromLocalSupabase[i] || '';
    console.log(`| ${nangoId.padEnd(36)} | ${productionId.padEnd(36)} | ${localId.padEnd(36)} |`);
  }

  console.log('-------------------------------------------');
  console.log(
    `Summary: ${toRemoveFromNango.length} from Nango, ${toRemoveFromProductionSupabase.length} from Production Supabase, ${toRemoveFromLocalSupabase.length} from Local Supabase.`
  );
  console.log('-------------------------------------------');
}

// Prints the presence table for all connections in sync mode
function printFullPresenceTable(
  nangoConnections: any[],
  productionConnections: ConnectionDetails[],
  localConnections: ConnectionDetails[]
) {
  const nangoConnectionMap = new Map(nangoConnections.map(conn => [conn.connection_id, conn]));
  const productionConnectionMap = new Map(productionConnections.map(conn => [conn.id, conn]));
  const localConnectionMap = new Map(localConnections.map(conn => [conn.id, conn]));

  const allUniqueConnectionIds = new Set([
    ...nangoConnectionMap.keys(),
    ...productionConnectionMap.keys(),
    ...localConnectionMap.keys(),
  ]);

  console.log('\n--- Connection Presence Across Sources ---');
  console.log(
    '| Connection ID                        | Provider Key         | Display Name / Email | Nango | Production | Local |'
  );
  console.log(
    '|--------------------------------------|----------------------|----------------------|-------|------------|-------|'
  );

  allUniqueConnectionIds.forEach(connId => {
    const nangoConn = nangoConnectionMap.get(connId);
    const productionConn = productionConnectionMap.get(connId);
    const localConn = localConnectionMap.get(connId);

    const providerKey =
      nangoConn?.provider_config_key || productionConn?.providerKey || localConn?.providerKey || '';
    const displayName =
      nangoConn?.end_user?.email ||
      nangoConn?.display_name ||
      productionConn?.displayName ||
      localConn?.displayName ||
      '';

    const inNango = nangoConn ? 'X' : ' ';
    const inProduction = productionConn ? 'X' : ' ';
    const inLocal = localConn ? 'X' : ' ';

    console.log(
      `| ${connId} | ${providerKey.padEnd(20)} | ${displayName.padEnd(20)} |   ${inNango}   |     ${inProduction}      |   ${inLocal}   |`
    );
  });

  console.log('---------------------------------------------------------------------------------');
}

// Performs connection removals in sync mode
async function performRemovals(
  nango: any,
  productionSupabase: any,
  localSupabase: any,
  toRemoveFromNango: any[],
  toRemoveFromProductionSupabase: string[],
  toRemoveFromLocalSupabase: string[]
) {
  console.log('\n--- Performing Removal ---');
  for (const conn of toRemoveFromNango) {
    try {
      console.log(`Removing connection ${conn.connection_id} from Nango...`);
      await nango.deleteConnection(conn.provider_config_key, conn.connection_id);
      console.log(`Successfully removed connection ${conn.connection_id} from Nango.`);
    } catch (error) {
      console.error(`Error removing connection ${conn.connection_id} from Nango:`, error);
    }
  }

  await deleteSupabaseConnections(productionSupabase, 'Production', toRemoveFromProductionSupabase);
  await deleteSupabaseConnections(localSupabase, 'Local', toRemoveFromLocalSupabase);
  console.log('--- Removal Complete ---');
}

// Runs the original sync mode with removal logic
async function runSyncMode(clients: any, forceSync: boolean) {
  console.log(`Starting connection synchronization (Dry Run: ${!forceSync})...`);

  // Fetch all connections
  let nangoConnections: any[] = [];
  let productionConnections: ConnectionDetails[] = [];
  let localConnections: ConnectionDetails[] = [];

  try {
    nangoConnections = await fetchNangoConnections(clients.nango, []);
    productionConnections = await fetchSupabaseConnections(
      clients.productionSupabase,
      'Production'
    );
    localConnections = await fetchSupabaseConnections(clients.localSupabase, 'Local');
  } catch (error) {
    console.error('Failed to fetch connections. Aborting synchronization.');
    return;
  }

  // Identify connections to remove
  const { toRemoveFromNango, toRemoveFromProductionSupabase, toRemoveFromLocalSupabase } =
    identifyConnectionsToRemove(nangoConnections, productionConnections, localConnections);

  // Print removal table
  printRemovalTable(toRemoveFromNango, toRemoveFromProductionSupabase, toRemoveFromLocalSupabase);

  // Print full presence table
  printFullPresenceTable(nangoConnections, productionConnections, localConnections);

  // Perform removals if --force
  if (forceSync) {
    await performRemovals(
      clients.nango,
      clients.productionSupabase,
      clients.localSupabase,
      toRemoveFromNango,
      toRemoveFromProductionSupabase,
      toRemoveFromLocalSupabase
    );
  } else {
    console.log('\nSkipping actual removal in dry run mode. Run with --force to apply changes.');
  }

  console.log('\nConnection synchronization finished.');
}

// Start the script
syncConnections();
