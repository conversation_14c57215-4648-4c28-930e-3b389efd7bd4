import fs from "fs-extra";

import {
  MakeAgentEntry,
  MakeAgentField,
  MakeAgentFormat,
  MakeAgentModel,
  NangoField,
  NangoModel,
  NangoProviderData,
  OpenAIAction,
} from "./types";

/**
 * Produces a MakeAgentFormat object from https://api.nango.dev/scripts/config & https://api.nango.dev/scripts/config?format=openai zipped on param descriptions.
 */
function transformToMakeAgentFormat(
  nangoData: NangoProviderData[],
  openAIData: OpenAIAction[],
): MakeAgentFormat {
  const makeAgentFormat: MakeAgentFormat = {
    actionInputs: [],
    actionInputModelsDictionary: {},
    actionOutputs: [],
    actionOutputModelsDictionary: {},
    syncOutputs: [],
    syncOutputModelsDictionary: {},
  };

  // Create a map of action names to parameter descriptions from OpenAI data
  const paramDescriptions: Record<string, Record<string, string>> = {};
  openAIData.forEach((action) => {
    paramDescriptions[action.name] = {};
    const properties = action.parameters?.properties || {};
    Object.entries(properties).forEach(([paramName, param]) => {
      if (param.description) {
        paramDescriptions[action.name][paramName] = param.description;
      }
    });
  });

  fs.writeFileSync(
    new URL("./paramDescriptions.json", import.meta.url).pathname,
    JSON.stringify(paramDescriptions, null, 2),
    "utf8",
  );

  // Helper function to clean description by removing parameter details
  function cleanDescription(description: string | undefined): string | undefined {
    if (!description) return undefined;
    // Split on newline and take the first part, trim to remove extra whitespace
    return description.split("\n")[0].trim();
  }

  // Helper function to convert Nango field to MakeAgent field
  function convertField(
    field: NangoField,
    paramDescs: Record<string, string>,
    isActionInput: boolean,
  ): MakeAgentField {
    let type: string | { $ref: string };
    let union: string[] | undefined;

    if (field.union && Array.isArray(field.value)) {
      union = field.value
        .map((v) => (typeof v === "string" ? v : v?.value))
        .filter((v) => v !== undefined);
      type = union.length === 1 ? union[0] : "union";
    } else if (field.model && typeof field.value === "string") {
      type = { $ref: field.value };
    } else if (typeof field.value === "string") {
      if (field.value === "Record<string, any>" || field.value === "Record<string any>") {
        type = "object";
      } else if (field.value === "Date") {
        type = "string";
      } else {
        type = field.value;
      }
    } else {
      type = "any";
    }

    const makeAgentField: MakeAgentField = {
      type,
      optional: field.optional,
      description: paramDescs[field.name],
    };

    if (field.array) {
      makeAgentField.array = true;
    }

    if (union && union.length > 1) {
      makeAgentField.union = union;
    }

    return makeAgentField;
  }

  // Helper function to convert Nango model to MakeAgent model
  function convertModel(
    model: NangoModel,
    paramDescs: Record<string, string>,
    isActionInput: boolean,
  ): MakeAgentModel {
    const makeAgentModel: MakeAgentModel = {};
    model.fields?.forEach((field) => {
      if (field && field.name) {
        makeAgentModel[field.name] = convertField(field, paramDescs, isActionInput);
      }
    });
    return makeAgentModel;
  }

  // Helper function to recursively collect models
  function collectModels(
    modelName: string,
    allModels: Record<string, NangoModel>,
    targetDictionary: Record<string, MakeAgentModel>,
    paramDescs: Record<string, string>,
    visited: Set<string>,
    isActionInput: boolean,
  ) {
    if (!modelName || !allModels[modelName] || visited.has(modelName)) return;
    visited.add(modelName);

    const model = allModels[modelName];
    targetDictionary[modelName] = convertModel(model, paramDescs, isActionInput);

    model.fields?.forEach((field) => {
      if (field.model && typeof field.value === "string") {
        collectModels(
          field.value,
          allModels,
          targetDictionary,
          paramDescs,
          visited,
          isActionInput,
        );
      }
    });
  }

  // Collect all models from Nango data
  const allModels: Record<string, NangoModel> = {};
  nangoData.forEach((provider) => {
    provider.actions
      .filter((action) => action.enabled)
      .forEach((action) => {
        action.models?.forEach((model) => {
          if (model && model.name) {
            allModels[model.name] = model;
          }
        });
        if (action.input?.name && action.input.fields) {
          allModels[action.input.name] = {
            name: action.input.name,
            fields: action.input.fields,
          };
        }
      });
    provider.syncs
      .filter((sync) => sync.enabled)
      .forEach((sync) => {
        sync.models?.forEach((model) => {
          if (model && model.name) {
            allModels[model.name] = model;
          }
        });
      });
  });

  // Process Action Inputs
  nangoData.forEach((provider) => {
    provider.actions
      .filter((action) => action.enabled)
      .forEach((action) => {
        const entry: MakeAgentEntry = {
          provider: provider.provider,
          action: action.name,
          description: cleanDescription(action.description),
        };

        // Include actions even if they have no input model
        if (action.input?.name && action.input.fields) {
          entry.model = action.input.name;
          const paramDescs = paramDescriptions[action.name] || {};
          const visited = new Set<string>();
          collectModels(
            action.input.name,
            allModels,
            makeAgentFormat.actionInputModelsDictionary,
            paramDescs,
            visited,
            true,
          );
        }

        makeAgentFormat.actionInputs.push(entry);
      });
  });

  // Process Action Outputs
  nangoData.forEach((provider) => {
    provider.actions
      .filter((action) => action.enabled)
      .forEach((action) => {
        // Always include the action, even if it has no output
        const paramDescs = paramDescriptions[action.name] || {};

        if (action.returns?.length) {
          // Process each return model
          action.returns.forEach((modelName) => {
            if (modelName) {
              const entry: MakeAgentEntry = {
                provider: provider.provider,
                action: action.name,
                model: modelName,
              };
              makeAgentFormat.actionOutputs.push(entry);

              const visited = new Set<string>();
              collectModels(
                modelName,
                allModels,
                makeAgentFormat.actionOutputModelsDictionary,
                paramDescs,
                visited,
                false,
              );
            }
          });
        } else {
          // Include action with no output model
          const entry: MakeAgentEntry = {
            provider: provider.provider,
            action: action.name,
            description: cleanDescription(action.description),
          };
          makeAgentFormat.actionOutputs.push(entry);
        }
      });
  });

  // Process Sync Outputs
  nangoData.forEach((provider) => {
    provider.syncs
      .filter((sync) => sync.enabled)
      .forEach((sync) => {
        sync.returns?.forEach((modelName) => {
          if (modelName) {
            const entry: MakeAgentEntry = {
              provider: provider.provider,
              sync: sync.name,
              model: modelName,
              description: cleanDescription(sync.description),
            };
            makeAgentFormat.syncOutputs.push(entry);

            const paramDescs = {};
            const visited = new Set<string>();
            collectModels(
              modelName,
              allModels,
              makeAgentFormat.syncOutputModelsDictionary,
              paramDescs,
              visited,
              false,
            );
          }
        });
      });
  });

  return makeAgentFormat;
}

export { transformToMakeAgentFormat };
