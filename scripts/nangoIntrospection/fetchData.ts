import dotenv from "dotenv";
import fs from "fs/promises";
import fsExtra from "fs-extra";
import { NangoProviderData, OpenAIAction } from "./types";

dotenv.config();
const secretKey = process.env.NANGO_SECRET_KEY;

export async function fetchData({ force = false }: { force?: boolean } = {}): Promise<{
  nangoData: NangoProviderData[];
  openAIData: OpenAIAction[];
}> {
  let nangoData: NangoProviderData[];
  let openAIData: OpenAIAction[];

  const nangoFilePath = new URL("./nango-format.json", import.meta.url).pathname;
  const openaiFilePath = new URL("./openai-format.json", import.meta.url).pathname;

  // Fetch or read Nango data
  if (!force) {
    try {
      console.log("Trying to read local Nango config file:", nangoFilePath);
      nangoData = fsExtra.readJSONSync(nangoFilePath);
      console.log("Using local Nango config file");
    } catch (e) {
      console.log("Local Nango file not available, fetching from API");
      nangoData = await fetchNangoData();
    }
  } else {
    console.log("Force flag enabled, fetching Nango data from API");
    nangoData = await fetchNangoData();
  }

  // Fetch or read OpenAI data
  if (!force) {
    try {
      console.log("Trying to read local OpenAI format file:", openaiFilePath);
      const rawOpenAIData = fsExtra.readJSONSync(openaiFilePath);
      openAIData = rawOpenAIData.data || rawOpenAIData;
      console.log("Using local OpenAI format file");
    } catch (e) {
      console.log("Local OpenAI file not available, fetching from API");
      openAIData = await fetchOpenAIData();
    }
  } else {
    console.log("Force flag enabled, fetching OpenAI data from API");
    openAIData = await fetchOpenAIData();
  }

  return { nangoData, openAIData };

  // Helper function to fetch Nango data
  async function fetchNangoData(): Promise<NangoProviderData[]> {
    const response = await fetch("https://api.nango.dev/scripts/config", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${secretKey}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch Nango config: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    try {
      await fs.writeFile(nangoFilePath, JSON.stringify(data, null, 2), "utf8");
      console.log("Saved Nango data to local file for future use");
    } catch (writeErr) {
      console.warn("Failed to save Nango data to local file:", writeErr);
    }

    return data;
  }

  // Helper function to fetch OpenAI data
  async function fetchOpenAIData(): Promise<OpenAIAction[]> {
    const openAIResponse = await fetch("https://api.nango.dev/scripts/config?format=openai", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${secretKey}`,
      },
    });

    if (!openAIResponse.ok) {
      throw new Error(
        `Failed to fetch OpenAI config: ${openAIResponse.status} ${openAIResponse.statusText}`,
      );
    }

    const rawOpenAIData = await openAIResponse.json();
    const data = rawOpenAIData.data || rawOpenAIData;

    try {
      await fs.writeFile(openaiFilePath, JSON.stringify(rawOpenAIData, null, 2), "utf8");
      console.log("Saved OpenAI data to local file for future use");
    } catch (writeErr) {
      console.warn("Failed to save OpenAI data to local file:", writeErr);
    }

    return data;
  }
}
