import { MakeAgentField, MakeAgentModel } from "./types";

export function fieldToZodType(field: MakeAgentField, referenceGroupName: string): string {
  let typeStr: string;

  if (field.union && field.union.length > 1) {
    const options = field.union
      .map((val) => {
        if (val === "null") return "z.null()";
        if (["string", "number", "boolean"].includes(val)) return `z.${val}()`;
        if (typeof val === "string") return `z.literal("${val.replace(/"/g, '\\"')}")`;
        return null;
      })
      .filter((option): option is string => option !== null);
    typeStr = options.length >= 2 ? `z.union([${options.join(", ")}])` : options[0] || "z.any()";
  } else if (typeof field.type === "object" && "$ref" in field.type) {
    typeStr = `z.lazy(() => ${referenceGroupName}["${field.type.$ref}"] || z.any().describe("Lazy load failed: ${field.type.$ref}"))`;
  } else {
    const baseType = field.type as string;
    if (baseType === "object") {
      typeStr = "z.record(z.string(), z.any())";
    } else if (baseType === "string" || baseType === "number" || baseType === "boolean") {
      typeStr = `z.${baseType}()`;
    } else if (baseType === "any") {
      typeStr = "z.any()";
    } else {
      typeStr = `z.literal("${baseType.replace(/"/g, '\\"')}")`;
    }
  }

  if (field.array) {
    typeStr = `z.array(${typeStr})`;
  }
  if (field.optional) {
    typeStr += ".optional()";
  }
  if (field.description) {
    typeStr += `.describe("${field.description.replace(/"/g, '\\"')}")`;
  }

  return typeStr;
}

export function modelToZodSchema(
  model: MakeAgentModel,
  modelName: string,
  referenceGroupName: string,
): string {
  let output = `  "${modelName}": z.object({\n`;
  Object.entries(model).forEach(([fieldName, field]) => {
    try {
      const typeStr = fieldToZodType(field, referenceGroupName);
      output += `    "${fieldName}": ${typeStr},\n`;
    } catch (e) {
      console.error(`Error generating Zod type for field ${fieldName} in model ${modelName}:`, e);
      output += `    "${fieldName}": z.any().describe("Error generating type string"),\n`;
    }
  });
  output += `  }),\n`;
  return output;
}
