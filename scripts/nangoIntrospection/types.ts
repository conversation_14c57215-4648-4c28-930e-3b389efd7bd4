// Nango Format Interfaces
export interface <PERSON><PERSON><PERSON>ield {
  name: string;
  array: boolean;
  value: any;
  tsType: boolean;
  optional: boolean;
  model?: boolean;
  union?: boolean;
}

export interface NangoModel {
  name: string;
  fields: <PERSON>goField[];
}

export interface NangoEndpoint {
  method: string;
  path: string;
  group: string | null;
}

export interface NangoAction {
  name: string;
  type: string;
  returns: string[];
  description: string;
  endpoints: NangoEndpoint[];
  input?: { name: string; fields: NangoField[] };
  models: NangoModel[];
  enabled: boolean;
}

export interface NangoSync {
  name: string;
  type: string;
  returns: string[];
  description: string;
  endpoints: NangoEndpoint[];
  models: NangoModel[];
  enabled: boolean;
}

export interface NangoProviderData {
  providerConfigKey: string;
  provider: string;
  actions: NangoAction[];
  syncs: NangoSync[];
}

// OpenAI Format Interfaces
export interface OpenAIParameter {
  type: string;
  description?: string;
  items?: { type: string };
  enum?: string[];
}

export interface OpenAIAction {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, OpenAIParameter>;
    required: string[];
  };
}

// MakeAgent Format Interfaces
export interface MakeAgentField {
  type: string | { $ref: string };
  optional: boolean;
  description?: string;
  array?: boolean;
  union?: string[];
}

export interface MakeAgentModel {
  [fieldName: string]: MakeAgentField;
}

export interface MakeAgentEntry {
  provider: string;
  action?: string;
  sync?: string;
  model?: string;
  description?: string;
}

export interface MakeAgentFormat {
  actionInputs: MakeAgentEntry[];
  actionInputModelsDictionary: Record<string, MakeAgentModel>;
  actionOutputs: MakeAgentEntry[];
  actionOutputModelsDictionary: Record<string, MakeAgentModel>;
  syncOutputs: MakeAgentEntry[];
  syncOutputModelsDictionary: Record<string, MakeAgentModel>;
}

// Formatter Output Types
export type FormatterOutput = {
  content: string;
  requiresZod: boolean;
};
