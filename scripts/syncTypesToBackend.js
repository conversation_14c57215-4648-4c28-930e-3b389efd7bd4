#!/usr/bin/env node
// This script copies protocol type definitions from frontend to backend (Deno) environment
// Handles path adjustments for Deno imports (.ts extension required)

import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Absolute paths for source and target
const SOURCE_DIR = path.resolve(__dirname, '../packages/ma-next/src/types/protocol');
const TARGET_DIR = path.resolve(__dirname, '../packages/ma-next/supabase/functions/_protocol');

// Make sure target directory exists
if (!fs.existsSync(TARGET_DIR)) {
  fs.mkdirSync(TARGET_DIR, { recursive: true });
}

// Get all TypeScript files except vercel.ts (since it only belongs on backend)
const files = fs.readdirSync(SOURCE_DIR).filter(file => {
  return file.endsWith('.ts') && file !== 'vercel.ts';
});

console.log('Frontend → Backend: Copying protocol types...');

for (const file of files) {
  // Read the source file
  const sourceContent = fs.readFileSync(path.join(SOURCE_DIR, file), 'utf8');

  // For Deno, update any imports from relative paths to use .ts extension
  const denoContent = sourceContent.replace(
    /from\s+['"]([\.\/][^'"]+)['"]/g,
    (match, importPath) => {
      // Only add .ts if not already present
      if (!importPath.endsWith('.ts')) {
        return `from '${importPath}.ts'`;
      }
      return match;
    }
  );

  // Write to the target file
  fs.writeFileSync(path.join(TARGET_DIR, file), denoContent, 'utf8');
  console.log(`✓ ${file}`);
}

console.log('Frontend → Backend: Protocol types synced successfully.\n');
