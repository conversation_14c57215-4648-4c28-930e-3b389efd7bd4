import { spawnSync } from 'node:child_process';
import { fileURLToPath } from 'node:url';

const schemaPath = fileURLToPath(
  new URL('../packages/supabase/schema.prisma', import.meta.url)
);
const sqlPath = fileURLToPath(
  new URL(
    '../packages/supabase/migrations/functions_permissions_cron.sql',
    import.meta.url
  )
);

function run(command: string, args: string[]): number {
  const result = spawnSync(command, args, { stdio: 'inherit' });
  return result.status ?? 0;
}

const pushStatus = run('pnpm', ['prisma', 'db', 'push', `--schema=${schemaPath}`]);

if (pushStatus !== 0) {
  console.error(`prisma db push failed with exit code ${pushStatus}. Aborting migration.`);
  process.exit(pushStatus);
}

const execStatus = run('pnpm', [
  'prisma',
  'db',
  'execute',
  `--file=${sqlPath}`,
  `--schema=${schemaPath}`,
]);
process.exit(execStatus);
