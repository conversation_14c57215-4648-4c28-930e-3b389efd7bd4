import { <PERSON><PERSON> } from "@nangohq/node";
import dotenv from "dotenv";

dotenv.config();

const nangoSecretKey = process.env.NANGO_SECRET_KEY;
const CONNECTION_ID = process.env.NANGO_CONNECTION_ID;
const PROVIDER_CONFIG_KEY = process.env.NANGO_PROVIDER_CONFIG_KEY;

if (!nangoSecretKey) {
  console.error("Error: NANGO_SECRET_KEY is not set in environment variables.");
  process.exit(1);
}

const nango = new Nango({ secretKey: nangoSecretKey });

async function main() {
  /*
    nango.listRecords()
  */
  // console.log("--- Running Active Example: List Records ---");

  // console.log("Provider Config Key:", PROVIDER_CONFIG_KEY);
  // console.log("Connection ID:", CONNECTION_ID);
  // try {
  //   const result = await nango.listRecords({
  //     providerConfigKey: PROVIDER_CONFIG_KEY!,
  //     connectionId: CONNECTION_ID!,
  //     model: "GmailEmail",
  //     modifiedAfter: "2025-01-18T00:00:00Z",
  //   });
  //   console.log("List Records Result:", JSON.stringify(result, null, 2));
  // } catch (error: any) {
  //   console.error("Error in List Records:", error.message || error);
  // }
  // console.log("-------------------------------------------------");

  /*
    BASIC EXAMPLE: PROXY REQUEST
  */
  console.log("--- Running Active Example: Basic Proxy Request ---");
  try {
    const result = await nango.proxy({
      method: "GET",
      endpoint: "/gmail/v1/users/me/messages/196e262307ab25bc",
      connectionId: CONNECTION_ID,
      providerConfigKey: PROVIDER_CONFIG_KEY,
      retries: 3,
      headers: {
        "Content-Type": "application/json",
      },
      data: null,
    });
    console.log("Basic Proxy Request Result:", JSON.stringify(result.data, null, 2));
  } catch (error: any) {
    console.error("Error in Basic Proxy Request:", error || error);
  }
  console.log("-------------------------------------------------");

  /*
    BASIC EXAMPLE: POST REQUEST
  */
  // console.log('--- Running Active Example: Basic Proxy Request ---');
  // try {
  //   const result = await nango.post({
  //     endpoint: '/',
  //     connectionId: CONNECTION_ID,
  //     providerConfigKey: PROVIDER_CONFIG_KEY,
  //     baseUrlOverride: 'https://cz7p3xqz1wg0000aq3dggxh87ocyyyyyd.oast.pro',
  //     retries: 3,
  //     headers: {
  //       'Content-Type': 'application/json',
  //     },
  //   });
  //   console.log('Basic Proxy Request Result:', JSON.stringify(result.data, null, 2));
  // } catch (error: any) {
  //   console.error('Error in Basic Proxy Request:', error.message || error);
  // }
  // console.log('-------------------------------------------------');

  /*
    EXAMPLE: Fetching Tasks for Project
  */
  // console.log("--- Fetching Tasks for Project ---");
  // try {
  //   // First, get the account ID
  //   const accountResponse = await nango.proxy({
  //     method: "GET",
  //     endpoint: "/users/me",
  //     connectionId: CONNECTION_ID,
  //     providerConfigKey: PROVIDER_CONFIG_KEY,
  //   });

  //   const accountId = accountResponse.data.company.id;
  //   console.log("Account ID:", accountId);

  //   // Now fetch tasks for the project
  //   const projectId = ********; // MakeAgent project ID from previous test
  //   const tasksResponse = await nango.proxy({
  //     method: "GET",
  //     endpoint: "/v2/tasks",
  //     connectionId: CONNECTION_ID,
  //     providerConfigKey: PROVIDER_CONFIG_KEY,
  //     headers: {
  //       "Harvest-Account-Id": accountId
  //     }
  //   });

  //   console.log("Tasks:", JSON.stringify(tasksResponse.data, null, 2));
  // } catch (error: any) {
  //   console.error("Error fetching tasks:", error.message || error);
  // }
  // console.log("-------------------------------------------------");

  /*
    EXAMPLE: Proxy Request with Base URL Override
  */
  /*
  console.log("--- Example: Proxy Request with Base URL Override ---");
  const requestBinUrl = "<request-bin-url>"; // e.g., https://eo{...}.m.pipedream.net
  try {
    const result = await nango.proxy({
      method: "POST", // Or GET, depending on what the request bin expects
      endpoint: "/inspect", // The path on your request bin
      connectionId: CONNECTION_ID,
      providerConfigKey: PROVIDER_CONFIG_KEY,
      baseUrlOverride: requestBinUrl,
      retries: 1,
      data: { message: "Hello from Nango proxy override!" }, // Example payload
    });
    console.log("Base URL Override Result:", JSON.stringify(result.data, null, 2));
    console.log(`Check your request bin at ${requestBinUrl} for the incoming request.`);
  } catch (error: any) {
    console.error("Error in Base URL Override Request:", error.message || error);
  }
  console.log("---------------------------------------------------");
  */

  /*
    EXAMPLE: Proxy Request with Connection ID from Env
  */
  /*
  console.log("--- Example: Proxy Request with Connection ID from Env ---");
  if (CONNECTION_ID) {
    try {
      const result = await nango.proxy({
        method: "GET",
        endpoint: "/some/other/endpoint",
        connectionId: CONNECTION_ID, // Using ID from .env
        providerConfigKey: PROVIDER_CONFIG_KEY,
        retries: 3,
      });
      console.log("Proxy Request (Env Connection ID) Result:", JSON.stringify(result.data, null, 2));
    } catch (error: any) {
      console.error("Error in Proxy Request (Env Connection ID):", error.message || error);
    }
  } else {
     console.log("Skipping: NANGO_CONNECTION_ID not set in environment variables.");
  }
  console.log("---------------------------------------------------------");
  */

  /*
    EXAMPLE: Inspecting a Connection using Nango API
  */
  /*
  console.log("--- Example: Inspecting a Connection using Nango API ---");
  try {
    // Note: This uses fetch directly to the Nango Management API, not the proxy method.
    const response = await fetch(`https://api.nango.dev/connection/${CONNECTION_ID}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${nangoSecretKey}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Nango API Error: ${response.status} ${response.statusText}`);
    }

    const connectionDetails = await response.json();
    console.log("Connection Details:", JSON.stringify(connectionDetails, null, 2));

  } catch (error: any) {
    console.error("Error fetching connection details:", error.message || error);
  }
  console.log("-------------------------------------------------------");
  */

  /*
    EXAMPLE: Listing Connections using Nango API
  */
  /*
  console.log("--- Example: Listing Connections using Nango API ---");
   try {
    const response = await fetch(`https://api.nango.dev/connection`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${nangoSecretKey}`,
      },
    });

     if (!response.ok) {
      throw new Error(`Nango API Error: ${response.status} ${response.statusText}`);
    }

     const connectionsList = await response.json();
    console.log("Connections List:", JSON.stringify(connectionsList, null, 2));

   } catch (error: any) {
    console.error("Error fetching connections list:", error.message || error);
  }
  console.log("---------------------------------------------------");
  */
}

main().catch(console.error);
