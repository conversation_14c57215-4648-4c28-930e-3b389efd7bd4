import type {
  CoreAssistantMessage,
  CoreMessage,
  CoreSystemMessage,
  CoreToolMessage,
  CoreUserMessage,
  TextPart,
  ToolCallPart,
  ToolResultPart,
} from "ai";

type VercelMessageRole = "user" | "assistant" | "system" | "tool";

type VercelTextPart = Pick<TextPart, "type" | "text">;
type VercelToolCallPart = Pick<ToolCallPart, "type" | "toolCallId" | "toolName"> & {
  args: any; // vercel: unknown
};
type VercelToolResultPart = Pick<ToolResultPart, "type" | "toolCallId" | "toolName" | "result">;

type VercelSystemMessage = Pick<CoreSystemMessage, "role" | "content">;
type VercelUserMessage = Pick<CoreUserMessage, "role" | "content">;
type VercelAssistantMessage = Pick<CoreAssistantMessage, "role" | "content">;
type VercelToolMessage = Pick<CoreToolMessage, "role" | "content">;

type VercelMessage =
  | VercelSystemMessage
  | VercelUserMessage
  | VercelAssistantMessage
  | VercelToolMessage;

type VercelStreamParams = {
  messages: VercelMessage[];
  threadId?: string;
  resourceId?: string;
};

type VercelCoreMessage = CoreMessage;

export type {
  VercelAssistantMessage,
  VercelCoreMessage,
  VercelMessage,
  VercelMessageRole,
  VercelStreamParams,
  VercelSystemMessage,
  VercelTextPart,
  VercelToolCallPart,
  VercelToolMessage,
  VercelToolResultPart,
  VercelUserMessage,
};

export { vercel } from "./vercelConverter";
