import type { UserMessage } from "../../../packages/ma-next/src/chat/protocol/messages";
import type { Tool<PERSON>allContent, ToolResultContent } from "../../../packages/ma-next/src/chat/protocol/content";
import type { Tool<PERSON>allPart, ToolResultPart } from "../../../packages/ma-next/src/chat/protocol/parts";
import type { ToolCall, ToolResult } from "../../../packages/ma-next/src/chat/protocol/tools";
import {
  VercelAssistantMessage,
  VercelSystemMessage,
  VercelToolCallPart,
  VercelToolMessage,
  VercelToolResultPart,
  VercelUserMessage,
} from "./vercel";

const vercel = {
  userMessage(input: string | UserMessage): VercelUserMessage {
    const content = typeof input === "string" ? input : input.content;
    return {
      role: "user",
      content: content,
    };
  },

  systemMessage(input: string): VercelSystemMessage {
    return {
      role: "system",
      content: input,
    };
  },

  toolResultMessage(input: ToolResultPart | ToolResultContent): VercelToolMessage {
    const toolResult: ToolResult = "content" in input ? input.content : input;
    const vercelToolResultPart: VercelToolResultPart = {
      type: "tool-result",
      toolCallId: toolResult.toolCallId,
      toolName: toolResult.toolName,
      result: toolResult.result,
    };
    return {
      role: "tool",
      content: [vercelToolResultPart],
    };
  },

  toolCallMessage(input: ToolCallPart | ToolCallContent): VercelAssistantMessage {
    const toolCall: ToolCall = "content" in input ? input.content : input;
    const vercelToolCallPart: VercelToolCallPart = {
      type: "tool-call",
      toolCallId: toolCall.toolCallId,
      toolName: toolCall.toolName,
      args: toolCall.args,
    };
    return {
      role: "assistant",
      content: [vercelToolCallPart],
    };
  },
};

export { vercel };
