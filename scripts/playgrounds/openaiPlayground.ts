import { OpenAI } from "openai";
import dotenv from "dotenv";
dotenv.config();

const openai = new OpenAI({
});

const tools = [{
  type: "function",
  name: "get_weather",
  description: "Get current temperature for provided coordinates in celsius.",
  parameters: {
    type: "object",
    properties: {
      latitude: { type: "number" },
      longitude: { type: "number" },
    },
    required: ["latitude", "longitude"],
    additionalProperties: true,
  },
  strict: false,
}];

const input = [
  {
    role: "user",
    content:
      "What's the weather like in Paris today? Can you say hello and introduce yourself first?",
  },
];

const response = await openai.responses.create({
  model: "gpt-4.1",
  input,
  tools,
});

console.log(JSON.stringify(response.output, null, 2));
