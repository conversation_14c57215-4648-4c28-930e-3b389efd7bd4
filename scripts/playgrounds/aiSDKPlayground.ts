import { openai } from "@ai-sdk/openai";
import { generateObject } from "ai";
import { z } from "zod";
import dotenv from "dotenv";
dotenv.config();

const modelSchema = z.object({
  summary: z.string(),
  description: z.string().optional(),
  location: z.string().optional(),
  start: z.string(),
  end: z.string(),
  timeZone: z.string().optional(),
  attendees: z.array(z.string()).optional(),
});

const { object: repairedArgs } = await generateObject({
  model: openai("gpt-4o"),
  schema: modelSchema,
  prompt: [
    `A model tried to call a tool` +
    ` with the following arguments:`,
    JSON.stringify({
      end$: "2025-05-20T11:00:00+12:00",
      start$: "2025-05-20T10:00:00+12:00",
      asdummary$: "Team Sync Meeting",
      placeToMeet$: "Conference Room 1",
      timezone$: "Pacific/Auckland",
      attendees$: "<EMAIL>",
    }),
    `The tool accepts the following schema:`,
    JSON.stringify(modelSchema.shape),
    "Please fix the arguments.",
  ].join("\n"),
});

console.log("Repaired Args:", repairedArgs);

// async function actionsAgent(messages: any[]) {
//   try {
//     console.log("Starting actionsAgent with messages:", messages);

//     const result = streamText({
//       model: openai.responses("gpt-4.1"),
//       system: `

// `,
//       messages,
//       providerOptions: {
//         openai: {
//           // previousResponseId: "resp_68212dfecca48191881018db2c6fbbc80b905778e4e51e85",
//           strictSchemas: false,
//         },
//       },
//       tools: {
//         actionCall: tool({
//           description: "Call an action",
//           parameters: jsonSchema({
//             "type": "object",
//             "properties": {
//               "actionName": {
//                 "type": "string",
//                 "description": "The name of the action to call",
//               },
//               "actionParameters": {
//                 "type": "object",
//                 "description": "Pass a few random parameters",
//                 "additionalProperties": true,
//               },
//             },
//             "additionalProperties": false,
//             "required": [
//               "actionName",
//               "actionParameters"
//             ],
//           }),
//         }),
//       },
//     });

//     const stream = result.toDataStream();
//     // Create a reader for the stream
//     const reader = stream.getReader();

//     // Function to read and log stream chunks
//     async function readStream() {
//       while (true) {
//         const { done, value } = await reader.read().catch((error) => {
//           console.error("Error reading stream:", error);
//           throw error;
//         });
//         if (done) {
//           console.log("Stream completed");
//           break;
//         }
//         // Convert the chunk (Uint8Array) to string and log it
//         const chunk = new TextDecoder().decode(value);
//         console.log("Response chunk:", chunk);
//       }
//     }

//     // Start reading the stream
//     await readStream().catch((error) => {
//       console.error("Error in readStream:", error);
//       throw error;
//     });

//     console.log((await result.providerMetadata)?.openai?.responseId)

//     return undefined;
//   } catch (error) {
//     console.error("Fatal error in actionsAgent:", error);
//     throw error;
//   }
// }

// export { actionsAgent };

// // Example usage
// (async () => {
//   try {
//     console.log("Executing actionsAgent");
//     await actionsAgent([
//       vercel.userMessage(
//         "What messages exist in this conversation so far?",
//       ),
//       // vercel.toolCallMessage({
//       //   toolCallId: "call_vdY7nqZA1mbmS0ufPnFZusn2",
//       //   toolName: "actionCall",
//       //   args: {
//       //     actionName: "test",
//       //     args: {},
//       //   },
//       // }),
//       // vercel.toolResultMessage({
//       //   toolCallId: "call_vdY7nqZA1mbmS0ufPnFZusn2",
//       //   toolName: "actionCall",
//       //   result: "this was a success",
//       // }),
//     ]);
//     console.log("actionsAgent execution completed");
//   } catch (error) {
//     console.error("Error in main execution:", error);
//   }
// })();
