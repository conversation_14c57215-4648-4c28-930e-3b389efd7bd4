import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase credentials in environment variables');
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

const { data: existingData, error: selectError } = await supabaseAdmin
  .from('emcpe_servers')
  .select('userId', { head: true })
  .eq('userId', 'ffefe187-a907-4c7b-926d-1ffd2d645f7e')
  .single();

console.log('Supabase Playground', { existingData, selectError });
