import { Mastra<PERSON>lient } from "@mastra/client-js";
import { openai } from "@ai-sdk/openai";

import dotenv from "dotenv";
import {
  convertToCoreMessages,
  CoreMessage,
  createDataStream,
  createDataStreamResponse,
  DataStreamPart,
  formatDataStreamPart,
  parseDataStreamPart,
  pipeDataStreamToResponse,
  streamText,
  ToolCall,
} from "ai";
import { callChatApi, Message } from "@ai-sdk/ui-utils";
// import * as aiSDKSlashUiUtils from "@ai-sdk/ui-utils";
import { z } from "zod";
import {StreamParams} from 'https://esm.sh/@mastra/client-js@0.1.13-alpha.3';
// import {  } from "@ai-sdk/react";

dotenv.config();

async function getUIMessages(stream: Response) {
  const messages: Message[] = [];
  await callChatApi({
    api: "",
    body: {} as Record<string, any>,
    onFinish: ({ toolInvocations, reasoning, data, ...result }) => {
      messages.push(result);
    },
    onUpdate: (options) => {
      console.log("Update", options);
    },
    restoreMessagesOnFailure: () => {},
    onToolCall: () => {},
    generateId: () => "",
    streamProtocol: undefined,
    credentials: undefined,
    headers: undefined,
    abortController: undefined,
    onResponse: undefined,
    fetch: async () => stream,
    lastMessage: undefined,
  });
  return messages;
}

// MESSAGE RECEIPT FROM UI => MASTRA
// console.log(JSON.stringify(
//   convertToCoreMessages(
//     [{ role: "user", content: "test", parts: [{ type: "text", text: "test" }] }],
//   ),
//   null,
//   2,
// ));

const result = streamText({
//   model: openai("gpt-4o-mini"),
//   messages: [{
//     role: "user",
//     content: "Please write a poem. Then call testTool. Then caht about it.",
//   }],
  async onFinish(result) {
    result.response.messages
    console.log("messages", JSON.stringify(response.messages, null, 2));
  },
  onChunk({ chunk }) {
    if (chunk.type === "tool-call") {
    }
  }
//   onError: (error) => {
//     console.log("error", error);
//   },
//   maxSteps: 5,
//   tools: {
//     testTool: {
//       type: "function",
//       description: "A test tool",
//       parameters: z.object({
//         param1: z.string(),
//         param2: z.number(),
//       }),
//       execute: async (args) => {
//         return `Param1: ${args.param1}, Param2: ${args.param2}`;
//       },
//     },
//   },
// });

// //  api: string;
// //     body: Record<string, any>;
// //     streamProtocol: 'data' | 'text' | undefined;
// //     credentials: RequestCredentials | undefined;
// //     headers: HeadersInit | undefined;
// //     abortController: (() => AbortController | null) | undefined;
// //     restoreMessagesOnFailure: () => void;
// //     onResponse: ((response: Response) => void | Promise<void>) | undefined;
// //     onUpdate: (options: {
// //         message: UIMessage;
// //         data: JSONValue[] | undefined;
// //         replaceLastMessage: boolean;
// //     }) => void;
// //     onFinish: UseChatOptions['onFinish'];
// //     onToolCall: UseChatOptions['onToolCall'];
// //     generateId: IdGenerator;
// //     fetch: ReturnType<typeof getOriginalFetch$1> | undefined;
// //     lastMessage: UIMessage | undefined;

// for await (const chunk of result.toDataStream()) {
//   const reader = new TextDecoder().decode(chunk);
//   console.log(reader);
// }

// processChatResponse(result);

// await new Promise((r) => setTimeout(r, 100000));

// Create a Mastra client
const mastra = new MastraClient({
  baseUrl: process.env.MASTRA_URL!,
});

// Get the playground agent
const agent = mastra.getAgent("playgroundAgent");

// Main function to handle the conversation with recursive tool calls
async function handleToolCalls() {
  // INCOMING MESSAGE
  // const { message } = req.json();
  // const messages: any[] = convertToCoreMessages(
  //   [{ role: "user", content: "Please write a Haiku, then play the game" }],
  // );

  const messages = [{ role: "user", content: "Please write a Haiku, then play the game" }];

  // THIS IS NOT REQUIRED BECAUSE MASTRA HANDLES CORE MESSAGE and MESSAGE (UI) types
  // // Initialize the conversation with a user message
  // const messages: CoreMessage[] = convertToCoreMessages(
  //   [
  //     {
  //       "createdAt": new Date(),
  //       "role": "assistant",
  //       "content":
  //         "Whispers of the breeze,  \nLeaves dance in the golden light,  \nAutumn's gentle sigh.  \n\nNow, let's start the guessing game! I'll make my first guess.",
  //       "parts": [
  //         {
  //           "type": "step-start",
  //         },
  //         {
  //           "type": "text",
  //           "text":
  //             "Whispers of the breeze,  \nLeaves dance in the golden light,  \nAutumn's gentle sigh.  \n\nNow, let's start the guessing game! I'll make my first guess.",
  //         },
  //         // {
  //         //   "type": "tool-invocation",
  //         //   "toolInvocation": {
  //         //     "state": "call",
  //         //     "step": 0,
  //         //     "toolCallId": "call_sfJ9Z9gU1hJeMLfaIQIGmXFT",
  //         //     "toolName": "guessTool",
  //         //     "args": {
  //         //       "guess": 50,
  //         //     },
  //         //   },
  //         // },
  //         {
  //           "type": "tool-invocation",
  //           "toolInvocation": {
  //             "state": "result",
  //             "toolCallId": "call_sfJ9Z9gU1hJeMLfaIQIGmXFT",
  //             "toolName": "guessTool",
  //             "args": {
  //               "guess": 50,
  //             },
  //             "result": "You guessed wrong. Your guess is too high.",
  //           },
  //         },
  //       ],
  //       "annotations": [
  //         {
  //           "type": "confirm-tool",
  //           "toolCallId": "call_sfJ9Z9gU1hJeMLfaIQIGmXFT",
  //         },
  //       ],
  //     },
  //   ],
  // );

  let conversationTurns = 0;
  const MAX_TURNS = 1; // Safety limit to prevent infinite loops

  // Continue the conversation until it naturally ends or hits the safety limit
  while (conversationTurns < MAX_TURNS) {
    conversationTurns++;
    console.log(`\n--- Conversation Turn ${conversationTurns} ---`);
    console.log("Sending messages:", JSON.stringify(messages, null, 2));

    // Get a response from the agent
    const stream = await agent.stream({ messages });

    function createStreamMiddleware(
      inputStream: ReadableStream<Uint8Array>,
    ): ReadableStream<Uint8Array> {
      const textDecoder = new TextDecoder();

      const transformStream = new TransformStream<Uint8Array, Uint8Array>({
        transform(chunk, controller) {
          // We only ever follow with custom chunks
          controller.enqueue(chunk);

          const decoded = textDecoder.decode(chunk);

          decoded.split("\n").filter(Boolean).forEach((line) => {
            const part = parseDataStreamPart(line);

            switch (part.type) {
              case "tool_call": {
                controller.enqueue(
                  new TextEncoder().encode(
                    '8:[{"type":"confirm-tool", "toolCallId": "' + part.value.toolCallId + '"}]\n',
                  ),
                );
                // pendingToolCalls.push(part.value);
                break;
              }
            }
          });
        },
        flush() {
          // Handle any cleanup when the stream is done
        },
      });

      // Pipe the input stream through the transform stream
      return inputStream.pipeThrough(transformStream);
    }

    const myStream = createStreamMiddleware(stream.body!);

    getUIMessages({ ok: true, body: myStream } as unknown as Response).then((result) =>
      // SAVE THE MESSAGES TO THE DB.
      console.log(JSON.stringify(result, null, 2))
    );

    const myStream = createDataStream({
      execute: async (dataStream) => {
        const reader = stream.body!.getReader();
        while (true) {
          const { done, value: encoded } = await reader.read();
          if (done) {
            break;
          }
          const decoded = new TextDecoder().decode(encoded);

          decoded.split("\n").filter(Boolean).forEach((line) => {
            // console.log("original", line);
            const part = parseDataStreamPart(line);
            // console.log("parsed", part);

            messageParts.push(part);

            dataStream.write(formatDataStreamPart(part.type, part.value));

            switch (part.type) {
              case "tool_call": {
                dataStream.writeMessageAnnotation({ boom: "it works" });
                // pendingToolCalls.push(part.value);
                break;
              }
            }
          });
        }
      },
    });

    // for await (const chunk of myStream) {
    //   console.log("chunk", chunk);
    // }

    // console.log(
    //   "HERE",
    //   await getUIMessages(stream),
    // );

    // console.log(convertToC)

    // reader.releaseLock();

    // // Process the stream to collect the agent's response
    // await new Promise<void>((resolve, reject) => {
    //   createDataStream({
    //     execute: async (dataStream) => {
    //       stream.processDataStream({
    //         onTextPart: (text) => {
    //           dataStream.write();
    //         },
    //         onToolCallPart: (toolCall) => {
    //           pendingToolCalls.push({
    //             type: "tool-call",
    //             ...toolCall,
    //           });
    //           console.log("\nTool Call:", JSON.stringify(toolCall, null, 2));
    //         },
    //         onFinishStepPart: () => {
    //           console.log(); // Add a newline after all text is printed
    //           resolve();
    //         },
    //         onErrorPart: (error) => {
    //           console.error("Stream error:", error);
    //           reject(error);
    //         },
    //       });
    //     },
    //   });
    // });

    // Add the assistant's text response to the conversation
    // if (currentText.trim()) {
    //   messages.push({ role: "assistant", content: currentText });
    // }

    // // If there are no tool calls, end the conversation
    // if (pendingToolCalls.length === 0) {
    //   console.log("No tool calls detected, ending conversation.");
    //   break;
    // }

    // // Process each tool call
    // for (const toolCall of pendingToolCalls) {
    //   console.log(`\nProcessing tool call: ${toolCall.toolName}`);

    //   // Add the tool call to the conversation
    //   messages.push({
    //     role: "assistant",
    //     content: [toolCall],
    //   });

    //   // Execute the tool and get the response
    //   const toolResponse = await executeToolCall(toolCall);
    //   console.log(`Tool Response: ${toolResponse}`);

    //   // Add the tool response to the conversation
    //   messages.push({
    //     role: "tool",
    //     content: [{
    //       type: "tool-result",
    //       toolCallId: toolCall.toolCallId,
    //       toolName: toolCall.toolName,
    //       result: toolResponse,
    //     }],
    //   });
    // }
    conversationTurns++;
  }

  if (conversationTurns >= MAX_TURNS) {
    console.log("\nReached maximum conversation turns limit.");
  }

  console.log("\nConversation completed.");
}

// Function to execute a tool call
async function executeToolCall(toolCall: ToolCall<string, any>): Promise<string> {
  console.log(
    `Executing tool: ${toolCall.toolName} with parameters:`,
    toolCall.args,
  );

  // Simulate tool execution for the guessing game
  if (toolCall.toolName === "guessTool") {
    const guess = (toolCall.args as { guess: number }).guess;

    // Simulate a guessing game where 42 is the correct answer
    if (guess === 42) {
      return "You guessed right! The answer is 42.";
    } else if (guess < 42) {
      return "You guessed wrong. Your guess is too low.";
    } else {
      return "You guessed wrong. Your guess is too high.";
    }
  }

  return `Unknown tool '${toolCall.toolName}' or error executing tool`;
}

// Start the recursive tool call handling process
console.log("Starting the guessing game conversation...");
handleToolCalls().catch((error) => {
  console.error("Error in handleToolCalls:", error);
});

// return createDataStreamResponse({
//   execute: async (dataStream) => {
//     const mastraClientStreamResponse = await client.getAgent(agentName).stream({
//       messages: [message] as StreamParams["messages"],
//       threadId: id,
//       resourceId: user.id,
//     });

//     // Add annotations / respond to stream
//     mastraClientStreamResponse.processDataStream({
//       onToolCallPart: (toolCall) => {
//         dataStream.writeMessageAnnotation(
//           JSON.stringify({ type: "toolCall", someServerSideMagic: "foo" }),
//         );
//       },
//       onToolResultPart(toolResult) {
//         dataStream.writeMessageAnnotation(
//           JSON.stringify({ type: "toolResult", otherServerSideMagic: "bar" }),
//         );
//       },
//     });

//     // mergeIntoDataStream (userland)
//     const reader = mastraClientStreamResponse.body!.getReader();
//     while (true) {
//       const { done, value: encoded } = await reader.read();
//       if (done) {
//         break;
//       }

//       new TextDecoder().decode(encoded).split("\n").filter(Boolean).forEach((line) => {
//         const part = parseDataStreamPart(line);
//         dataStream.write(formatDataStreamPart(part.type, part.value));
//       });
//     }
//   },
// });
