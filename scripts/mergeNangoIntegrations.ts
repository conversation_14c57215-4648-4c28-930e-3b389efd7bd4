#!/usr/bin/env npx tsx

import fs from 'node:fs';
import path from 'node:path';
import yaml from 'js-yaml';
import { execSync } from 'node:child_process';
import { fileURLToPath } from 'node:url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BASE_DIR = path.resolve(__dirname, '../packages');
const TARGET_DIR = path.resolve(BASE_DIR, 'nango-integrations');
const TARGET_YAML_PATH = path.resolve(TARGET_DIR, 'nango.yaml');
const SOURCE_DIRS_CONFIG = [
  { name: 'emcpe-nango-integrations', path: path.resolve(BASE_DIR, 'emcpe-nango-integrations') },
];

interface NangoIntegration {
  providers?: string | string[];
  syncs?: Record<string, any>;
  actions?: Record<string, any>;
}

interface NangoYaml {
  integrations: Record<string, NangoIntegration>;
  models: Record<string, any>;
}

const mergedYaml: NangoYaml = {
  integrations: {},
  models: {},
};

console.log('Starting YAML merge...');

SOURCE_DIRS_CONFIG.forEach(sourceConfig => {
  if (sourceConfig.name === 'emcpe-nango-integrations') {
    console.log(`Processing providers within ${sourceConfig.name}...`);
    if (!fs.existsSync(sourceConfig.path)) {
      console.warn(`Skipping ${sourceConfig.name}: Source directory not found.`);
      return;
    }
    const providerDirs = fs
      .readdirSync(sourceConfig.path, { withFileTypes: true })
      .filter(
        dirent =>
          dirent.isDirectory() &&
          !dirent.name.startsWith('.') &&
          !['node_modules', 'dist', '.github'].includes(dirent.name)
      );

    providerDirs.forEach(providerDir => {
      const providerName = providerDir.name;
      const providerYamlPath = path.join(sourceConfig.path, providerName, 'nango.yaml');

      if (!fs.existsSync(providerYamlPath)) {
        console.warn(`  Skipping ${providerName}: nango.yaml not found at ${providerYamlPath}`);
        return;
      }

      try {
        console.log(`  Processing ${providerName}/nango.yaml...`);
        const providerYamlContent = fs.readFileSync(providerYamlPath, 'utf8');
        const providerData = yaml.load(providerYamlContent) as NangoYaml; // Structure: { integrations: { providerName: {...} }, models: {...} }

        // Merge integration actions/syncs for this specific provider
        if (providerData.integrations && providerData.integrations[providerName]) {
          const intValue = providerData.integrations[providerName];
          if (!mergedYaml.integrations[providerName]) {
            mergedYaml.integrations[providerName] = {}; // Initialize if not exists
          }
          Object.entries(intValue).forEach(([propKey, propValue]) => {
            if (propKey === 'syncs' || propKey === 'actions') {
              mergedYaml.integrations[providerName][propKey] = {
                ...(mergedYaml.integrations[providerName][propKey] || {}),
                ...propValue,
              };
              console.log(`    Merged ${propKey} for integration "${providerName}"`);
            } else {
              // Merge other top-level integration properties if any (like 'providers')
              mergedYaml.integrations[providerName][propKey] = propValue;
            }
          });
        } else {
          console.warn(
            `    Warning: No 'integrations.${providerName}' section found in ${providerName}/nango.yaml`
          );
        }

        // Merge models
        if (providerData.models) {
          Object.entries(providerData.models).forEach(([modelKey, modelValue]) => {
            if (mergedYaml.models[modelKey]) {
              // Optional: Deep compare before warning? For now, simple check is fine.
              // console.warn(`    Warning: Model "${modelKey}" from ${providerName} overwrites existing definition.`);
            }
            mergedYaml.models[modelKey] = modelValue;
          });
          console.log(`    Merged models from ${providerName}`);
        }
      } catch (error) {
        console.error(`  Error processing ${providerName}/nango.yaml:`, error);
      }
    });
  } else {
    // Handle makeagent-nango-integrations (or others) as before
    const sourceYamlPath = path.join(sourceConfig.path, 'nango.yaml');
    if (!fs.existsSync(sourceYamlPath)) {
      console.warn(`Skipping ${sourceConfig.name}: nango.yaml not found at ${sourceYamlPath}`);
      return; // Use return instead of continue in forEach context
    }
    try {
      console.log(`Processing ${sourceConfig.name}/nango.yaml...`);
      const sourceYamlContent = fs.readFileSync(sourceYamlPath, 'utf8');
      const sourceData = yaml.load(sourceYamlContent) as NangoYaml;

      if (sourceData.integrations) {
        Object.entries(sourceData.integrations).forEach(([intKey, intValue]) => {
          if (!mergedYaml.integrations[intKey]) {
            mergedYaml.integrations[intKey] = {}; // Initialize if not exists
          }
          Object.entries(intValue).forEach(([propKey, propValue]) => {
            if (propKey === 'syncs' || propKey === 'actions') {
              mergedYaml.integrations[intKey][propKey] = {
                ...(mergedYaml.integrations[intKey][propKey] || {}),
                ...propValue,
              };
              console.log(`  Merged ${propKey} for integration "${intKey}"`);
            } else {
              mergedYaml.integrations[intKey][propKey] = propValue;
            }
          });
        });
      }

      if (sourceData.models) {
        Object.entries(sourceData.models).forEach(([modelKey, modelValue]) => {
          if (mergedYaml.models[modelKey]) {
            console.warn(
              `  Warning: Model "${modelKey}" from ${sourceConfig.name} overwrites existing definition.`
            );
          }
          mergedYaml.models[modelKey] = modelValue;
        });
        console.log(`  Merged models from ${sourceConfig.name}`);
      }
    } catch (error) {
      console.error(`Error processing ${sourceConfig.name}/nango.yaml:`, error);
    }
  }
});

console.log('YAML merge completed.');

// --- Step 1: Collect all unique provider directory names from sources ---
console.log('\nCollecting provider directory names from all sources...');
const allProviderDirs = new Set<string>();
SOURCE_DIRS_CONFIG.forEach(sourceConfig => {
  if (!fs.existsSync(sourceConfig.path)) {
    console.warn(
      `Skipping directory collection for ${sourceConfig.name}: Source directory not found.`
    );
    return;
  }
  try {
    const entries = fs.readdirSync(sourceConfig.path, { withFileTypes: true });
    entries.forEach(entry => {
      // Collect only directories, excluding hidden ones and specific files/dirs
      if (
        entry.isDirectory() &&
        !entry.name.startsWith('.') &&
        entry.name !== 'node_modules' &&
        entry.name !== 'dist'
      ) {
        allProviderDirs.add(entry.name);
      }
    });
  } catch (error) {
    console.error(`Error reading directories from ${sourceConfig.name}:`, error);
  }
});
console.log('Collected provider directories:', Array.from(allProviderDirs));

// --- Step 2: Delete these specific provider directories from the target ---
console.log(`\nDeleting existing provider directories in ${TARGET_DIR}...`);
allProviderDirs.forEach(dirName => {
  const destPath = path.join(TARGET_DIR, dirName);
  if (fs.existsSync(destPath)) {
    console.log(`  Deleting: ${destPath}`);
    try {
      fs.rmSync(destPath, { recursive: true, force: true });
    } catch (error) {
      console.error(`  Error deleting ${destPath}:`, error);
    }
  }
});
console.log('Deletion of target provider directories completed.');

// --- Step 3: Rsync files from each source ---
console.log(`\nStarting file sync to ${TARGET_DIR}...`);
SOURCE_DIRS_CONFIG.forEach(sourceConfig => {
  if (!fs.existsSync(sourceConfig.path)) {
    console.warn(`Skipping file sync for ${sourceConfig.name}: Source directory not found.`);
    return;
  }

  console.log(`Syncing implementation files from ${sourceConfig.name}...`);
  try {
    const entries = fs.readdirSync(sourceConfig.path, { withFileTypes: true });
    entries.forEach(entry => {
      // Sync only the provider directories we collected earlier
      if (entry.isDirectory() && allProviderDirs.has(entry.name)) {
        const providerName = entry.name;
        let srcImplPath: string;

        // Determine the correct source path for implementation files
        if (sourceConfig.name === 'emcpe-nango-integrations') {
          // New structure: <provider>/<provider>/
          srcImplPath = path.join(sourceConfig.path, providerName, providerName);
        } else {
          // Assume old structure for others: <provider>/
          srcImplPath = path.join(sourceConfig.path, providerName);
        }

        const destPath = path.join(TARGET_DIR, providerName);

        // Check if the specific implementation source path exists
        if (fs.existsSync(srcImplPath)) {
          // Ensure target parent directory exists
          fs.mkdirSync(TARGET_DIR, { recursive: true });

          // Use rsync without --delete, excluding config/generated files
          const command = `rsync -a --exclude='node_modules' --exclude='dist' --exclude='nango.yaml' --exclude='models.ts' --exclude='.env*' "${srcImplPath}/" "${destPath}/"`;
          console.log(`  Executing: ${command}`);
          execSync(command, { stdio: 'inherit' });
        } else {
          console.log(
            `  Skipping sync for ${providerName} from ${sourceConfig.name}: Implementation directory not found at ${srcImplPath}`
          );
        }
      }
    });
  } catch (error) {
    console.error(`Error syncing files from ${sourceConfig.name}:`, error);
  }
});
console.log('File sync completed.');

console.log(`\nWriting merged configuration to ${TARGET_YAML_PATH}...`);
try {
  fs.mkdirSync(TARGET_DIR, { recursive: true });

  const yamlOutput = yaml.dump(mergedYaml, {
    indent: 4,
  });
  fs.writeFileSync(TARGET_YAML_PATH, yamlOutput);
  console.log('Merged YAML written successfully.');
} catch (error) {
  console.error('Error writing merged YAML file:', error);
  process.exit(1);
}

// console.log('\nFormatting output YAML with Prettier...');
// try {
//   execSync(`npx prettier "${TARGET_YAML_PATH}" --write`, { stdio: 'inherit' }); // Restore execSync
//   console.log('Prettier formatting completed.');
// } catch (error) {
//   console.error('Error running Prettier:', error);
//   // Don't exit, formatting is optional
// }

console.log(`\n✅ Merge process complete. Target directory: ${TARGET_DIR}`);
console.log(`\nTo deploy to Nango, run:\ncd "${TARGET_DIR}" && npx @nangohq/cli deploy`);
