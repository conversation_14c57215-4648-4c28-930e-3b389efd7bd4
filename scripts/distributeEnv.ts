#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define types
type EnvSection = {
  paths: string[];
  vars: Record<string, string>;
};

// Main function to distribute environment variables
async function distributeEnv(exampleMode = false): Promise<void> {
  const rootDir = path.resolve(__dirname, '..');
  const sourceFile = path.join(rootDir, exampleMode ? '.env.example' : '.env');

  console.log(`Running in ${exampleMode ? 'EXAMPLE' : 'NORMAL'} mode`);
  console.log(`Reading from ${sourceFile}`);

  // Check if source file exists
  if (!fs.existsSync(sourceFile)) {
    console.error(`Source file ${sourceFile} does not exist`);
    process.exit(1);
  }

  // Read the source file
  const content = fs.readFileSync(sourceFile, 'utf8');

  // Parse the content into sections
  const sections = parseEnvFile(content);

  console.log(`Found ${sections.length} sections`);

  // Distribute the sections to their respective locations
  for (const section of sections) {
    await writeEnvSection(rootDir, section, exampleMode);
  }

  console.log('Environment variables distributed successfully');
}

// Parse the env file into sections
function parseEnvFile(content: string): EnvSection[] {
  const sections: EnvSection[] = [];
  const lines = content.split('\n');

  let currentSection: EnvSection | null = null;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Check for section header (with or without comment)
    if (line.match(/^#?---@/)) {
      // Extract path from section header
      const pathMatch = line.match(/^#?---@(.+)---/);

      if (pathMatch && pathMatch[1]) {
        // If we were already processing a section, add it to our list
        if (currentSection) {
          sections.push(currentSection);
        }

        // Split the path by commas to handle multiple workspaces
        const paths = pathMatch[1].split(',').map(p => p.trim());

        // Start a new section
        currentSection = {
          paths,
          vars: {},
        };
      }
    } // Check for separator line (with or without comment)
    else if (
      line === '---------------------------------' ||
      line === '#---------------------------------'
    ) {
      // If we were processing a section, add it to our list
      if (currentSection) {
        sections.push(currentSection);
        currentSection = null;
      }
    } // Process environment variable
    else if (line && !line.startsWith('#') && currentSection) {
      const parts = line.split('=');
      if (parts.length >= 2) {
        const key = parts[0].trim();
        const value = parts.slice(1).join('=').trim();
        currentSection.vars[key] = value;
      }
    }
  }

  // Add the last section if it exists
  if (currentSection) {
    sections.push(currentSection);
  }

  return sections;
}

// Write a section to its target location
async function writeEnvSection(
  rootDir: string,
  section: EnvSection,
  exampleMode: boolean
): Promise<void> {
  // For each path in the section
  for (const pathStr of section.paths) {
    // Skip empty paths
    if (!pathStr.trim()) continue;

    // Process the path to handle different formats
    let normalizedPath = pathStr;

    // Handle different path formats
    if (normalizedPath.includes('/')) {
      // For paths with slashes like 'makeagent/supabase/functions'
      // First extract the package name after any prefix
      if (normalizedPath.startsWith('@')) {
        normalizedPath = normalizedPath.substring(1);
      }

      // Remove 'makeagent/' prefix if present
      if (normalizedPath.startsWith('makeagent/')) {
        normalizedPath = normalizedPath.substring('makeagent/'.length);
      }
    } else {
      // For simple paths like '@makeagent/emcpe' or 'makeagent/emcpe'
      // Extract just the package name
      if (normalizedPath.startsWith('@')) {
        normalizedPath = normalizedPath.substring(1);
      }

      if (normalizedPath.includes('/')) {
        normalizedPath = normalizedPath.split('/').pop() || normalizedPath;
      }
    }

    // Determine the target directory
    const targetDir = path.join(rootDir, 'packages', normalizedPath);

    // Check if the target directory exists
    if (!fs.existsSync(targetDir)) {
      console.warn(`Target directory ${targetDir} does not exist, creating it...`);
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // Determine the target file name
    const targetFile = path.join(targetDir, exampleMode ? '.env.example' : '.env');

    console.log(`Writing to ${targetFile}`);

    // Create the env content
    let envContent = '';
    for (const [key, value] of Object.entries(section.vars)) {
      envContent += `${key}=${value}\n`;
    }

    // Write the file
    fs.writeFileSync(targetFile, envContent);

    // If the current target is the main emcpe-nango-integrations directory,
    // also write the same content to its provider subdirectories.
    if (targetDir.endsWith('emcpe-nango-integrations')) {
      console.log(`Distributing Nango keys to provider subdirs within ${targetDir}...`);
      try {
        const entries = fs.readdirSync(targetDir, { withFileTypes: true });
        const providerDirs = entries.filter(
          dirent =>
            dirent.isDirectory() &&
            (fs.existsSync(path.join(targetDir, dirent.name, dirent.name, 'actions')) ||
              fs.existsSync(path.join(targetDir, dirent.name, dirent.name, 'syncs')))
        );

        for (const providerDir of providerDirs) {
          const providerPath = path.join(targetDir, providerDir.name);
          const providerTargetFile = path.join(providerPath, exampleMode ? '.env.example' : '.env');
          try {
            fs.writeFileSync(providerTargetFile, envContent);
            console.log(`  -> Wrote Nango keys to ${providerTargetFile}`);
          } catch (writeErr) {
            console.error(`  -> Failed to write Nango keys to ${providerTargetFile}:`, writeErr);
          }
        }
      } catch (readErr) {
        console.error(`Error reading provider directories within ${targetDir}:`, readErr);
      }
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const exampleMode = args.includes('--example') || args.includes('-e');

// Run the main function
distributeEnv(exampleMode).catch(err => {
  console.error('Error distributing environment variables:', err);
  process.exit(1);
});
