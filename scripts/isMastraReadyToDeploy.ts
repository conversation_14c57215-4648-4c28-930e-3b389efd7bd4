import prompts from 'prompts';

async function confirmNangoRun(): Promise<void> {
  const response = await prompts({
    type: 'confirm',
    name: 'confirmed',
    message: 'Have you run scripts/nangoIntrospection.ts with a production NANGO_SECRET_KEY?',
    initial: false,
  });

  if (!response.confirmed) {
    console.error(
      'Error: You must run nangoIntrospection.ts with production NANGO_SECRET_KEY first.'
    );
    process.exit(1); // Block push
  }

  console.log('Confirmation received. Proceeding...');
  process.exit(0); // Allow push
}

// Run it
await confirmNangoRun();
