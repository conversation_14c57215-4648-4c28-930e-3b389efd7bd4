#!/bin/bash

# Check if .env exists
if [ ! -f ".env" ]; then
  echo "Error: .env file not found. Cannot determine current environment."
  exit 1
fi

# Case 1: .env is currently production, switching to development
# This means .env.development exists and .env.production does not.
if [ -f ".env.development" ] && [ ! -f ".env.production" ]; then
  echo "Current .env is (assumed) production. Switching to development..."
  mv .env .env.production
  mv .env.development .env
  echo ".env is now development."
  echo ".env.production now holds the previous production environment."
  exit 0
fi

# Case 2: .env is currently development, switching to production
# This means .env.production exists and .env.development does not.
if [ -f ".env.production" ] && [ ! -f ".env.development" ]; then
  echo "Current .env is (assumed) development. Switching to production..."
  mv .env .env.development
  mv .env.production .env
  echo ".env is now production."
  echo ".env.development now holds the previous development environment."
  exit 0
fi

# Ambiguous or unsupported states
if [ -f ".env.development" ] && [ -f ".env.production" ]; then
  echo "Error: Both .env.development and .env.production exist."
  echo "Please resolve this ambiguity before running the script."
  echo "  - If .env is currently development, delete .env.development."
  echo "  - If .env is currently production, delete .env.production."
  exit 1
fi

if [ ! -f ".env.development" ] && [ ! -f ".env.production" ]; then
  echo "Error: Neither .env.development nor .env.production found."
  echo "Cannot determine target environment to swap with."
  echo "Please ensure one of these files exists (e.g., .env.development or .env.production)."
  exit 1
fi

echo "Error: Could not determine current environment state. No action taken."
exit 1
