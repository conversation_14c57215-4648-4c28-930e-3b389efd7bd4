#!/usr/bin/env node
// This script copies protocol type definitions from backend (Deno) to frontend environment
// Handles path adjustments for TypeScript imports (removing .ts extension)

import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Absolute paths for source and target
const SOURCE_DIR = path.resolve(__dirname, '../packages/ma-next/supabase/functions/_protocol');
const TARGET_DIR = path.resolve(__dirname, '../packages/ma-next/src/types/protocol');

// Make sure target directory exists
if (!fs.existsSync(TARGET_DIR)) {
  fs.mkdirSync(TARGET_DIR, { recursive: true });
}

// Get all TypeScript files except vercel.ts (since it only belongs on backend)
const files = fs.readdirSync(SOURCE_DIR).filter(file => {
  return file.endsWith('.ts') && file !== 'vercel.ts';
});

console.log('Backend → Frontend: Copying protocol types...');

for (const file of files) {
  // Read the source file
  const sourceContent = fs.readFileSync(path.join(SOURCE_DIR, file), 'utf8');

  // For frontend, remove .ts extension from imports
  const frontendContent = sourceContent.replace(
    /from\s+['"]([\.\/][^'"]+)\.ts['"]/g,
    (match, importPath) => {
      return `from '${importPath}'`;
    }
  );

  // Write to the target file
  fs.writeFileSync(path.join(TARGET_DIR, file), frontendContent, 'utf8');
  console.log(`✓ ${file}`);
}

console.log('Backend → Frontend: Protocol types synced successfully.\n');

// Don't copy vercel.ts to frontend as it's backend-only
