#!/usr/bin/env node
// This script synchronizes protocol type definitions between frontend and backend (Deno)
// Can sync in either direction or both

import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Absolute paths for source and target - UPDATED FOR NEW LOCATION
const FRONTEND_DIR = path.resolve(__dirname, '../packages/ma-next/src/chat/protocol');
const BACKEND_DIR = path.resolve(__dirname, '../packages/ma-next/supabase/functions/_protocol');

// Command line arguments
const args = process.argv.slice(2);
const direction = args[0]?.toLowerCase();

// Ensure both directories exist
[FRONTEND_DIR, BACKEND_DIR].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

/**
 * Syncs files from source to target with the given transformation function
 */
function syncFiles(sourceDir, targetDir, transform, excludeFiles = []) {
  // Get all TypeScript files excluding any specified
  const files = fs.readdirSync(sourceDir).filter(file => {
    return file.endsWith('.ts') && !excludeFiles.includes(file);
  });

  for (const file of files) {
    // Read the source file
    const sourceContent = fs.readFileSync(path.join(sourceDir, file), 'utf8');

    // Apply transformation
    const transformedContent = transform(sourceContent);

    // Write to the target file
    fs.writeFileSync(path.join(targetDir, file), transformedContent, 'utf8');
    console.log(`✓ ${file}`);
  }
}

// Transform for frontend -> backend (add .ts to imports)
function toDenoImports(content) {
  return content.replace(/from\s+['"]([\.\/][^'"]+)['"]/g, (match, importPath) => {
    // Only add .ts if not already present
    if (!importPath.endsWith('.ts')) {
      return `from '${importPath}.ts'`;
    }
    return match;
  });
}

// Transform for backend -> frontend (remove .ts from imports)
function toTsImports(content) {
  return content.replace(/from\s+['"]([\.\/][^'"]+)\.ts['"]/g, (match, importPath) => {
    return `from '${importPath}'`;
  });
}

// Execute based on direction
if (direction === 'to-backend' || !direction) {
  console.log('Frontend → Backend: Copying protocol types...');
  syncFiles(FRONTEND_DIR, BACKEND_DIR, toDenoImports, [
    'vercel.ts',
    'chatProtocolEnqueuer.ts',
    'toolCallProcessor.ts',
    'vercelConverter.ts',
    'ui.ts',
  ]);
  console.log('Frontend → Backend: Protocol types synced successfully.\n');
}

if (direction === 'to-frontend' || !direction) {
  console.log('Backend → Frontend: Copying protocol types...');
  syncFiles(BACKEND_DIR, FRONTEND_DIR, toTsImports, [
    'vercel.ts',
    'chatProtocolEnqueuer.ts',
    'toolCallProcessor.ts',
    'vercelConverter.ts',
    'ui.ts',
  ]);
  console.log('Backend → Frontend: Protocol types synced successfully.\n');
}

if (direction && !['to-backend', 'to-frontend'].includes(direction)) {
  console.log(`
Usage: node syncProtocolTypes.js [direction]

Options:
  to-backend   Sync types from frontend to backend (Deno)
  to-frontend  Sync types from backend to frontend
  (no arg)     Sync in both directions
  `);
}
