{"tasks": {"dev": "deno run --allow-net --allow-read --allow-env main.ts"}, "importMap": "./import_map.json", "compilerOptions": {"lib": ["deno.window"], "strict": true}, "lint": {"files": {"include": ["packages/ma-next/supabase/functions/"]}, "rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-unused-vars", "no-explicit-any", "require-await"]}}, "fmt": {"files": {"include": ["packages/ma-next/supabase/functions/"]}, "options": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "singleQuote": false, "proseWrap": "preserve"}}}